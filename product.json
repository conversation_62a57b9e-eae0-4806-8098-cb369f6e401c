{"nameShort": "Flow", "nameLong": "Flow", "applicationName": "flow", "dataFolderName": ".flow", "win32MutexName": "flowditor", "licenseName": "MIT", "licenseUrl": "https://github.com/flowditor/flow/blob/main/LICENSE.txt", "serverLicenseUrl": "https://github.com/flowditor/flow/blob/main/LICENSE.txt", "serverGreeting": [], "serverLicense": [], "serverLicensePrompt": "", "serverApplicationName": "flow-server", "serverDataFolderName": ".flow-server", "tunnelApplicationName": "flow-tunnel", "win32DirName": "Flow", "win32NameVersion": "Flow", "win32RegValueName": "Floweditor", "win32x64AppId": "{{9D394D01-1728-45A7-B997-A6C82C5452C3}", "win32arm64AppId": "{{0668DD58-2BDE-4101-8CDA-40252DF8875D}", "win32x64UserAppId": "{{8BED5DC1-6C55-46E6-9FE6-18F7E6F7C7F1}", "win32arm64UserAppId": "{{F6C87466-BC82-4A8F-B0FF-18CA366BA4D8}", "win32AppUserModelId": "Flow.Editor", "win32ShellNameShort": "flow", "win32TunnelServiceMutex": "flow-tunnelservice", "win32TunnelMutex": "flow-tunnel", "darwinBundleIdentifier": "com.floweditor.code", "linuxIconName": "flow-editor", "licenseFileName": "LICENSE.txt", "reportIssueUrl": "https://github.com/floweditor/flow/issues/new", "nodejsRepository": "https://nodejs.org", "urlProtocol": "flow-editor", "downloadUrl": "https://artxa.zte.com.cn/artifactory/rjgctd-local-release-generic/ai-ide", "extensionsGallery": {"serviceUrl": "https://open-vsx.org/vscode/gallery", "itemUrl": "https://open-vsx.org/vscode/item"}, "builtInExtensions": [{"name": "ms-vscode.js-debug-companion", "version": "1.1.3", "sha256": "7380a890787452f14b2db7835dfa94de538caf358ebc263f9d46dd68ac52de93", "repo": "https://github.com/microsoft/vscode-js-debug-companion", "metadata": {"id": "99cb0b7f-7354-4278-b8da-6cc79972169d", "publisherId": {"publisherId": "5f5636e7-69ed-4afe-b5d6-8d231fb3d3ee", "publisherName": "ms-vscode", "displayName": "Microsoft", "flags": "verified"}, "publisherDisplayName": "Microsoft"}, "vsix": ".build/builtInExtensions/ms-vscode.js-debug-companion.1.1.3.vsix"}, {"name": "ms-vscode.js-debug", "version": "1.97.1", "sha256": "977dd854805547702e312e176f68a1b142fa123f228258f47f0964560ad32496", "repo": "https://github.com/microsoft/vscode-js-debug", "metadata": {"id": "25629058-ddac-4e17-abba-74678e126c5d", "publisherId": {"publisherId": "5f5636e7-69ed-4afe-b5d6-8d231fb3d3ee", "publisherName": "ms-vscode", "displayName": "Microsoft", "flags": "verified"}, "publisherDisplayName": "Microsoft"}, "vsix": ".build/builtInExtensions/ms-vscode.js-debug.1.97.1.vsix"}, {"name": "ms-vscode.vscode-js-profile-table", "version": "1.0.10", "sha256": "7361748ddf9fd09d8a2ed1f2a2d7376a2cf9aae708692820b799708385c38e08", "repo": "https://github.com/microsoft/vscode-js-profile-visualizer", "metadata": {"id": "7e52b41b-71ad-457b-ab7e-0620f1fc4feb", "publisherId": {"publisherId": "5f5636e7-69ed-4afe-b5d6-8d231fb3d3ee", "publisherName": "ms-vscode", "displayName": "Microsoft", "flags": "verified"}, "publisherDisplayName": "Microsoft"}, "vsix": ".build/builtInExtensions/ms-vscode.vscode-js-profile-table.1.0.10.vsix"}]}