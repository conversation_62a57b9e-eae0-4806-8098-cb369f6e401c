#!/bin/bash

# 严格的错误处理
set -euo pipefail

# 颜色定义
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly NC='\033[0m' # No Color

# 错误处理函数
error_exit() {
    echo -e "${RED}错误: $1${NC}" >&2
    exit 1
}

# 日志函数
log() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] $*"
}

log_success() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] $*${NC}"
}

log_warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] $*${NC}"
}

# 检查必要工具
check_tools() {
    log "检查必要工具..."
    local tools=("rpmspec" "rpmbuild" "tar" "unzip" "grep" "cut" "awk")
    for tool in "${tools[@]}"; do
        command -v "$tool" >/dev/null 2>&1 || error_exit "缺少必要工具: $tool"
    done
    log_success "所有必要工具检查通过"
}

# 验证版本号格式
validate_version() {
    if [[ ! "$VERSION" =~ ^[0-9]+\.[0-9]+\.[0-9]+([.-][a-zA-Z0-9]+)?$ ]]; then
        error_exit "版本号格式无效: $VERSION (期望格式: x.y.z 或 x.y.z-suffix)"
    fi
}

# 提取版本号
extract_version() {
    local package_json="$ROOT_DIR/package.json"
    log "从 package.json 提取版本号..."

    if command -v jq >/dev/null 2>&1; then
        VERSION=$(jq -r '.productVersion' "$package_json" 2>/dev/null) || \
            error_exit "无法从package.json提取版本号"
    else
        VERSION=$(grep -o '"productVersion"[[:space:]]*:[[:space:]]*"[^"]*"' "$package_json" | \
                  cut -d'"' -f4) || \
            error_exit "无法提取版本号，请检查package.json格式"
    fi

    [[ -n "$VERSION" ]] || error_exit "版本号为空"
    validate_version
    log_success "提取到版本号: $VERSION"
}

# 验证必要文件存在
verify_required_files() {
    log "验证必需文件..."
    local required_files=(
        "$ROOT_DIR/package.json"
        "$ROOT_DIR/resources/linux/rpm/flow.spec"
        "$ROOT_DIR/resources/linux/flow.desktop"
        "$ROOT_DIR/resources/linux/code.png"
        "$ROOT_DIR/resources/completions/argv.json"
    )

    for file in "${required_files[@]}"; do
        [[ -f "$file" ]] || error_exit "必需文件不存在: $file"
    done

    [[ -d "$VSCODE_DIR" ]] || error_exit "VSCode目录不存在: $VSCODE_DIR"
    log_success "所有必需文件验证通过"
}

# 初始化RPM构建环境
init_rpmbuild_env() {
    log "初始化RPM构建环境..."
    if [[ ! -d "$RPMBUILD_DIR" ]]; then
        mkdir -p "$RPMBUILD_DIR"/{BUILD,RPMS,SOURCES,SPECS,SRPMS}
        log_success "创建RPM构建目录: $RPMBUILD_DIR"
    else
        log "RPM构建目录已存在: $RPMBUILD_DIR"
    fi
}

# 准备spec文件
prepare_spec_file() {
    log "准备spec文件..."
    local spec_source="$ROOT_DIR/resources/linux/rpm/flow.spec"
    local spec_dest="$RPMBUILD_DIR/SPECS/flow.spec"

    cp "$spec_source" "$spec_dest" || error_exit "复制spec文件失败"

    # 更新版本号
    sed -i "s/^Version:.*$/Version:        $VERSION/" "$spec_dest" || \
        error_exit "更新spec文件版本号失败"

    log_success "spec文件准备完成，版本号已更新为: $VERSION"
}

# 复制文件（如果存在）
copy_if_exists() {
    local src="$1"
    local dest="$2"

    if [[ -f "$src" ]]; then
        cp "$src" "$dest" || error_exit "复制文件失败: $src -> $dest"
        log "已复制: $(basename "$src")"
    else
        log_warning "文件不存在，跳过: $src"
    fi
}

# 解压文件（如果存在）
extract_if_exists() {
    local zip_file="$1"
    local dest_dir="$2"

    if [[ -f "$zip_file" ]]; then
        mkdir -p "$dest_dir"
        unzip -o -j "$zip_file" -d "$dest_dir" >/dev/null 2>&1 || \
            error_exit "解压失败: $zip_file"
        log "已解压: $(basename "$zip_file")"
    else
        log_warning "压缩文件不存在，跳过: $zip_file"
    fi
}

# 复制依赖的二进制文件
copy_native_dependencies() {
    log "复制本地依赖文件..."

    # 检查并复制sqlite3.node
    if [[ -f "$HOME/.cache/vscode-sqlite3.node" ]]; then
        local sqlite_dest="$VSCODE_DIR/resources/app/node_modules/@vscode/sqlite3/build/Release/"
        mkdir -p "$sqlite_dest"
        cp "$HOME/.cache/vscode-sqlite3.node" "$sqlite_dest" || \
            log_warning "复制vscode-sqlite3.node失败"
        log "已复制: vscode-sqlite3.node"
    fi

    # 检查并复制spdlog.node
    if [[ -f "$HOME/.cache/spdlog.node" ]]; then
        local spdlog_dest="$VSCODE_DIR/resources/app/node_modules/@vscode/spdlog/build/Release/"
        mkdir -p "$spdlog_dest"
        cp "$HOME/.cache/spdlog.node" "$spdlog_dest" || \
            log_warning "复制spdlog.node失败"
        log "已复制: spdlog.node"
    fi
}

# 创建源码包
create_source_package() {
    log "创建源码包..."

    cd "$VSCODE_DIR" || error_exit "无法进入VSCode目录"

    copy_native_dependencies

    # 获取包名
    local package_name
    package_name=$(rpmspec -P "$RPMBUILD_DIR/SPECS/flow.spec" | awk '/^Source0:/ {print $2}') || \
        error_exit "无法获取源码包名"

    # 创建压缩包
    tar czf "$RPMBUILD_DIR/SOURCES/$package_name" . || \
        error_exit "创建源码包失败"

    cd "$SCRIPT_DIR" || error_exit "无法返回脚本目录"
    log_success "源码包创建完成: $package_name"
}

# 复制资源文件
copy_resources() {
    log "复制资源文件..."
    local sources_dir="$RPMBUILD_DIR/SOURCES"

    # 复制基本文件
    copy_if_exists "$ROOT_DIR/resources/linux/flow.desktop" "$sources_dir"
    copy_if_exists "$ROOT_DIR/resources/linux/code.png" "$sources_dir"
    copy_if_exists "$ROOT_DIR/resources/completions/argv.json" "$sources_dir"
    copy_if_exists "$ROOT_DIR/resources/ctags/ctags" "$sources_dir"
    copy_if_exists "$ROOT_DIR/resources/clangd/clangd" "$sources_dir"
	copy_if_exists "$ROOT_DIR/resources/codebase/codebase" "$sources_dir"

    # 处理压缩文件
    extract_if_exists "$ROOT_DIR/resources/symf/symf-x86_64-linux-musl.zip" "$sources_dir"

    log_success "资源文件复制完成"
}

# 设置文件权限
set_permissions() {
    log "设置文件权限..."
    chmod +x "$RPMBUILD_DIR/SOURCES"/* 2>/dev/null || true
    log_success "文件权限设置完成"
}

# 构建RPM包
build_rpm() {
    log "开始构建RPM包..."
    rpmbuild -bb --nocheck "$RPMBUILD_DIR/SPECS/flow.spec" || \
        error_exit "RPM构建失败"
    log_success "RPM包构建完成"
}

# 收集和打包结果
collect_results() {
    log "收集构建结果..."

    local rpm_pattern="$RPMBUILD_DIR/RPMS/x86_64/flow-*.rpm"
    local rpm_files=($rpm_pattern)

    if [[ ! -e "${rpm_files[0]}" ]]; then
        error_exit "未找到RPM包文件: $rpm_pattern"
    fi

    # 创建输出目录
    local output_dir="$ROOT_DIR/.build/rpm"
    mkdir -p "$output_dir"
    rm -f "$output_dir"/flow-*.rpm "$output_dir"/flow-*.tar.gz

    # 复制RPM文件
    cp "${rpm_files[@]}" "$output_dir" || error_exit "复制RPM文件失败"

    # 创建压缩包
    cd "$output_dir" || error_exit "无法进入输出目录"
    tar -czvf "flow-${VERSION}.tar.gz" flow-${VERSION}*.rpm >/dev/null || \
        error_exit "创建压缩包失败"

    cd "$SCRIPT_DIR" || error_exit "无法返回脚本目录"

    log_success "构建结果已保存到: $output_dir"
    log_success "RPM包: $(ls -1 "$output_dir"/flow-*.rpm | xargs basename)"
    log_success "压缩包: flow-${VERSION}.tar.gz"
}

# 清理函数
cleanup() {
    if [[ -n "${TEMP_DIR:-}" && -d "$TEMP_DIR" ]]; then
        rm -rf "$TEMP_DIR"
    fi
}

# 主函数
main() {
    log "开始Flow RPM包构建流程..."

    # 设置清理陷阱
    trap cleanup EXIT

    # 创建临时目录
    TEMP_DIR=$(mktemp -d)

    # 执行构建步骤
    check_tools
    verify_required_files
    extract_version
    init_rpmbuild_env
    prepare_spec_file
    create_source_package
    copy_resources
    set_permissions
    build_rpm
    collect_results

    log_success "Flow RPM包构建完成!"
}

# 全局变量定义
readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
readonly ROOT_DIR="$(cd "$SCRIPT_DIR/.." && pwd)"
readonly VSCODE_DIR="$ROOT_DIR/../VSCode-linux-x64"
readonly RPMBUILD_DIR="${RPMBUILD_DIR:-$HOME/rpmbuild}"

# 版本变量（将在extract_version中设置）
VERSION=""

# 执行主函数
main "$@"



