%global debug_package %{nil}
%global _use_internal_dependency_generator 0
%global _build_id_links none
%global _missing_build_ids_terminate_build 0
AutoReqProv: no

%define DesktopFile %{SOURCE1}
%define IconFile %{SOURCE2}
%define SymfTool %{SOURCE3}
%define ArgvConfig %{SOURCE4}
%define CtagsTool %{SOURCE5}
%define ClangdTool %{SOURCE6}
%define CodebaseTool %{SOURCE7}

Name:           flow
Version:        0.0.1
Release:        1%{?dist}
Summary:        Flow IDE - 智能化代码编辑器
License:        MIT
Group:          Development/Tools
Vendor:         ZTE
URL:            https://github.com/zte/flow-ide
Source0:        flow-linux-x64.tar.gz
Source1:        flow.desktop
Source2:        code.png
Source3:        symf-x86_64-linux-musl
Source4:        argv.json
Source5:        ctags
Source6:        clangd
Source7:        codebase

# 系统依赖
Requires:       gtk3 >= 3.10
Requires:       libX11 >= 1.6
Requires:       libXtst >= 1.2
Requires:       libxkbfile >= 1.0
Requires:       libsecret >= 0.18
Requires:       alsa-lib
Requires:       nss
Requires:       at-spi2-atk


%description
Flow IDE 是一个基于 VSCode 的智能化代码编辑器，专为开发者提供高效的编程体验。
它集成了多种开发工具和AI助手，支持多种编程语言和框架。

特性包括：
- 智能代码补全和语法高亮
- 集成调试器和版本控制
- 丰富的插件生态系统
- AI代码助手

%prep
%setup -c -n flow-linux-x64

%build
# 预编译包，无需构建

%install
# 创建安装目录
mkdir -p %{buildroot}/opt/flow
mkdir -p %{buildroot}/usr/bin
mkdir -p %{buildroot}%{_datadir}/applications
mkdir -p %{buildroot}%{_datadir}/icons/hicolor/{16,32,48,64,128,256}x{16,32,48,64,128,256}/apps

# 创建系统级工具目录模板（使用 /etc/skel）
mkdir -p %{buildroot}/etc/skel/.flow/{symf,ctags,clangd,codebase}/bin

# 复制主程序文件
cp -r * %{buildroot}/opt/flow/

# 复制工具文件到 skel 模板
cp %{SymfTool} %{buildroot}/etc/skel/.flow/symf/bin/
cp %{ArgvConfig} %{buildroot}/etc/skel/.flow/
cp %{CtagsTool} %{buildroot}/etc/skel/.flow/ctags/bin/
cp %{ClangdTool} %{buildroot}/etc/skel/.flow/clangd/bin/
cp %{CodebaseTool} %{buildroot}/etc/skel/.flow/codebase/bin/

# 创建工具版本文件，用于跟踪版本变化
cat > %{buildroot}/etc/skel/.flow/.tools_version << 'EOF'
FLOW_TOOLS_VERSION=%{version}-%{release}
SYMF_TIMESTAMP=$(stat -c %Y %{SymfTool} 2>/dev/null || echo 0)
CTAGS_TIMESTAMP=$(stat -c %Y %{CtagsTool} 2>/dev/null || echo 0)
CLANGD_TIMESTAMP=$(stat -c %Y %{ClangdTool} 2>/dev/null || echo 0)
CODEBASE_TIMESTAMP=$(stat -c %Y %{CodebaseTool} 2>/dev/null || echo 0)
ARGV_TIMESTAMP=$(stat -c %Y %{ArgvConfig} 2>/dev/null || echo 0)
EOF

# 设置 skel 模板文件权限
chmod +x %{buildroot}/etc/skel/.flow/*/bin/*
chmod 644 %{buildroot}/etc/skel/.flow/.tools_version

# 确保所有用户都能读取skel模板文件
chmod -R o+r %{buildroot}/etc/skel/.flow/
find %{buildroot}/etc/skel/.flow/ -type d -exec chmod o+rx {} \;
find %{buildroot}/etc/skel/.flow/ -name "*.json" -exec chmod 644 {} \;

# 创建优化的启动包装脚本
cat > %{buildroot}/usr/bin/flow << 'EOF'
#!/bin/bash

# Flow IDE 启动脚本
#
# 环境变量：
#   FLOW_DEBUG=1          - 启用调试日志
#   FLOW_FORCE_UPDATE=1   - 强制更新工具文件
#
# 使用示例：
#   flow                           - 正常启动
#   FLOW_DEBUG=1 flow             - 启用调试模式
#   FLOW_FORCE_UPDATE=1 flow      - 强制更新工具后启动

# 设置错误处理和调试
set -e

# 定义常量
readonly FLOW_HOME="${HOME}/.flow"
readonly FLOW_INSTALL_DIR="/opt/flow"
readonly FLOW_CONFIG_DIR="${XDG_CONFIG_HOME:-$HOME/.config}/Flow"

# 日志函数
log_info() {
    [[ "${FLOW_DEBUG:-}" == "1" ]] && echo "[Flow] $*" >&2 || true
}

log_error() {
    echo "[Flow Error] $*" >&2 || true
}

# 初始化用户工具目录（处理首次运行和升级情况）
init_user_tools() {
    local need_update=false

    # 检查是否强制更新
    if [[ "${FLOW_FORCE_UPDATE:-}" == "1" ]]; then
        log_info "检测到强制更新标志，将重新初始化工具..."
        rm -rf "$FLOW_HOME" 2>/dev/null || true
        need_update=true
    elif [[ ! -d "$FLOW_HOME" ]]; then
        log_info "首次运行，初始化用户工具目录..."
        need_update=true
    else
        # 检查是否需要更新工具
        local skel_version_file="/etc/skel/.flow/.tools_version"
        local user_version_file="$FLOW_HOME/.tools_version"

        if [[ -f "$skel_version_file" ]]; then
            if [[ ! -f "$user_version_file" ]]; then
                log_info "检测到工具版本文件缺失，更新工具..."
                need_update=true
            else
                # 比较版本
                local skel_tools_version=""
                local user_tools_version=""

                # 安全地读取版本文件
                if source "$skel_version_file" 2>/dev/null; then
                    skel_tools_version="$FLOW_TOOLS_VERSION"
                fi

                if source "$user_version_file" 2>/dev/null; then
                    user_tools_version="$FLOW_TOOLS_VERSION"
                fi

                if [[ "$skel_tools_version" != "$user_tools_version" ]]; then
                    log_info "检测到工具版本更新 ($user_tools_version -> $skel_tools_version)，更新工具..."
                    need_update=true
                fi
            fi
        fi
    fi

    if [[ "$need_update" == "true" ]]; then
        # 从 skel 复制模板
        if [[ -d "/etc/skel/.flow" ]]; then
            # 备份现有配置（如果有的话）
            if [[ -f "$FLOW_HOME/argv.json" ]]; then
                cp "$FLOW_HOME/argv.json" "$FLOW_HOME/argv.json.backup.$(date +%s)" 2>/dev/null || true
                log_info "已备份现有配置文件"
            fi

            # 确保目标目录存在
            mkdir -p "$FLOW_HOME"

            # 递归复制整个目录结构
            if ! cp -rf "/etc/skel/.flow/"* "$FLOW_HOME/" 2>&1; then
                log_error "复制工具文件失败，检查 /etc/skel/.flow/ 目录"
                # 显示调试信息
                if [[ "${FLOW_DEBUG:-}" == "1" ]]; then
                    ls -la "/etc/skel/.flow/" || true
                fi
                exit 1
            fi

            # 复制版本文件
            cp "/etc/skel/.flow/.tools_version" "$FLOW_HOME/" 2>/dev/null || true

            # 设置权限
            chmod +x "$FLOW_HOME"/*/bin/* 2>/dev/null || true

            log_info "工具文件更新完成"
        else
            log_error "系统模板目录不存在，Flow 可能安装不完整"
            exit 1
        fi

        mkdir -p "$FLOW_CONFIG_DIR"
    fi
}

# 设置环境变量
setup_environment() {
    local tools_path="$FLOW_HOME/symf/bin:$FLOW_HOME/ctags/bin:$FLOW_HOME/clangd/bin:$FLOW_HOME/codebase/bin"

    export FLOW_USER_HOME="$FLOW_HOME"

    if [[ ":$PATH:" != *":$tools_path:"* ]]; then
        export PATH="$tools_path:$PATH"
    fi
}

# 修复扩展目录权限
fix_extension_permissions() {
    local ext_dir="$FLOW_INSTALL_DIR/resources/app/extensions"

    if [[ -d "$ext_dir" ]]; then
        sudo chown -R $(id -u):$(id -g) "$ext_dir"
    fi
}

# 主函数
main() {
    # 检查 Flow 安装目录
    if [[ ! -d "$FLOW_INSTALL_DIR" ]]; then
        log_error "Flow 安装目录不存在: $FLOW_INSTALL_DIR"
        exit 1
    fi

    # 智能检测主程序路径
    local flow_executable=""
    if [[ -x "$FLOW_INSTALL_DIR/bin/flow" ]]; then
        flow_executable="$FLOW_INSTALL_DIR/bin/flow"
    elif [[ -x "$FLOW_INSTALL_DIR/flow" ]]; then
        flow_executable="$FLOW_INSTALL_DIR/flow"
    else
        log_error "Flow 主程序不存在，检查路径："
        log_error "- $FLOW_INSTALL_DIR/bin/flow"
        log_error "- $FLOW_INSTALL_DIR/flow"
        exit 1
    fi

	log_info "找到 Flow 主程序: $flow_executable"

    # 初始化用户工具目录
    init_user_tools

    # 设置环境变量
    setup_environment

    # 检查扩展权限
    fix_extension_permissions

    # 启动主程序
    log_info "启动 Flow IDE..."
    exec "$flow_executable" --password-store=basic "$@"
}

main "$@"
EOF

# 设置启动脚本权限
chmod +x %{buildroot}/usr/bin/flow

# 复制桌面文件
cp %{DesktopFile} %{buildroot}%{_datadir}/applications/

# 复制图标文件到不同尺寸目录
for size in 16 32 48 64 128 256; do
    cp %{IconFile} %{buildroot}%{_datadir}/icons/hicolor/${size}x${size}/apps/
done

# 设置桌面和图标文件权限
chmod 644 %{buildroot}%{_datadir}/applications/flow.desktop
find %{buildroot}%{_datadir}/icons -name "code.png" -exec chmod 644 {} \;

%files
# 主程序目录
/opt/flow
%attr(755,root,root) /usr/bin/flow

# 用户工具模板 - 确保所有用户都能读取
%defattr(644,root,root,755)
/etc/skel/.flow

# 桌面集成文件
%attr(644,root,root) %{_datadir}/applications/flow.desktop

# 图标文件
%attr(644,root,root) %{_datadir}/icons/hicolor/16x16/apps/code.png
%attr(644,root,root) %{_datadir}/icons/hicolor/32x32/apps/code.png
%attr(644,root,root) %{_datadir}/icons/hicolor/48x48/apps/code.png
%attr(644,root,root) %{_datadir}/icons/hicolor/64x64/apps/code.png
%attr(644,root,root) %{_datadir}/icons/hicolor/128x128/apps/code.png
%attr(644,root,root) %{_datadir}/icons/hicolor/256x256/apps/code.png

%pre
# 安装前脚本 - 检查系统要求
if [[ ! -f /usr/lib64/libgtk-3.so.0 ]] && [[ ! -f /usr/lib/x86_64-linux-gnu/libgtk-3.so.0 ]]; then
    echo "警告: 系统可能缺少 GTK3 库，Flow 可能无法正常运行"
fi

%post
# 为现有用户初始化工具目录
echo "正在为现有用户初始化 Flow 工具..."
for user_home in /home/<USER>
    if [[ -d "$user_home" && -r "$user_home" ]]; then
        username=$(basename "$user_home")
        if id "$username" >/dev/null 2>&1 && [[ ! -d "$user_home/.flow" ]]; then
            # 使用 sudo 以用户身份复制文件
            sudo -u "$username" cp -r /etc/skel/.flow "$user_home/" 2>/dev/null || {
                echo "警告: 无法为用户 $username 初始化 Flow 工具"
            }
        fi
    fi
done

# 更新桌面数据库
if command -v update-desktop-database >/dev/null 2>&1; then
    update-desktop-database %{_datadir}/applications &> /dev/null || true
fi

# 更新图标缓存
if command -v gtk-update-icon-cache >/dev/null 2>&1; then
    gtk-update-icon-cache --quiet %{_datadir}/icons/hicolor &> /dev/null || true
fi

# 创建系统级符号链接（可选）
if [[ ! -e /usr/local/bin/flow ]] && [[ -w /usr/local/bin ]]; then
    ln -sf /usr/bin/flow /usr/local/bin/flow 2>/dev/null || true
fi

echo "Flow IDE 安装完成！"
echo "使用 'flow' 命令启动，或从应用程序菜单中启动。"

%preun
# 卸载前脚本
if [[ $1 -eq 0 ]]; then
    # 完全卸载时，清理符号链接
    [[ -L /usr/local/bin/flow ]] && rm -f /usr/local/bin/flow 2>/dev/null || true

	# 清除用户工具模板
	rm -rf /etc/skel/.flow
fi

%postun
# 卸载后更新桌面数据库和图标缓存
if [[ $1 -eq 0 ]]; then
    if command -v update-desktop-database >/dev/null 2>&1; then
        update-desktop-database %{_datadir}/applications &> /dev/null || true
    fi

    if command -v gtk-update-icon-cache >/dev/null 2>&1; then
        gtk-update-icon-cache --quiet %{_datadir}/icons/hicolor &> /dev/null || true
    fi

    echo "Flow IDE 已完全卸载。"
    echo "用户配置文件保留在 ~/.flow 目录中，如需完全清理请手动删除。"
fi

%changelog
* Mon Mar 10 2025 Flow Team <<EMAIL>> - %{version}
- 初始 RPM 包发布
- 集成开发工具：symf, ctags, clangd, codebase
- 添加桌面环境集成
- 使用 /etc/skel 模板自动为用户初始化工具
- 优化启动脚本，减少权限依赖
- 改进用户体验和错误处理
