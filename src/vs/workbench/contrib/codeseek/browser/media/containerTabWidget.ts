import { IAction } from '../../../../../base/common/actions.js';
import { IChatThreadService } from '.././chatThreadType.js';
import { Disposable } from '../../../../../base/common/lifecycle.js';
import { CODESEEK_NEW_CHAT_ACTION_ID, CODESEEK_VIEW_CONTAINER_ID } from '../actionIDs.js';
import { ICommandService } from '../../../../../platform/commands/common/commands.js';
import { ICompositeBarActionViewItemOptions } from '../../../../../workbench/browser/parts/compositeBarActions.js';
import { IHoverService } from '../../../../../platform/hover/browser/hover.js';

type ChatBarSuiteType = {
	action: IAction;
	container: HTMLElement;
	label: HTMLElement;
	badge: HTMLElement;
	badgeContent: HTMLElement;
	active?: boolean;
}

const activeContainerColor = 'color-mix(in srgb, var(--vscode-list-inactiveSelectionBackground) 80%, blue)';
const inactiveContainerColor = 'color-mix(in srgb, var(--vscode-list-inactiveSelectionBackground) 90%, gray)';

export class ContainerTabWidget extends Disposable {
	private chatBarSuite: Record<string, ChatBarSuiteType> = {};

	constructor(
		private readonly action: IAction,
		private readonly options: ICompositeBarActionViewItemOptions,
		@IChatThreadService private readonly _chatThreadService: IChatThreadService,
		@ICommandService private readonly _commandService: ICommandService,
		@IHoverService private readonly _hoverService: IHoverService
	) {
		super();

		this._register(this._chatThreadService.onDidSetChatTitle(({ containerId, message }) => {
			const { container, label } = this.chatBarSuite[containerId];
			if (label) {
				container.style.justifyContent = 'flex-start';
				container.style.padding = '1px 8px 0px 8px';
				label.style.justifyContent = 'flex-start';
				label.textContent = message;
				this.updateHoverContent(container, message);
				label.setAttribute('aria-label', message);
				container.setAttribute('aria-label', message);
			}
		}));

		this._register(this._chatThreadService.onDidChangeCurrentContainer(() => {
			const currentContainerId = this._chatThreadService.getCurrentContainerId();
			Object.keys(this.chatBarSuite).forEach(id => {
				this.chatBarSuite[id].active = id === currentContainerId;
				this.updateContainerActiveState(this.chatBarSuite[id]);
			});
		}));
	}

	private updateContainerActiveState(suite: ChatBarSuiteType): void {
		if (suite.active) {
			suite.container.style.backgroundColor = activeContainerColor;
			suite.container.style.border = 'none';
		} else {
			suite.container.style.backgroundColor = inactiveContainerColor;
			suite.container.style.border = 'none';
		}
	}

	private updateHoverContent(container: HTMLElement, title: string): void {
		this._register(this._hoverService.setupDelayedHover(container, () => ({
			content: title,
			position: {
				hoverPosition: this.options.hoverOptions.position(),
			},
			persistence: {
				hideOnKeyDown: true,
			},
			appearance: {
				showPointer: true,
				compact: true,
			}
		}), { groupId: 'composite-bar-actions' }));
	}

	render({ container, label, badge, badgeContent }: { container: HTMLElement, label: HTMLElement, badge: HTMLElement, badgeContent: HTMLElement }): void {
		const isActiveContainer = this._chatThreadService.getCurrentContainerId() === this.action.id;

		this.chatBarSuite[this.action.id] = {
			action: this.action,
			container: container,
			label: label,
			badge: badge,
			badgeContent: badgeContent,
			active: isActiveContainer
		};
		const containerCloseButton = this.createContainerCloseButton();
		container.appendChild(containerCloseButton);
		const containerStyle = {
			display: 'flex',
			alignItems: 'center',
			justifyContent: 'center',
			backgroundColor: isActiveContainer ? activeContainerColor : inactiveContainerColor,
			cursor: 'pointer',
			maxWidth: '120px',
			flex: '0 1 auto',
			height: '22px',
			borderRadius: '4px',
			padding: '1px 8px 0px 8px',
			marginRight: '4px',
			marginTop: '2px',
			position: 'relative',
			border: 'none',
		}
		Object.assign(container.style, containerStyle);
		container.removeAttribute('class');
		label.removeAttribute('class');
		const style = {
			display: 'flex',
			alignItems: 'center',
			justifyContent: 'center',
			fontSize: '12px',
			whiteSpace: 'nowrap',
			overflow: 'hidden',
			textOverflow: 'ellipsis',
			maxWidth: '80px',
			flex: '0 1 auto',
			height: '100%',
			color: 'var(--vscode-list-activeSelectionForeground)',
		}
		Object.assign(label.style, style);
		label.textContent = "New Chat";
		label.setAttribute('aria-label', 'New Chat');
		label.addEventListener('mouseenter', () => {
			label.style.backgroundColor = this.chatBarSuite[this.action.id].active ? activeContainerColor : inactiveContainerColor;
		});
		label.addEventListener('mouseleave', () => {
			label.style.backgroundColor = this.chatBarSuite[this.action.id].active ? activeContainerColor : inactiveContainerColor;
		});

		container.addEventListener('mouseenter', () => {
			container.style.backgroundColor = this.chatBarSuite[this.action.id].active ? activeContainerColor : inactiveContainerColor;
			containerCloseButton.style.visibility = 'visible';
		});

		container.addEventListener('mouseleave', () => {
			container.style.backgroundColor = this.chatBarSuite[this.action.id].active ? activeContainerColor : inactiveContainerColor;
			containerCloseButton.style.visibility = 'hidden';
		});
	}

	private createContainerCloseButton(): HTMLElement {
		const containerCloseButton = document.createElement("div");
		containerCloseButton.className = "codicon codicon-close";
		const style = {
			display: 'flex',
			alignItems: 'center',
			justifyContent: 'center',
			position: 'absolute',
			right: '0px',
			top: '0px',
			fontSize: '13px',
			cursor: 'pointer',
			zIndex: '2',
			visibility: 'hidden',
		}
		Object.assign(containerCloseButton.style, style);
		containerCloseButton.addEventListener("click", () => {
			if (this.action.id === CODESEEK_VIEW_CONTAINER_ID) {
				this._commandService.executeCommand(CODESEEK_NEW_CHAT_ACTION_ID);
			} else {
				this._chatThreadService.deleteContainer(this.action.id);
				delete this.chatBarSuite[this.action.id];
			}
		});

		containerCloseButton.addEventListener('mouseenter', () => {
			containerCloseButton.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
		});
		containerCloseButton.addEventListener('mouseleave', () => {
			containerCloseButton.style.backgroundColor = 'transparent';
		});

		return containerCloseButton;
	}
}

