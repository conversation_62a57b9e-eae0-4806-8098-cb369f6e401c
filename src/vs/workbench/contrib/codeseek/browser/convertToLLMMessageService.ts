import { createDecorator } from '../../../../platform/instantiation/common/instantiation.js';
import { InstantiationType, registerSingleton } from '../../../../platform/instantiation/common/extensions.js';
import { Disposable } from '../../../../base/common/lifecycle.js';
import { SendLLMType } from '../common/llmMessageTypes.js';
import { userMessageOpts } from './chatThreadType.js';
import { Step } from './todolistService.js';
import { code_agent_systemMessage, enhancementSystemMessage, enhancementUserMessage, systemToolsXMLPrompt, user_rules } from '../common/prompt/prompts.js';
import { codeseekTools, InternalToolInfo, ToolNameEnum } from '../common/toolsServiceTypes.js';
import { ChatMode, ICodeseekSettingsService } from '../common/codeseekSettingsService.js';

export type CodeAgentConvertMessageOpts = {
	messagesType: 'codeAgentMessages';
	containerId: string;
	threadId: string;
	task: string;
	taskContext: string;
	step: Step;
}

export type ChatAgenConverttMessageOpts = {
	messagesType: 'chatAgentMessages';
	containerId: string;
	threadId: string;
	userMessage: string;
	chatHistory: string;
	directoryStrDetail: string;
	workingDirectory: string;
	openTabs: string;
}

export type ConvertMessagesOpts = CodeAgentConvertMessageOpts | ChatAgenConverttMessageOpts;

export interface IConvertToLLMMessageService {
	readonly _serviceBrand: undefined;

	prepareLLMChatMessages(opts: ConvertMessagesOpts): SendLLMType;
}

export const IConvertToLLMMessageService = createDecorator<IConvertToLLMMessageService>('codeseekConvertToLLMMessageService');
export class ConvertToLLMMessageService extends Disposable implements IConvertToLLMMessageService {
	readonly _serviceBrand: undefined;

	constructor(
		@ICodeseekSettingsService private readonly codeseekSettingsService: ICodeseekSettingsService,
	) {
		super();
	}

	prepareLLMChatMessages(opts: ConvertMessagesOpts): SendLLMType {
		if (opts.messagesType === 'codeAgentMessages') {
			return this.prepareCodeAgentMessages(opts);
		}
		else if (opts.messagesType === 'chatAgentMessages') {
			return this.prepareChatAgentMessages(opts);
		}
		else {
			return {
				messagesType: 'chatMessages',
				messages: [],
			};
		}
	}

	prepareChatAgentMessages(opts: ChatAgenConverttMessageOpts) {
		// codeseekTools转换为InternalToolInfo[]
		let tools = Object.values(codeseekTools).map(tool => {
			return {
				name: tool.name,
				description: tool.description,
				params: tool.params,
				required: tool.required,
				needApprove: tool.needApprove,
			} satisfies InternalToolInfo;
		});
		const filterTools = [ToolNameEnum.READ_FILE, ToolNameEnum.LIST_FILES, ToolNameEnum.CODEBASE_SEARCH];
		tools = tools.filter(tool => filterTools.includes(tool.name as ToolNameEnum));
		const toolsDefinition = systemToolsXMLPrompt(ChatMode.Agent, tools) || '';
		const systemMessage = enhancementSystemMessage(toolsDefinition) || '';
		const userMessage = enhancementUserMessage(opts.userMessage, opts.chatHistory, opts.openTabs, opts.workingDirectory, opts.directoryStrDetail);
		return {
			messagesType: 'chatMessages' as const,
			messages: [
				{ role: 'system' as const, content: systemMessage },
				{ role: 'user' as const, content: userMessage }
			],
		};
	}

	private prepareCodeAgentMessages(opts: CodeAgentConvertMessageOpts): SendLLMType {
		const tools = Object.values(codeseekTools).map(tool => {
			return {
				name: tool.name,
				description: tool.description,
				params: tool.params,
				required: tool.required,
				needApprove: tool.needApprove,
			} satisfies InternalToolInfo;
		});
		const toolsDefinition = systemToolsXMLPrompt(ChatMode.Agent, tools) || '';
		const stepDepResult = opts.step.dependencies.map(dep => `${dep.stepResult}`).join('\n');
		const systemMessage = code_agent_systemMessage(opts.task, opts.taskContext, opts.step.content, opts.step.detail, stepDepResult, toolsDefinition);
		return {
			messagesType: 'chatMessages',
			messages: [
				{ role: 'system' as const, content: systemMessage },
				{ role: 'user', content: user_rules(this.codeseekSettingsService.state.globalSettings.userRules) },
				{ role: 'user' as const, content: opts.step.content }
			],
		};
	}
}

registerSingleton(IConvertToLLMMessageService, ConvertToLLMMessageService, InstantiationType.Eager);
