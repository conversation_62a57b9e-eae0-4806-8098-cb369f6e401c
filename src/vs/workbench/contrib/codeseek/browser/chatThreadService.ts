/*--------------------------------------------------------------------------------------
 *  Copyright 2025 ZTE, Inc. All rights reserved.
 *  Licensed under the Apache License, Version 2.0. See LICENSE.txt for more information.
 *--------------------------------------------------------------------------------------*/

import { Disposable } from '../../../../base/common/lifecycle.js';
import { InstantiationType, registerSingleton } from '../../../../platform/instantiation/common/extensions.js';
import { IStorageService, StorageScope, StorageTarget } from '../../../../platform/storage/common/storage.js';
import { Emitter, Event } from '../../../../base/common/event.js';
import { generateUuid } from '../../../../base/common/uuid.js';
import { IEditorService } from '../../../services/editor/common/editorService.js';
import { IMentionService, StagingSelectionItem } from '../common/selectedFileService.js';
import { AskResponse } from '../common/toolsServiceTypes.js';
import { ICodeseekCodeSelectionService } from './codeseekCodeSelectionService.js';
import { ISidebarStateService } from './sidebarStateService.js';
import { THREAD_MESSAGES_STORAGE_KEY, THREAD_ABSTRACT_STORAGE_KEY } from '../common/storageKeys.js';
import { URI } from '../../../../base/common/uri.js';
import { IChatThreadService, ChatContainers, ChatMessage, ChatThreads, ContainerState, ThreadStreamState, ThreadType, ThreadsState, UserMessageState, userMessageOpts } from './chatThreadType.js';
import { IViewsService } from '../../../services/views/common/viewsService.js';
import { SyncDescriptor } from '../../../../platform/instantiation/common/descriptors.js';
import { IViewContainersRegistry, IViewsRegistry, ViewContainerLocation, Extensions as ViewExtensions } from '../../../common/views.js';
import { ViewPaneContainer } from '../../../browser/parts/views/viewPaneContainer.js';
import { Orientation } from '../../../../base/browser/ui/sash/sash.js';
import { localize2 } from '../../../../nls.js';
import { Registry } from '../../../../platform/registry/common/platform.js';
import { SidebarViewPane } from './sidebarPane.js';
import { CODESEEK_NEW_CHAT_ACTION_ID, CODESEEK_VIEW_CONTAINER_ID, CODESEEK_VIEW_CONTAINER_ID_KEY } from './actionIDs.js';
import { ICommandService } from '../../../../platform/commands/common/commands.js';
import { Codicon } from '../../../../base/common/codicons.js';
import { IInstantiationService } from '../../../../platform/instantiation/common/instantiation.js';
import { IAgentManageService } from './agent/agentManageService.js';
import { ChatMode } from '../common/codeseekSettingsService.js';
import { ICodeseekLogger } from '../common/codeseekLogService.js';

let isContainerLoaded = false;
const MAX_CONTAINERS = 3;
let order = 2;

const defaultMessageState: UserMessageState = {
	stagingSelections: [],
	isBeingEdited: false,
};

const newThreadObject = () => {
	const now = new Date().toISOString();
	return {
		id: generateUuid(),
		createdAt: now,
		lastModified: now,
		messagesLength: 0,
		firstUserMessage: '',
		state: {
			stateSelections: { list: [], followEditorActive: true },
			focusedMessageIdx: undefined,
			isCheckedOfSelectionId: {}
		},

	} satisfies ChatThreads[string];
};

export class ChatThreadService extends Disposable implements IChatThreadService {
	_serviceBrand: undefined;

	// this fires when the current thread changes at all (a switch of currentThread, or a message added to it, etc)
	private readonly _onDidChangeCurrentThread = new Emitter<string>();
	readonly onDidChangeCurrentThread: Event<string> = this._onDidChangeCurrentThread.event;

	private readonly _onDidChangeCurrentContainer = new Emitter<void>();
	readonly onDidChangeCurrentContainer: Event<void> = this._onDidChangeCurrentContainer.event;

	private readonly _onDidSetChatTitle = new Emitter<{ containerId: string, message: string }>();
	readonly onDidSetChatTitle: Event<{ containerId: string, message: string }> = this._onDidSetChatTitle.event;

	containerState: ContainerState;
	readonly streamState: ThreadStreamState = {};

	private readonly _onDidChangeStreamState = new Emitter<{ containerId: string, threadId: string }>();
	readonly onDidChangeStreamState: Event<{ containerId: string, threadId: string }> = this._onDidChangeStreamState.event;

	constructor(
		@IStorageService private readonly _storageService: IStorageService,
		@ISidebarStateService private readonly _sidebarStateService: ISidebarStateService,
		@ICodeseekCodeSelectionService private readonly _codeSeekCodeSelectionService: ICodeseekCodeSelectionService,
		@IEditorService private readonly _editorService: IEditorService,
		@ICodeseekLogger private readonly _codeseekLogService: ICodeseekLogger,
		@IMentionService private readonly _mentionsService: IMentionService,
		@IViewsService private readonly _viewsService: IViewsService,
		@ICommandService private readonly _commandService: ICommandService,
		@IInstantiationService private readonly _instantiationService: IInstantiationService,
	) {
		super();

		this.containerState = {
			allContainers: {
				[CODESEEK_VIEW_CONTAINER_ID]: {
					threadsState: { allThreads: {}, currentThreadId: '', currentThreadMessages: [] }
				}
			},
			currentContainerId: CODESEEK_VIEW_CONTAINER_ID
		};

		const allContainers = this._readAllContainer();
		if (allContainers) {
			this.containerState.allContainers = allContainers;
			this._registerExistingContainers();
			// isContainerLoaded = true;
			// for (const containerId of Object.keys(this.containerState.allContainers)) {
			// 	const threadsState = this.containerState.allContainers[containerId].threadsState;
			// 	if (threadsState && threadsState.currentThreadId && threadsState.currentThreadId !== '') {
			// 		const messages = this._loadChatMessages(containerId, threadsState.currentThreadId);
			// 		threadsState.currentThreadMessages = messages;
			// 	}
			// }
		}

		if (!this.getCurrentThreadId(this.containerState.currentContainerId) || this.getCurrentThreadId(this.containerState.currentContainerId) === '') {
			this.openNewThread(this.containerState.currentContainerId);
		}

		this._editorService.onDidActiveEditorChange(() => this.onDidActiveEditorChange(this.containerState.currentContainerId));
		this._codeSeekCodeSelectionService.onDidAddContentFromEditor((selection) => this.addSelectionToChat(this.containerState.currentContainerId, selection));
		this._codeSeekCodeSelectionService.onDidAddCodeBlock((selection) => this.addSelectionToChat(this.containerState.currentContainerId, selection));

		this._viewsService.onDidChangeViewContainerVisibility(event => {
			if (event.visible && event.id.startsWith(CODESEEK_VIEW_CONTAINER_ID_KEY)) {
				this.containerState.currentContainerId = event.id;
				this._onDidChangeCurrentContainer.fire();
				this._onDidChangeCurrentThread.fire(event.id);
				if (isContainerLoaded) {
					isContainerLoaded = false;
					Object.keys(this.containerState.allContainers).forEach(containerId => {
						const container = this.containerState.allContainers[containerId];
						const messages = container.threadsState.currentThreadMessages;
						if (messages && messages.length > 0) {
							const firstUserMessage = messages.find((m: ChatMessage) => m.role === 'user');
							if (firstUserMessage && firstUserMessage.displayContent) {
								this._onDidSetChatTitle.fire({ containerId, message: firstUserMessage.displayContent });
							}
						}
					});
				}
			}
		});
	}

	public openNewContainer(): string {
		const viewContainersRegistry = Registry.as<IViewContainersRegistry>(ViewExtensions.ViewContainersRegistry);
		const viewsRegistry = Registry.as<IViewsRegistry>(ViewExtensions.ViewsRegistry);

		const allContainerIds = Object.keys(this.containerState.allContainers);

		if (allContainerIds.length >= MAX_CONTAINERS) {
			this._commandService.executeCommand(CODESEEK_NEW_CHAT_ACTION_ID);
			return this.containerState.currentContainerId;
		}

		const usedOrders = new Set<number>();
		for (const id of allContainerIds) {
			const orderStr = id.split('.')[3];
			const orderNum = parseInt(orderStr);
			if (!isNaN(orderNum)) {
				usedOrders.add(orderNum);
			}
		}

		let nextOrder = 2;
		while (usedOrders.has(nextOrder)) {
			nextOrder++;
		}

		const newContainerId = `workbench.codeseek.container.${nextOrder}.view`;
		const newViewId = newContainerId;

		const newContainer = viewContainersRegistry.registerViewContainer({
			id: newContainerId,
			title: localize2('newCodeseekContainer', 'New Chat'),
			ctorDescriptor: new SyncDescriptor(
				ViewPaneContainer, [newContainerId, {
					mergeViewWithContainerWhenSingleView: true,
					orientation: Orientation.HORIZONTAL,
				}]
			),
			hideIfEmpty: false,
			order: nextOrder,
			rejectAddedViews: true,
			icon: Codicon.symbolMethod,
		}, ViewContainerLocation.AuxiliaryBar, { doNotRegisterOpenCommand: true, isDefault: true });

		viewsRegistry.registerViews([{
			id: newViewId,
			hideByDefault: false,
			name: localize2('newCodeseekChat', 'New Chat'),
			ctorDescriptor: new SyncDescriptor(SidebarViewPane),
			canToggleVisibility: false,
			canMoveView: false,
			weight: 80,
			order: 1,
		}], newContainer);

		this.containerState.currentContainerId = newContainerId;
		this.containerState.allContainers[newContainerId] = {
			threadsState: { allThreads: {}, currentThreadId: '', currentThreadMessages: [] }
		};
		this.openNewThread(newContainerId);

		order = Math.max(order, nextOrder + 1);

		return newContainerId;
	}

	openNewThread(containerId: string) {
		const currentThread = this.getCurrentThread(containerId);
		if (currentThread && this.isCurrentThreadWorking(containerId)) {
			this.cancelStreaming(containerId, currentThread.id);
		}

		this._sidebarStateService.fireOpenNewChat(containerId);
		// if a thread with 0 messages already exists, switch to it
		const { allThreads } = this.containerState.allContainers[containerId].threadsState;
		for (const threadId in allThreads) {
			if (allThreads[threadId].messagesLength === 0) {
				allThreads[threadId].state.focusedMessageIdx = undefined;
				allThreads[threadId].state.stateSelections = { list: [], followEditorActive: true };
				allThreads[threadId].state.isCheckedOfSelectionId = {};
				this.switchToThread(containerId, threadId);
				this._addSelectionToChat(containerId);
				return;
			}
		}
		// otherwise, start a new thread
		const newThread = newThreadObject();

		// update state
		const newThreads: ChatThreads = {
			...allThreads,
			[newThread.id]: newThread
		};
		this._onDidSetChatTitle.fire({ containerId, message: 'New Chat' });
		this._setState(containerId, { allThreads: newThreads, currentThreadId: newThread.id, currentThreadMessages: [] }, true);
		this._storeChatSummary();
		this._addSelectionToChat(containerId);
	}

	private _convertThreadDataFromStorage(threadsStr: string): ChatContainers | ChatThreads | ChatMessage[] {
		const data = JSON.parse(threadsStr, (key, value) => {
			if (value && typeof value === 'object' && value.$mid === 1) {
				return URI.from(value);
			}
			return value;
		});

		if (data && typeof data === 'object') {
			if ('allThreads' in data && 'currentThreadId' in data && !('allContainers' in data)) {
				const oldFormat = data as unknown as ThreadsState;
				const newFormat: ChatContainers = {
					[CODESEEK_VIEW_CONTAINER_ID]: {
						threadsState: {
							allThreads: oldFormat.allThreads || {},
							currentThreadId: oldFormat.currentThreadId || '',
							currentThreadMessages: []
						}
					}
				};
				return newFormat;
			}
		}

		return data;
	}

	private _readAllContainer(): ChatContainers | null {
		try {
			const containerMapStr = this._storageService.get(THREAD_ABSTRACT_STORAGE_KEY, StorageScope.WORKSPACE);
			if (!containerMapStr) {
				return null;
			}
			const containers = this._convertThreadDataFromStorage(containerMapStr) as ChatContainers;
			if (!containers || typeof containers !== 'object') {
				this._codeseekLogService.error('Invalid container structure in storage');
				return null;
			}

			if (!containers[CODESEEK_VIEW_CONTAINER_ID]) {
				containers[CODESEEK_VIEW_CONTAINER_ID] = {
					threadsState: { allThreads: {}, currentThreadId: '', currentThreadMessages: [] }
				};
			}

			for (const containerId of Object.keys(containers)) {
				if (!containers[containerId].threadsState) {
					containers[containerId].threadsState = {
						allThreads: {},
						currentThreadId: '',
						currentThreadMessages: []
					};
				} else {
					containers[containerId].threadsState.currentThreadMessages = [];
					if (!containers[containerId].threadsState.currentThreadId) {
						containers[containerId].threadsState.currentThreadId = '';
					}
				}
			}

			return containers;
		} catch (error) {
			this._codeseekLogService.error('Error reading containers:', error);
			return null;
		}
	}

	private _storeChatSummary() {
		const { allContainers } = this.containerState
		const serializedSummary = JSON.stringify(allContainers);
		this._storageService.store(
			THREAD_ABSTRACT_STORAGE_KEY,
			serializedSummary,
			StorageScope.WORKSPACE,
			StorageTarget.USER
		);
	}

	private _storeChatMessages(containerId: string, threadId: string, messags: ChatMessage[]) {
		const serializedMessages = JSON.stringify(messags)
		this._storageService.store(
			`${THREAD_MESSAGES_STORAGE_KEY}--${containerId}--${threadId}`,
			serializedMessages,
			StorageScope.WORKSPACE,
			StorageTarget.USER
		);
	}

	private _loadChatMessages(containerId: string, threadId: string): ChatMessage[] {
		const messagesStr = this._storageService.get(`${THREAD_MESSAGES_STORAGE_KEY}--${containerId}--${threadId}`, StorageScope.WORKSPACE);
		if (!messagesStr) {
			return [];
		}
		const messages = this._convertThreadDataFromStorage(messagesStr);

		if (messages && Array.isArray(messages)) {
			const firstUserMessage = messages.find(m => m.role === 'user');
			if (firstUserMessage && firstUserMessage.displayContent) {
				this._onDidSetChatTitle.fire({ containerId, message: firstUserMessage.displayContent });
			}
		}

		return messages as ChatMessage[];
	}

	private _deleteThreadMessages(containerId: string, threadId: string): void {
		this._storageService.remove(`${THREAD_MESSAGES_STORAGE_KEY}--${containerId}--${threadId}`, StorageScope.WORKSPACE)
	}

	// this should be the only place this.state = ... appears besides constructor
	private _setState(containerId: string, state: Partial<ThreadsState>, affectsCurrent: boolean) {
		this.containerState.allContainers[containerId].threadsState = {
			...this.containerState.allContainers[containerId].threadsState,
			...state
		};
		if (affectsCurrent)
			this._onDidChangeCurrentThread.fire(containerId);
	}

	// private _getSelectionsUpToMessageIdx(containerId: string, messageIdx: number) {
	// 	const prevMessages = this.containerState.allContainers[containerId].threadsState.currentThreadMessages.slice(0, messageIdx);
	// 	return prevMessages.flatMap(m => m.role === 'user' && m.state.stagingSelections || []);
	// }

	public setStreamState(containerId: string, threadId: string, state: Partial<NonNullable<ThreadStreamState[string][string]>>) {
		this.streamState[containerId] ??= {};
		this.streamState[containerId][threadId] = {
			...this.streamState[containerId]?.[threadId],
			...state
		};
		this._onDidChangeStreamState.fire({ containerId, threadId });
	}

	// ---------- streaming ----------
	finishStreamingTextMessage(containerId: string, threadId: string, content: string, error?: { message: string; fullError: Error | null }) {
		// add assistant's message to chat history, and clear selection
		this.addMessageToThread(containerId, threadId, { role: 'assistant', content, displayContent: content || null });
		this.setStreamState(containerId, threadId, { messageSoFar: undefined, streamingToken: undefined, error, isStreaming: false });
	}

	async editUserMessageAndStreamResponse({ containerId, threadId, userMessage, chatMode, messageIdx }: { containerId: string; threadId: string; userMessage: string; chatMode: ChatMode; messageIdx: number }) {

		const currentThreadMessages = this.containerState.allContainers[containerId].threadsState.currentThreadMessages

		if (currentThreadMessages[messageIdx]?.role !== 'user') {
			throw new Error("Error: editing a message with role !=='user'");
		}

		// get curr selections before clearing the message
		const currSelns = currentThreadMessages[messageIdx].state.stagingSelections || [];

		// clear messages up to the index
		const slicedMessages = currentThreadMessages.slice(0, messageIdx);
		this._setState(containerId, {
			currentThreadMessages: slicedMessages
		}, true);

		// re-add the message and stream it
		this.addUserMessageAndStreamResponse({ containerId, userMessageOpts: { from: 'Chat', chatMode, userMessage, selections: [...currSelns] } });
	}

	async addUserMessageAndStreamResponse({ containerId, userMessageOpts }: {
		containerId: string;
		userMessageOpts: userMessageOpts;
	}) {
		const thread = this.getCurrentThread(containerId);
		const threadId = thread.id;

		userMessageOpts.selections = userMessageOpts.selections || thread.state.stateSelections.list;
		const state = {
			stagingSelections: [...userMessageOpts.selections],
			isBeingEdited: false,
		};

		if (userMessageOpts.userMessage && thread.firstUserMessage === '') {
			this._onDidSetChatTitle.fire({ containerId, message: userMessageOpts.userMessage });
		}

		const userHistoryElt: ChatMessage = { role: 'user', content: userMessageOpts.userMessage, displayContent: userMessageOpts.userMessage, state };
		this._setFirstUserMessage(containerId, threadId, userMessageOpts.userMessage)
		this.addMessageToThread(containerId, threadId, userHistoryElt);
		this.setStreamState(containerId, threadId, { error: undefined, isStreaming: true });

		this._instantiationService.invokeFunction(accessor => {
			const agentManageService = accessor.get(IAgentManageService);
			agentManageService.agentLoop(containerId, threadId, userMessageOpts);
		});
	}

	cancelStreaming(containerId: string, threadId: string) {
		const cancelToken = this.streamState[containerId]?.[threadId]?.streamingToken;
		if (cancelToken !== undefined) {
			this._instantiationService.invokeFunction(accessor => {
				const agentManageService = accessor.get(IAgentManageService);
				agentManageService.abort(cancelToken)
			});
		}
		this.finishStreamingTextMessage(containerId, threadId, this.streamState[containerId]?.[threadId]?.messageSoFar ?? '');
	}

	dismissStreamError(containerId: string, threadId: string): void {
		this.setStreamState(containerId, threadId, { error: undefined });
	}


	// ---------- the rest ----------

	isCurrentThreadWorking(containerId: string): boolean {
		const currentThread = this.getCurrentThread(containerId);
		if (!currentThread) return false;
		const streamState = this.streamState[containerId]?.[currentThread.id];
		return streamState && streamState.streamingToken && !streamState.error ? true : false;
	}

	getCurrentContainerId(): string {
		return this.containerState.currentContainerId;
	}

	getCurrentThreadId(containerId: string): string {
		return this.containerState.allContainers[containerId].threadsState.currentThreadId;
	}

	getCurrentThread(containerId: string): ChatThreads[string] {
		const container = this.containerState.allContainers[containerId];
		if (!container.threadsState.currentThreadId || container.threadsState.currentThreadId === '') {
			return undefined as unknown as ChatThreads[string];
		}
		return container.threadsState.allThreads[container.threadsState.currentThreadId];
	}

	getCurrentThreadMessages(containerId: string): ChatMessage[] {
		return this.containerState.allContainers[containerId].threadsState.currentThreadMessages
	}

	isFocusingContainer(containerId: string): boolean {
		return this.containerState.currentContainerId === containerId;
	}

	getFocusedMessageIdx(containerId: string): number | undefined {
		const thread = this.getCurrentThread(containerId);
		if (!thread) return undefined;

		// get the focusedMessageIdx
		const focusedMessageIdx = thread.state.focusedMessageIdx;
		if (focusedMessageIdx === undefined) return;

		// check that the message is actually being edited
		const focusedMessage = this.getCurrentThreadMessages(containerId)[focusedMessageIdx];
		if (focusedMessage.role !== 'user') return;
		if (!focusedMessage.state) return;

		return focusedMessageIdx;
	}

	isFocusingMessage(containerId: string): boolean {
		return this.getFocusedMessageIdx(containerId) !== undefined;
	}

	switchToContainer(containerId: string): void {
		this.containerState.currentContainerId = containerId;
		this._sidebarStateService.fireFocusContainer(containerId);
		this._onDidChangeCurrentContainer.fire();
		this._onDidChangeCurrentThread.fire(containerId);
	}

	switchToThread(containerId: string, threadId: string, targetContainerId?: string) {
		const actualTargetContainerId = targetContainerId ?? containerId;

		if (!this.containerState.allContainers[actualTargetContainerId]) {
			return;
		}
		const messages = this._loadChatMessages(containerId, threadId);
		if (actualTargetContainerId !== this.containerState.currentContainerId) {
			this.switchToContainer(actualTargetContainerId);
		}
		this._setState(actualTargetContainerId, {
			currentThreadId: threadId,
			currentThreadMessages: messages
		}, true);
	}

	deleteContainer(containerId: string): void {
		if (containerId === CODESEEK_VIEW_CONTAINER_ID) {
			return;
		}

		const currentThreadId = this.getCurrentThreadId(containerId);
		if (currentThreadId) {
			this.cancelStreaming(containerId, currentThreadId);
		}

		const threadsState = this.containerState.allContainers[containerId]?.threadsState;
		if (threadsState) {
			const threadIds = Object.keys(threadsState.allThreads);
			threadIds.forEach(threadId => {
				this._deleteThreadMessages(containerId, threadId);
			});
		}

		const viewContainersRegistry = Registry.as<IViewContainersRegistry>(ViewExtensions.ViewContainersRegistry);
		const viewContainer = viewContainersRegistry.get(containerId);
		if (viewContainer) {
			viewContainersRegistry.deregisterViewContainer(viewContainer);
		}

		if (this.streamState[containerId]) {
			delete this.streamState[containerId];
		}

		delete this.containerState.allContainers[containerId];

		if (this.containerState.currentContainerId === containerId) {
			this.containerState.currentContainerId = CODESEEK_VIEW_CONTAINER_ID;
			this._sidebarStateService.fireFocusContainer(CODESEEK_VIEW_CONTAINER_ID);
			this._onDidChangeCurrentContainer.fire();
			this._onDidChangeCurrentThread.fire(CODESEEK_VIEW_CONTAINER_ID);
		}

		this._storeChatSummary();
	}

	deleteThread(containerId: string, threadId: string) {
		const currentThread = this.getCurrentThread(containerId);
		const isCurrentThread = currentThread?.id === threadId;

		if (isCurrentThread) {
			this.cancelStreaming(containerId, threadId);
		}

		const { allThreads } = this.containerState.allContainers[containerId].threadsState;
		delete allThreads[threadId];
		this._deleteThreadMessages(containerId, threadId)

		// 清理streamState中的相关状态
		if (this.streamState[containerId]?.[threadId]) {
			delete this.streamState[containerId][threadId];
		}

		// 保存更新后的线程集合
		this._storeChatSummary();

		if (isCurrentThread) {
			const remainingThreadIds = Object.keys(allThreads);
			if (remainingThreadIds.length > 0) {
				// 切换到第一个可用的线程
				this.switchToThread(containerId, remainingThreadIds[0]);
			} else {
				// 如果没有可用的线程，创建一个新线程
				this.openNewThread(containerId);
			}
		} else {
			this._setState(containerId, { allThreads }, true);
		}
	}

	private _addSelectionToChat(containerId: string) {
		const selection = this._codeSeekCodeSelectionService.getFileSelction();
		this.addSelectionToChat(containerId, selection);
	}

	addSelectionToChat(containerId: string, selection?: StagingSelectionItem) {
		if (!selection) return;

		const focusedMessageIdx = this.getFocusedMessageIdx(containerId);
		let selections: StagingSelectionItem[] = [];
		let setSelections = (s: StagingSelectionItem[]) => { };

		if (focusedMessageIdx === undefined) {
			selections = this.getCurrentThreadStagingSelections(containerId);
			setSelections = (s: StagingSelectionItem[]) => this.setCurrentThreadStagingSelections(containerId, s);
		} else {
			selections = this.getCurrentMessageState(containerId, focusedMessageIdx).stagingSelections;
			setSelections = (s) => this.setCurrentMessageState(containerId, focusedMessageIdx, { stagingSelections: s });
		}

		const updatedSelections = selections.map(item => {
			if (item.type === 'File' && selection.type === 'File' &&
				item.fileURI.toString() === selection.fileURI.toString() &&
				item.fromActive) {
				return {
					...item,
					fromActive: false,
					fromEditor: true
				};
			}
			return item;
		});

		setSelections(this._mentionsService.addItemToSelectedFile(updatedSelections, selection));
	}

	addMessageToThread(containerId: string, threadId: string, message: ChatMessage, affectCurrentThread: boolean = true) {
		const { allThreads } = this.containerState.allContainers[containerId].threadsState;

		const oldThread = allThreads[threadId];
		const newMessages = [...this.containerState.allContainers[containerId].threadsState.currentThreadMessages, message]

		// update state and store it
		const newThreads = {
			...allThreads,
			[oldThread.id]: {
				...oldThread,
				lastModified: new Date().toISOString(),
				messagesLength: newMessages.length,
				// messages: [...oldThread.messages, message],
			}
		};
		this._setState(containerId, { allThreads: newThreads, currentThreadMessages: newMessages }, affectCurrentThread); // the current thread just changed (it had a message added to it)
		this._storeChatSummary()
		this._storeChatMessages(containerId, oldThread.id, newMessages);
	}
	private _setFirstUserMessage(containerId: string, threadId: string, userMessage: string) {
		const allThreads = this.containerState.allContainers[containerId].threadsState.allThreads
		const thread = this.containerState.allContainers[containerId].threadsState.allThreads[threadId]
		if (thread.firstUserMessage === '') {
			const newThreads = {
				...allThreads,
				[thread.id]: {
					...thread,
					firstUserMessage: userMessage
				}
			}
			this._setState(containerId, { allThreads: newThreads }, false)
		}
	}
	// sets the currently selected message (must be undefined if no message is selected)
	setFocusedMessageIdx(containerId: string, messageIdx: number | undefined) {

		const threadId = this.containerState.allContainers[containerId].threadsState.currentThreadId;
		const thread = this.containerState.allContainers[containerId].threadsState.allThreads[threadId];
		if (!thread) return;

		this._setState(containerId, {
			allThreads: {
				...this.containerState.allContainers[containerId].threadsState.allThreads,
				[threadId]: {
					...thread,
					state: {
						...thread.state,
						focusedMessageIdx: messageIdx,
					}
				}
			}
		}, true);
	}

	// set message.state
	private _setCurrentMessageState(containerId: string, state: Partial<UserMessageState>, messageIdx: number): void {

		const threadId = this.containerState.allContainers[containerId].threadsState.currentThreadId;
		const thread = this.containerState.allContainers[containerId].threadsState.allThreads[threadId];
		if (!thread) return;

		this._setState(containerId, {
			currentThreadMessages: this.containerState.allContainers[containerId].threadsState.currentThreadMessages.map((m, i) =>
				i === messageIdx && m.role === 'user' ? {
					...m,
					state: {
						...m.state,
						...state
					},
				} : m
			)
		}, true);

	}

	// set thread.state
	private _setCurrentThreadState(containerId: string, state: Partial<ThreadType['state']>): void {

		const threadId = this.containerState.allContainers[containerId].threadsState.currentThreadId;
		const thread = this.containerState.allContainers[containerId].threadsState.allThreads[threadId];
		if (!thread) return;

		this._setState(containerId, {
			allThreads: {
				...this.containerState.allContainers[containerId].threadsState.allThreads,
				[thread.id]: {
					...thread,
					state: {
						...thread.state,
						...state
					}
				}
			}
		}, true);

	}

	getCurrentThreadStagingSelections = (containerId: string) => {
		const thread = this.getCurrentThread(containerId);
		if (!thread) return [];
		return thread.state.stateSelections.list;
	};

	getCurrentThreadStateSelections = (containerId: string) => {
		const thread = this.getCurrentThread(containerId);
		if (!thread) return { list: [], followEditorActive: true };
		return thread.state.stateSelections;
	};

	setCurrentThreadStagingSelections = (containerId: string, stagingSelections: StagingSelectionItem[]) => {
		const thread = this.getCurrentThread(containerId);
		if (!thread) return;
		const stateSelections = thread.state.stateSelections;
		stateSelections.list = [...stagingSelections];
		this._setCurrentThreadState(containerId, { stateSelections });
	};

	// gets `staging` and `setStaging` of the currently focused element, given the index of the currently selected message (or undefined if no message is selected)

	getCurrentMessageState = (containerId: string, messageIdx: number): UserMessageState => {
		const currMessage = this.containerState.allContainers[containerId].threadsState.currentThreadMessages[messageIdx];
		if (!currMessage || currMessage.role !== 'user') return defaultMessageState;
		return currMessage.state;
	}

	setCurrentMessageState = (containerId: string, messageIdx: number, newState: Partial<UserMessageState>) => {
		const currMessage = this.containerState.allContainers[containerId].threadsState.currentThreadMessages[messageIdx];
		if (!currMessage || currMessage.role !== 'user') return;
		this._setCurrentMessageState(containerId, newState, messageIdx);
	}

	setCurrentThreadStateSelectionsChangeSelections = (containerId: string) => {
		const thread = this.getCurrentThread(containerId);
		if (!thread) return;
		const stagingSelections = thread.state.stateSelections;
		stagingSelections.followEditorActive = false;
	}

	private onDidActiveEditorChange(containerId: string) {
		const selections = this.getCurrentThreadStagingSelections(containerId);
		if (selections.length === 0) {
			this._addSelectionToChat(containerId);
		} else {
			if (this.containerState.allContainers[containerId].threadsState.currentThreadMessages.length === 0) {
				const selection = this._codeSeekCodeSelectionService.getFileSelction();
				if (!selection) {
					return;
				}

				let hasSameUriFromEditor = false;
				const filteredSelections = selections.filter(s => {
					if (s.type === 'File') {
						if (s.fileURI.toString() === selection.fileURI.toString() && s.fromEditor) {
							hasSameUriFromEditor = true;
							return true;
						}
						return !s.fromActive;
					}
					return true;
				});
				if (!hasSameUriFromEditor) {
					this.setCurrentThreadStagingSelections(containerId, [selection, ...filteredSelections]);
				} else {
					this.setCurrentThreadStagingSelections(containerId, filteredSelections);
				}
			}
		}
	}

	setAskResponse(containerId: string, askResponse: AskResponse): void {
		const currentThreadState = this.getCurrentThread(containerId).state;
		currentThreadState.askResponse = { type: askResponse.type, response: askResponse.response, text: askResponse.text };
	}

	private _registerExistingContainers(): void {
		const viewContainersRegistry = Registry.as<IViewContainersRegistry>(ViewExtensions.ViewContainersRegistry);
		const viewsRegistry = Registry.as<IViewsRegistry>(ViewExtensions.ViewsRegistry);

		const containerIds = Object.keys(this.containerState.allContainers);

		for (const containerId of containerIds) {
			if (containerId === CODESEEK_VIEW_CONTAINER_ID) {
				continue;
			}

			const orderStr = containerId.split('.')[3];
			const orderNum = parseInt(orderStr);
			if (isNaN(orderNum)) {
				continue;
			}

			const existingContainer = viewContainersRegistry.get(containerId);
			if (existingContainer) {
				continue;
			}

			const newContainer = viewContainersRegistry.registerViewContainer({
				id: containerId,
				title: localize2('newCodeseekContainer', 'New Chat'),
				ctorDescriptor: new SyncDescriptor(
					ViewPaneContainer,
					[containerId, {
						mergeViewWithContainerWhenSingleView: true,
						orientation: Orientation.HORIZONTAL,
					}]
				),
				hideIfEmpty: false,
				order: orderNum,
			}, ViewContainerLocation.AuxiliaryBar, { doNotRegisterOpenCommand: true, isDefault: true });

			viewsRegistry.registerViews([{
				id: containerId,
				hideByDefault: false,
				name: localize2('newCodeseekChat', 'New Chat'),
				ctorDescriptor: new SyncDescriptor(SidebarViewPane),
				canToggleVisibility: false,
				canMoveView: false,
				weight: 80,
				order: 1,
			}], newContainer);

			const threadsState = this.containerState.allContainers[containerId].threadsState;
			const threadIds = Object.keys(threadsState.allThreads);

			if (threadIds.length > 0 && (!threadsState.currentThreadId || threadsState.currentThreadId === '')) {
				const firstThreadId = threadIds[0];
				const messages = this._loadChatMessages(containerId, firstThreadId);
				this._setState(containerId, {
					currentThreadId: firstThreadId,
					currentThreadMessages: messages
				}, false);
			} else if (threadIds.length === 0) {
				this.openNewThread(containerId);
			}
		}

		const maxOrder = containerIds.reduce((max, id) => {
			const orderStr = id.split('.')[3];
			const orderNum = parseInt(orderStr);
			return isNaN(orderNum) ? max : Math.max(max, orderNum);
		}, order);

		order = Math.max(order, maxOrder + 1);
	}
}

registerSingleton(IChatThreadService, ChatThreadService, InstantiationType.Eager);
