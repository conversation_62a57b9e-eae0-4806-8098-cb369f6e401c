/*--------------------------------------------------------------------------------------
 *  Copyright 2025 ZTE, Inc. All rights reserved.
 *  Licensed under the Apache License, Version 2.0. See LICENSE.txt for more information.
 *--------------------------------------------------------------------------------------*/

import { KeyCode, KeyMod } from '../../../../base/common/keyCodes.js';
import { Action2, MenuId, registerAction2 } from '../../../../platform/actions/common/actions.js';
import { ServicesAccessor } from '../../../../editor/browser/editorExtensions.js';
import { KeybindingWeight } from '../../../../platform/keybinding/common/keybindingsRegistry.js';
import { IChatThreadService } from './chatThreadType.js';
import { ISidebarStateService } from './sidebarStateService.js';
import { CommandsRegistry, ICommandService } from '../../../../platform/commands/common/commands.js';
import { CODESEEK_CTRL_L_ACTION_ID, CODESEEK_NEW_CHAT_ACTION_ID } from './actionIDs.js';
import { localize2 } from '../../../../nls.js';
import { IEditCodeService } from './editCodeService.js';
import { ITextAreaService } from './InputBox2Service.js';
import { ICodeseekCodeSelectionService } from './codeseekCodeSelectionService.js';
import { ICodeEditorService } from '../../../../editor/browser/services/codeEditorService.js';
import { IEditorService } from '../../../../workbench/services/editor/common/editorService.js';
import { IListService } from '../../../../platform/list/browser/listService.js';
import { getResourceForCommand } from '../../files/browser/files.js';
import { URI } from '../../../../base/common/uri.js';
import { MenuRegistry } from '../../../../platform/actions/common/actions.js';
import { IFileService } from '../../../../platform/files/common/files.js';
import { ContextKeyExpr } from '../../../../platform/contextkey/common/contextkey.js';

// ---------- Register commands and keybindings ----------

export const CODESEEK_OPEN_SIDEBAR_ACTION_ID = 'codeseek.sidebar.open';
registerAction2(class extends Action2 {
	constructor() {
		super({ id: CODESEEK_OPEN_SIDEBAR_ACTION_ID, title: localize2('codeseekOpenSidebar', 'Codeseek: Open Sidebar'), f1: true });
	}
	async run(accessor: ServicesAccessor): Promise<void> {
		const stateService = accessor.get(ISidebarStateService);
		const chatThreadService = accessor.get(IChatThreadService);
		const containerId = chatThreadService.getCurrentContainerId();
		stateService.setState({ isHistoryOpen: false, isChatOpen: true, currentTab: 'chat' });
		stateService.fireFocusContainer(containerId);
		stateService.fireFocusChat(containerId);
	}
});


// Action: when press ctrl+L, show the sidebar chat and add to the selection
export const CODESEEK_ADD_SELECTION_TO_SIDEBAR_ACTION_ID = 'codeseek.sidebar.select';
registerAction2(class extends Action2 {
	constructor() {
		super({ id: CODESEEK_ADD_SELECTION_TO_SIDEBAR_ACTION_ID, title: localize2('codeseekAddToSidebar', 'Codeseek: Add Selection to Sidebar'), f1: true });
	}
	async run(accessor: ServicesAccessor): Promise<void> {
		const codeSelectionService = accessor.get(ICodeseekCodeSelectionService);
		codeSelectionService.addCodeBlock();
	}
});

// press ctrl+L, open sidebar and add to chat
registerAction2(class extends Action2 {
	constructor() {
		super({
			id: CODESEEK_CTRL_L_ACTION_ID,
			f1: true,
			title: localize2('codeseekCtrlL', 'Flow: Add Select to Chat'),
			keybinding: {
				primary: KeyMod.CtrlCmd | KeyCode.KeyL,
				weight: KeybindingWeight.CodeseekExtension
			}
		});
	}
	async run(accessor: ServicesAccessor): Promise<void> {
		const commandService = accessor.get(ICommandService);
		const editCodeService = accessor.get(IEditCodeService);
		editCodeService.disposeTip('TextSelectionTipZone');
		await commandService.executeCommand(CODESEEK_OPEN_SIDEBAR_ACTION_ID);
		await commandService.executeCommand(CODESEEK_ADD_SELECTION_TO_SIDEBAR_ACTION_ID);
	}
});

// add context
registerAction2(class extends Action2 {
	constructor() {
		super({
			id: 'codeseek.addContext',
			f1: true,
			title: localize2('codeseekAddContext', 'Flow: Add Context'),
		});
	}
	async run(accessor: ServicesAccessor, args: string): Promise<void> {
		const commandService = accessor.get(ICommandService);
		const editCodeService = accessor.get(IEditCodeService);
		const textAreaService = accessor.get(ITextAreaService);

		editCodeService.disposeTip('TextSelectionTipZone');
		await commandService.executeCommand(CODESEEK_OPEN_SIDEBAR_ACTION_ID);
		textAreaService.setValue(args ?? '');
	}
});

// New Container menu button
// registerAction2(class extends Action2 {
// 	constructor() {
// 		super({
// 			id: CODESEEK_NEW_CONTAINER_ACTION_ID,
// 			title: 'New Container',
// 			keybinding: {
// 				primary: KeyMod.CtrlCmd | KeyMod.Alt | KeyCode.KeyL,
// 				weight: KeybindingWeight.WorkbenchContrib
// 			}
// 		});
// 	}
// 	async run(accessor: ServicesAccessor): Promise<void> {
// 		const stateService = accessor.get(ISidebarStateService);
// 		const chatThreadService = accessor.get(IChatThreadService);
// 		const containerId = chatThreadService.openNewContainer();
// 		stateService.fireFocusContainer(containerId);
// 	}
// });

// New chat menu button
registerAction2(class extends Action2 {
	constructor() {
		super({
			id: CODESEEK_NEW_CHAT_ACTION_ID,
			title: 'New Chat (Ctrl+L)',
			icon: { id: 'add' },
			menu: [{
				id: MenuId.ViewTitle,
				group: 'navigation',
				order: 1,
				when: ContextKeyExpr.regex('view', /^workbench\.codeseek\.container\.\d+\.view$/)
			}]
		});
	}

	async run(accessor: ServicesAccessor): Promise<void> {
		const stateService = accessor.get(ISidebarStateService);
		const chatThreadService = accessor.get(IChatThreadService);
		const containerId = chatThreadService.getCurrentContainerId();
		stateService.setState({ isHistoryOpen: false, isChatOpen: true, currentTab: 'chat' });
		chatThreadService.openNewThread(containerId);
		stateService.fireFocusChat(containerId);
	}
});

// History menu button for all view containers
registerAction2(class extends Action2 {
	constructor() {
		super({
			id: 'codeseek.historyAction',
			title: localize2('codeseekHistory', 'History'),
			icon: { id: 'history' },
			menu: [{
				id: MenuId.ViewTitle,
				group: 'navigation',
				order: 2,
				when: ContextKeyExpr.regex('view', /^workbench\.codeseek\.container\.\d+\.view$/)
			}]
		});
	}
	async run(accessor: ServicesAccessor): Promise<void> {
		const stateService = accessor.get(ISidebarStateService);
		stateService.setState({ isHistoryOpen: !stateService.state.isHistoryOpen });
	}
});

// 在编辑器上下文菜单中添加命令
registerAction2(class extends Action2 {
	constructor() {
		super({
			id: 'codeseek.addToContextFromEditor',
			title: localize2('codeseekAddToContext', 'Flow: Add Context'),
			menu: [
				{
					group: '0_codeseek',
					id: MenuId.EditorContext,
					order: 1,
				}
			]
		});
	}
	async run(accessor: ServicesAccessor): Promise<void> {
		const commandService = accessor.get(ICommandService);
		const editCodeService = accessor.get(IEditCodeService);
		const codeSelectionService = accessor.get(ICodeseekCodeSelectionService);
		const codeEditorService = accessor.get(ICodeEditorService);

		editCodeService.disposeTip('TextSelectionTipZone');
		await commandService.executeCommand(CODESEEK_OPEN_SIDEBAR_ACTION_ID);

		const model = codeEditorService.getActiveCodeEditor()?.getModel();
		if (model) {
			codeSelectionService.addContentFromEditor(model.uri);
		}
	}
});

// 在编辑器标题上下文菜单中添加命令
export const ADD_TO_CONTEXT_FROM_EDITOR_TITLE_COMMAND_ID = 'codeseek.addToContextFromEditorTitle';
CommandsRegistry.registerCommand({
	id: ADD_TO_CONTEXT_FROM_EDITOR_TITLE_COMMAND_ID,
	handler: async (accessor, resource: URI | object) => {
		const commandService = accessor.get(ICommandService);
		const codeSelectionService = accessor.get(ICodeseekCodeSelectionService);
		const editorService = accessor.get(IEditorService);
		const listService = accessor.get(IListService);

		const uri = getResourceForCommand(resource, editorService, listService);
		await commandService.executeCommand(CODESEEK_OPEN_SIDEBAR_ACTION_ID);

		if (uri) {
			codeSelectionService.addContentFromEditor(uri);
		}
	}
});

MenuRegistry.appendMenuItem(MenuId.EditorTitleContext, {
	command: {
		id: ADD_TO_CONTEXT_FROM_EDITOR_TITLE_COMMAND_ID,
		title: localize2('codeseekAddToContext', 'Flow: Add Context')
	},
	group: '0_codeseek',
	order: 1
});

// 在资源管理器上下文菜单中添加命令
export const ADD_TO_CONTEXT_FROM_EXPLORER_COMMAND_ID = 'codeseek.addToContextFromExplorer';
CommandsRegistry.registerCommand({
	id: ADD_TO_CONTEXT_FROM_EXPLORER_COMMAND_ID,
	handler: async (accessor, resource: URI | object) => {
		const commandService = accessor.get(ICommandService);
		const codeSelectionService = accessor.get(ICodeseekCodeSelectionService);
		const editorService = accessor.get(IEditorService);
		const listService = accessor.get(IListService);
		const fileService = accessor.get(IFileService);

		const uri = getResourceForCommand(resource, editorService, listService);
		if (!uri) {
			return;
		}
		await commandService.executeCommand(CODESEEK_OPEN_SIDEBAR_ACTION_ID);
		const stat = await fileService.stat(uri);
		if (stat.isFile) {
			codeSelectionService.addContentFromEditor(uri);
		}
	}
});
MenuRegistry.appendMenuItem(MenuId.ExplorerContext, {
	command: {
		id: ADD_TO_CONTEXT_FROM_EXPLORER_COMMAND_ID,
		title: localize2('codeseekAddToContext', 'Flow: Add Context')
	},
	group: '0_codeseek',
	order: 1
});
