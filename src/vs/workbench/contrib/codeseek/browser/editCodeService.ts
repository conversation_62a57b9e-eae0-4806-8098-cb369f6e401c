/*--------------------------------------------------------------------------------------
 *  Copyright 2025 ZTE, Inc. All rights reserved.
 *  Licensed under the Apache License, Version 2.0. See LICENSE.txt for more information.
 *--------------------------------------------------------------------------------------*/

import { Disposable } from '../../../../base/common/lifecycle.js';
import { registerSingleton, InstantiationType } from '../../../../platform/instantiation/common/extensions.js';
import { createDecorator, IInstantiationService } from '../../../../platform/instantiation/common/instantiation.js';
import { <PERSON><PERSON>odeE<PERSON>or, IViewZone } from '../../../../editor/browser/editorBrowser.js';

// import { IUndoRedoService } from '../../../../platform/undoRedo/common/undoRedo.js';
import { ICodeEditorService } from '../../../../editor/browser/services/codeEditorService.js';
// import { throttle } from '../../../../base/common/decorators.js';
import { ComputedDiff, findDiffs } from './helpers/findDiffs.js';
import { EndOfLinePreference, IModelDecorationOptions, ITextModel } from '../../../../editor/common/model.js';
import { IRange } from '../../../../editor/common/core/range.js';
import { IModelService } from '../../../../editor/common/services/model.js';
import { IUndoRedoElement, IUndoRedoService, UndoRedoElementType } from '../../../../platform/undoRedo/common/undoRedo.js';
import { RenderOptions } from '../../../../editor/browser/widget/diffEditor/components/diffEditorViewZones/renderLines.js';
// import { IModelService } from '../../../../editor/common/services/model.js';
import { ScrollType } from '../../../../editor/common/editorCommon.js';

import { URI } from '../../../../base/common/uri.js';
import { IConsistentEditorItemService, IConsistentItemService } from './helperServices/consistentItemService.js';
import { codeseekPrefixAndSuffix, ctrlKStream_userMessage, ctrlKStream_systemMessage, defaultQuickEditFimTags, rewriteCode_systemMessage, rewriteCode_userMessage, searchReplace_systemMessage, searchReplace_userMessage, } from './../common/prompt/prompts.js';

import { mountCtrlK } from './react/out/quick-edit-tsx/index.js';
import { QuickEditPropsType } from './quickEditActions.js';
import { IModelContentChangedEvent } from '../../../../editor/common/textModelEvents.js';
import { extractCodeFromFIM, extractCodeFromRegular, ExtractedSearchReplaceBlock, extractSearchReplaceBlocks } from './helpers/extractCodeFromResult.js';
import { filenameToVscodeLanguage } from '../common/helpers/detectLanguage.js';
import { INotificationService, Severity } from '../../../../platform/notification/common/notification.js';
import { isMacintosh } from '../../../../base/common/platform.js';
import { EditorOption } from '../../../../editor/common/config/editorOptions.js';
import { Emitter, Event } from '../../../../base/common/event.js';
import { CODESEEK_OPEN_SETTINGS_ACTION_ID } from './codeseekSettingsPane.js';
import { ICommandService } from '../../../../platform/commands/common/commands.js';
import { ILLMMessageService } from '../common/llmMessageService.js';
import { LLMChatMessage, OnError, errorDetails } from '../common/llmMessageTypes.js';
import { IMetricsService, METRICS_EVENT } from '../common/metricsService.js';
import { ICodeseekFileService } from '../common/codeseekFileService.js';
import { CODESEEK_OPEN_SIDEBAR_ACTION_ID, CODESEEK_ADD_SELECTION_TO_SIDEBAR_ACTION_ID } from './sidebarActions.js';
import { CODESEEK_CTRL_K_ACTION_ID, CODESEEK_EXPLAIN_CODE_ACTION_ID } from './actionIDs.js';
import { ISidebarStateService } from './sidebarStateService.js';
import { TextSelectionTipWidget } from './media/textSelectionTipWidget.js';
import { BlankLineTipWidget } from './media/blankLineTipWidget.js';
import { AcceptAllRejectAllWidget } from './media/acceptAllRejectAllWidget.js';
import { AcceptRejectWidget } from './media/acceptRejectWidget.js';
import { IQuickEditStateService } from './quickEditStateService.js';
import { FeatureNames } from '../common/codeseekSettingsTypes.js';
import { ICodeseekLogger } from '../common/codeseekLogService.js';
import { IFileService } from '../../../../platform/files/common/files.js';
import { IWorkspaceContextService } from '../../../../platform/workspace/common/workspace.js';
import { ILifecycleService } from '../../../services/lifecycle/common/lifecycle.js';
import { IEditorService } from '../../../services/editor/common/editorService.js';
import { getWorkspaceUri } from '../common/helpers/path.js';
import { StagingSelectionItem } from '../common/selectedFileService.js';
import { ICodeSeekExporerService } from '../common/codeseekExporerService.js';
import { ICodeseekSettingsService } from '../common/codeseekSettingsService.js';

const getLeadingWhitespacePx = (editor: ICodeEditor, startLine: number): number => {

	const model = editor.getModel();
	if (!model) {
		return 0;
	}

	// Get the line content, defaulting to empty string if line doesn't exist
	const lineContent = model.getLineContent(startLine) || '';

	// Find the first non-whitespace character
	const firstNonWhitespaceIndex = lineContent.search(/\S/);

	// Extract leading whitespace, handling case where line is all whitespace
	const leadingWhitespace = firstNonWhitespaceIndex === -1
		? lineContent
		: lineContent.slice(0, firstNonWhitespaceIndex);

	// Get font information from editor render options
	const { tabSize: numSpacesInTab } = model.getFormattingOptions();
	const spaceWidth = editor.getOption(EditorOption.fontInfo).spaceWidth;
	const tabWidth = numSpacesInTab * spaceWidth;

	let paddingLeft = 0;
	for (const char of leadingWhitespace) {
		if (char === '\t') {
			paddingLeft += tabWidth;
		} else if (char === ' ') {
			paddingLeft += spaceWidth;
		}
	}

	return paddingLeft;
};



// finds block.orig in fileContents and return its range in file
// startingAtLine is 1-indexed and inclusive
const findTextInCode = (text: string, fileContents: string, startingAtLine?: number) => {
	const idx = fileContents.indexOf(text,
		startingAtLine !== undefined ?
			fileContents.split('\n').slice(0, startingAtLine).join('\n').length // num characters in all lines before startingAtLine
			: 0
	);
	if (idx === -1) return 'Not found' as const;
	const lastIdx = fileContents.lastIndexOf(text);
	if (lastIdx !== idx) return 'Not unique' as const;
	const startLine = fileContents.substring(0, idx).split('\n').length;
	const numLines = text.split('\n').length;
	const endLine = startLine + numLines - 1;
	return [startLine, endLine] as const;
};


export type URIStreamState = 'idle' | 'acceptRejectAll' | 'streaming';


export type StartApplyingOpts = {
	from: 'QuickEdit';
	type: 'rewrite';
	diffareaid: number; // id of the CtrlK area (contains text selection)
} | {
	from: 'ClickApply';
	type: 'searchReplace' | 'rewrite';
	applyStr: string;
	uri: URI;
	applyBoxId?: string;
} | {
	from: 'QuickEdit';
	type: 'explain';
	instructions: string;
	uri: URI;
	startLine: number;
	endLine: number;
};



export type AddCtrlKOpts = {
	startLine: number;
	endLine: number;
	editor: ICodeEditor;
};

export type AddTipOpts = {
	tipArea: TipArea;
	editor: ICodeEditor;
};


export type Diff = {
	diffid: number;
	diffareaid: number; // the diff area this diff belongs to, "computed"
} & ComputedDiff;


type CommonZoneProps = {
	diffareaid: number;
	startLine: number;
	endLine: number;

	_URI: URI; // typically we get the URI from model

};

type BlankLineTipZone = {
	type: 'BlankLineTipZone';
	lineNumber: number;
	column: number;
};

type TextSelectionTipZone = {
	type: 'TextSelectionTipZone';
	startLine: number;
	endLine: number;
};


type CtrlKZone = {
	type: 'CtrlKZone';
	originalCode?: undefined;

	editorId: string; // the editor the input lives on

	_mountInfo: null | {
		textAreaRef: { current: HTMLTextAreaElement | null };
		selections: { current: StagingSelectionItem[] }
		dispose: () => void;
		refresh: () => void;
	};

	_linkedStreamingDiffZone: number | null; // diffareaid of the diffZone currently streaming here
	_removeStylesFns: Set<Function>; // these don't remove diffs or this diffArea, only their styles

} & CommonZoneProps;


type DiffZone = {
	type: 'DiffZone';
	originalCode: string;
	_diffOfId: Record<string, Diff>; // diffid -> diff in this DiffArea
	_streamState: {
		isStreaming: true;
		streamRequestIdRef: { current: string | null };
		line: number;
		applyBoxId?: string;
	} | {
		isStreaming: false;
		streamRequestIdRef?: undefined;
		line?: undefined;
		applyBoxId?: string;
	};
	editorId?: undefined;
	linkedStreamingDiffZone?: undefined;
	_removeStylesFns: Set<Function>; // these don't remove diffs or this diffArea, only their styles
} & CommonZoneProps;



type TrackingZone<T> = {
	type: 'TrackingZone';
	metadata: T;
	originalCode?: undefined;
	editorId?: undefined;
	_removeStylesFns?: undefined;
} & CommonZoneProps;


// called DiffArea for historical purposes, we can rename to something like TextRegion if we want
type DiffArea = CtrlKZone | DiffZone | TrackingZone<any>;
type TipArea = BlankLineTipZone | TextSelectionTipZone;

const diffAreaSnapshotKeys = [
	'type',
	'diffareaid',
	'originalCode',
	'startLine',
	'endLine',
	'editorId',

] as const satisfies (keyof DiffArea)[];

type DiffAreaSnapshot<DiffAreaType extends DiffArea = DiffArea> = Pick<DiffAreaType, typeof diffAreaSnapshotKeys[number]>;



type HistorySnapshot = {
	snapshottedDiffAreaOfId: Record<string, DiffAreaSnapshot>;
	entireFileCode: string;
};

const tipTypes = ['BlankLineTipZone', 'TextSelectionTipZone'] as const;



// line/col is the location, originalCodeStartLine is the start line of the original code being displayed
type StreamLocationMutable = { line: number; col: number; addedSplitYet: boolean; originalCodeStartLine: number };


export interface IEditCodeService {
	readonly _serviceBrand: undefined;
	startApplying(opts: StartApplyingOpts): Promise<URI | null>;

	addCtrlKZone(opts: AddCtrlKOpts): number | undefined;
	addTipZone(opts: AddTipOpts): void;
	removeCtrlKZone(opts: { diffareaid: number }): void;
	removeDiffAreas(opts: { uri: URI; removeCtrlKs: boolean; behavior: 'reject' | 'accept' }): void;
	disposeTip(type?: (typeof tipTypes)[number]): void;

	// CtrlKZone streaming state
	isCtrlKZoneStreaming(opts: { diffareaid: number }): boolean;
	interruptCtrlKStreaming(opts: { diffareaid: number }): void;
	onDidChangeCtrlKZoneStreaming: Event<{ uri: URI; diffareaid: number }>;

	// // DiffZone codeBoxId streaming state
	getURIStreamState(opts: { uri: URI | null, applyBoxId?: string }): URIStreamState;
	interruptURIStreaming(opts: { uri: URI }): void;
	onDidChangeURIStreamState: Event<{ uri: URI; state: URIStreamState }>;
	onDidChangeDiffCount: Event<{ uri: URI; currentIndex: number; totalDiffs: number }>;
	openOrCreateFile(uri: URI): Promise<void>;
	acceptDiffAtIndex(opts: { uri: URI; index: number }): void;
	rejectDiffAtIndex(opts: { uri: URI; index: number }): void;
	getCurrentDiffIndex(): number;
}

export const IEditCodeService = createDecorator<IEditCodeService>('editCodeService');

class EditCodeService extends Disposable implements IEditCodeService {
	_serviceBrand: undefined;

	// URI <--> model
	diffAreasOfURI: Record<string, Set<string>> = {};

	diffAreaOfId: Record<string, DiffArea> = {};
	diffOfId: Record<string, Diff> = {}; // redundant with diffArea._diffs

	createFilePaths = new Set<string>();
	// only applies to diffZones
	// streamingDiffZones: Set<number> = new Set()
	private readonly _onDidChangeDiffZoneStreaming = new Emitter<{ uri: URI; diffareaid: number; originCode?: string }>();
	private readonly _onDidAddOrDeleteDiffZones = new Emitter<{ uri: URI; originCode?: string }>();

	private readonly _onDidChangeCtrlKZoneStreaming = new Emitter<{ uri: URI; diffareaid: number }>();
	onDidChangeCtrlKZoneStreaming = this._onDidChangeCtrlKZoneStreaming.event;

	private readonly _onDidChangeURIStreamState = new Emitter<{ uri: URI; state: URIStreamState }>();
	onDidChangeURIStreamState = this._onDidChangeURIStreamState.event;

	private tips: (BlankLineTipWidget | TextSelectionTipWidget)[] = [];

	// 在类定义部分添加新的事件发射器
	private readonly _onDidChangeDiffCount = new Emitter<{ uri: URI; currentIndex: number; totalDiffs: number }>();
	onDidChangeDiffCount = this._onDidChangeDiffCount.event;

	// 存储排序后的差异
	private _sortedDiffs: Record<string, Diff[]> = {};
	// 当前的diff索引
	private _currentDiffIndex: number = 0;

	constructor(
		// @IHistoryService private readonly _historyService: IHistoryService, // history service is the history of pressing alt left/right
		@ICodeEditorService private readonly _codeEditorService: ICodeEditorService,
		@IModelService private readonly _modelService: IModelService,
		@IUndoRedoService private readonly _undoRedoService: IUndoRedoService, // undoRedo service is the history of pressing ctrl+z
		@ILLMMessageService private readonly _llmMessageService: ILLMMessageService,
		@IConsistentItemService private readonly _consistentItemService: IConsistentItemService,
		@IInstantiationService private readonly _instantiationService: IInstantiationService,
		@IConsistentEditorItemService private readonly _consistentEditorItemService: IConsistentEditorItemService,
		@IMetricsService private readonly _metricsService: IMetricsService,
		@INotificationService private readonly _notificationService: INotificationService,
		@ICommandService private readonly _commandService: ICommandService,
		@ICodeseekFileService private readonly _codeseekFileService: ICodeseekFileService,
		@ISidebarStateService private readonly _sidebarStateService: ISidebarStateService,
		@IQuickEditStateService private readonly _quickEditStateService: IQuickEditStateService,
		@ICodeseekLogger private readonly _codeseekLogService: ICodeseekLogger,
		@ILifecycleService private readonly _lifecycleService: ILifecycleService,
		@IFileService private readonly _fileService: IFileService,
		@IWorkspaceContextService private readonly _workspaceService: IWorkspaceContextService,
		@IEditorService private readonly _editorService: IEditorService,
		@ICodeSeekExporerService private readonly _codeseekExporerService: ICodeSeekExporerService,
		@ICodeseekSettingsService private readonly _codeseekSettingsService: ICodeseekSettingsService,
	) {
		super();

		// this function initializes data structures and listens for changes
		const initializeModel = (model: ITextModel) => {
			if (!(model.uri.fsPath in this.diffAreasOfURI)) {
				this.diffAreasOfURI[model.uri.fsPath] = new Set();
				this._sortedDiffs[model.uri.fsPath] = [];
			}
			else return; // do not add listeners to the same model twice - important, or will see duplicates

			// when the user types, realign diff areas and re-render them
			this._register(
				model.onDidChangeContent(e => {
					// it's as if we just called _write, now all we need to do is realign and refresh
					if (this.weAreWriting) return;
					const uri = model.uri;
					this._onUserChangeContent(uri, e);
				})
			);

			// when a stream starts or ends, fire the event for onDidChangeURIStreamState
			let prevStreamState = this.getURIStreamState({ uri: model.uri });
			const updateAcceptRejectAllUI = () => {
				const state = this.getURIStreamState({ uri: model.uri });
				const prevStateActual = prevStreamState;
				prevStreamState = state;
				if (state === prevStateActual) return;
				this._onDidChangeURIStreamState.fire({ uri: model.uri, state });
			};


			let _removeAcceptRejectAllUI: (() => void) | null = null;
			this._register(this._onDidChangeURIStreamState.event(({ uri, state }) => {
				if (uri.fsPath !== model.uri.fsPath) return;
				if (state === 'acceptRejectAll') {
					if (!_removeAcceptRejectAllUI)
						_removeAcceptRejectAllUI = this._addAcceptRejectAllUI(model.uri) ?? null;
				} else {
					_removeAcceptRejectAllUI?.();
					_removeAcceptRejectAllUI = null;
				}
			}));
			this._register(this._onDidChangeDiffZoneStreaming.event(({ uri: uri_, originCode }) => {
				if (uri_.fsPath === model.uri.fsPath) {
					if (originCode ? model.getValue() !== originCode : true) {
						updateAcceptRejectAllUI();
					} else {
						this.removeDiffAreas({ uri: model.uri, behavior: 'reject', removeCtrlKs: false });
					}
				}
			}));
			this._register(this._onDidAddOrDeleteDiffZones.event(({ uri: uri_, originCode }) => {
				if (uri_.fsPath === model.uri.fsPath) {
					if (originCode ? model.getValue() !== originCode : true) {
						updateAcceptRejectAllUI();
					} else {
						this.removeDiffAreas({ uri: model.uri, behavior: 'reject', removeCtrlKs: false });
					}
				}
			}));


		};
		// initialize all existing models + initialize when a new model mounts
		for (const model of this._modelService.getModels()) { initializeModel(model); }
		this._register(this._modelService.onModelAdded(model => initializeModel(model)));
		// 当模型被移除时，清理该URI的所有diff区域
		this._register(this._modelService.onModelRemoved(model => {
			const uri = model.uri;
			this.clearAllDiffAreasInEditor(uri);
		}));



		// this function adds listeners to refresh styles when editor changes tab
		const initializeEditor = (editor: ICodeEditor) => {
			const uri = editor.getModel()?.uri ?? null;
			if (uri) this._refreshStylesAndDiffsInURI(uri);
		};
		// add listeners for all existing editors + listen for editor being added
		for (const editor of this._codeEditorService.listCodeEditors()) { initializeEditor(editor); }
		this._register(this._codeEditorService.onCodeEditorAdd(editor => { initializeEditor(editor); }));

		// 监听应用关闭事件，清理所有diff区域
		this._register(this._lifecycleService.onWillShutdown(() => {
			this.clearAllDiffAreasInWorkspace();
		}));
	}

	private clearAllDiffAreasInEditor(uri: URI): void {
		if (this.diffAreasOfURI[uri.fsPath]?.size > 0) {
			this._codeseekLogService.info(`the editor is closed, clear the diff areas: ${uri.fsPath}`);
			this._deleteAllDiffAreas(uri);
			this.diffAreasOfURI[uri.fsPath].clear();
			this._onDidAddOrDeleteDiffZones.fire({ uri });
		}
	}

	/**
	 * 清理工作区中所有URI的diff区域
	 */
	private clearAllDiffAreasInWorkspace(): void {
		// 获取所有URI并清理相关diff区域
		for (const uriPath in this.diffAreasOfURI) {
			if (this.diffAreasOfURI[uriPath]?.size > 0) {
				this.clearAllDiffAreasInEditor(URI.parse(uriPath));
			}
		}
	}


	private _onUserChangeContent(uri: URI, e: IModelContentChangedEvent) {
		for (const change of e.changes) {
			this._realignAllDiffAreasLines(uri, change.text, change.range);
		}
		this._refreshStylesAndDiffsInURI(uri);
	}

	private _onInternalChangeContent(uri: URI, { shouldRealign }: { shouldRealign: false | { newText: string; oldRange: IRange } }) {
		if (shouldRealign) {
			const { newText, oldRange } = shouldRealign;
			// console.log('realiging', newText, oldRange)
			this._realignAllDiffAreasLines(uri, newText, oldRange);
		}
		this._refreshStylesAndDiffsInURI(uri);

	}

	private _notifyError = (e: Parameters<OnError>[0]) => {
		const details = errorDetails(e.fullError);
		this._notificationService.notify({
			severity: Severity.Warning,
			message: `Flow Error: ${e.message}`,
			actions: {
				secondary: [{
					id: 'codeseek.onerror.opensettings',
					enabled: true,
					label: `Open flow settings`,
					tooltip: '',
					class: undefined,
					run: () => { this._commandService.executeCommand(CODESEEK_OPEN_SETTINGS_ACTION_ID); }
				}]
			},
			source: details ? `(Hold ${isMacintosh ? 'Option' : 'Alt'} to hover) - ${details}\n\nIf this persists, feel free to [report](https://github.com/codeseekeditor/codeseek/issues/new) it.` : undefined
		});
	};



	// highlight the region
	private _addLineDecoration = (model: ITextModel | null, startLine: number, endLine: number, className: string, options?: Partial<IModelDecorationOptions>) => {
		if (model === null) return;
		const id = model.changeDecorations(accessor => accessor.addDecoration(
			{ startLineNumber: startLine, startColumn: 1, endLineNumber: endLine, endColumn: Number.MAX_SAFE_INTEGER },
			{
				className: className,
				description: className,
				isWholeLine: true,
				...options
			}));
		const disposeHighlight = () => {
			if (id && !model.isDisposed()) model.changeDecorations(accessor => accessor.removeDecoration(id));
		};
		return disposeHighlight;
	};


	private _addDiffAreaStylesToURI = (uri: URI) => {
		const model = this._getModel(uri);

		for (const diffareaid of this.diffAreasOfURI[uri.fsPath] || []) {
			const diffArea = this.diffAreaOfId[diffareaid];
			if (!diffArea) continue;
			if (diffArea.type === 'DiffZone') {
				// add sweep styles to the diffZone
				if (diffArea._streamState.isStreaming) {
					// sweepLine ... sweepLine
					const fn1 = this._addLineDecoration(model, diffArea._streamState.line, diffArea._streamState.line, 'codeseek-sweepIdxBG');
					// sweepLine+1 ... endLine
					const fn2 = diffArea._streamState.line + 1 <= diffArea.endLine ?
						this._addLineDecoration(model, diffArea._streamState.line + 1, diffArea.endLine, 'codeseek-sweepBG')
						: null;
					diffArea._removeStylesFns.add(() => { fn1?.(); fn2?.(); });
				}
			}

			else if (diffArea.type === 'CtrlKZone' && diffArea._linkedStreamingDiffZone === null) {
				// highlight zone's text
				const fn = this._addLineDecoration(model, diffArea.startLine, diffArea.endLine, 'codeseek-highlightBG');
				diffArea._removeStylesFns.add(() => fn?.());
			}
		}
	};


	private _computeDiffsAndAddStylesToURI = (uri: URI) => {
		const fullFileText = this._readURI(uri) ?? '';

		for (const diffareaid of this.diffAreasOfURI[uri.fsPath] || []) {
			const diffArea = this.diffAreaOfId[diffareaid];
			if (!diffArea) continue;
			if (diffArea.type !== 'DiffZone') continue;

			const newDiffAreaCode = fullFileText.split('\n').slice((diffArea.startLine - 1), (diffArea.endLine - 1) + 1).join('\n');
			const computedDiffs = findDiffs(diffArea.originalCode, newDiffAreaCode);
			for (const computedDiff of computedDiffs) {
				if (computedDiff.type === 'deletion') {
					computedDiff.startLine += diffArea.startLine - 1;
				}
				if (computedDiff.type === 'edit' || computedDiff.type === 'insertion') {
					computedDiff.startLine += diffArea.startLine - 1;
					computedDiff.endLine += diffArea.startLine - 1;
				}
				this._addDiff(computedDiff, diffArea);
			}
			this._sortedDiffs[uri.fsPath].sort((a, b) => a.startLine - b.startLine);
		}

		// 触发事件通知，确保UI状态更新
		if (Object.keys(this.diffAreasOfURI[uri.fsPath] || {}).length > 0) {
			this._onDidAddOrDeleteDiffZones.fire({ uri });
		}
	};

	private _addAcceptRejectAllUI(uri: URI) {
		if (!this._sortedDiffs[uri.fsPath] || this._sortedDiffs[uri.fsPath].length === 0) {
			return;
		}

		// 检查是否有其他文件包含diff区域
		const hasNextFile = this._hasNextFileWithDiffs(uri);

		const consistentItemId = this._consistentItemService.addConsistentItemToURI({
			uri,
			fn: (editor) => {
				const buttonsWidget = new AcceptAllRejectAllWidget({
					editor,
					onAcceptAll: () => {
						this.removeDiffAreas({ uri, behavior: 'accept', removeCtrlKs: false });
						// this._metricsService.capture('Accept All', {});
					},
					onRejectAll: () => {
						this.removeDiffAreas({ uri, behavior: 'reject', removeCtrlKs: false });
						this.shouldDeleteFileOnReject(uri);
						// this._metricsService.capture('Reject All', {});
					},
					onNextDiff: (step: number) => {
						const result = this._navigateToNextDiff(uri, editor, step);
						if (result) {
							buttonsWidget.updateDiffCounter(result.currentIndex, result.totalDiffs);
						}
						// this._metricsService.capture('Navigate Diff', { step });
					},
					onNextFile: () => {
						this._navigateToNextFileWithDiffs(uri);
						// this._metricsService.capture('Navigate Next File', {});
					},
					hasNextFile: hasNextFile,
					onDidChangeDiffCount: this.onDidChangeDiffCount
				});

				setTimeout(() => {
					this._updateDiffCount(uri);
				}, 0);

				return () => { buttonsWidget.dispose(); };
			}
		});

		return () => { this._consistentItemService.removeConsistentItemFromURI(consistentItemId); };
	}

	/**
	 * 检查是否有其他文件包含diff区域
	 * @param currentUri 当前文件URI
	 * @returns 是否有其他文件包含diff区域
	 */
	private _hasNextFileWithDiffs(currentUri: URI): boolean {
		for (const uriPath in this.diffAreasOfURI) {
			if (uriPath === currentUri.fsPath) continue;

			const diffAreas = this.diffAreasOfURI[uriPath];
			if (!diffAreas || diffAreas.size === 0) continue;

			// 检查是否有非流式传输的DiffZone
			for (const diffareaid of diffAreas) {
				const diffArea = this.diffAreaOfId[diffareaid];
				if (diffArea?.type === 'DiffZone' && !diffArea._streamState.isStreaming) {
					return true;
				}
			}
		}
		return false;
	}

	/**
	 * 导航到下一个包含diff区域的文件
	 * @param currentUri 当前文件URI
	 */
	private _navigateToNextFileWithDiffs(currentUri: URI): void {
		// 收集所有包含diff的文件URI
		const urisWithDiffs: URI[] = [];
		for (const uriPath in this.diffAreasOfURI) {
			if (uriPath === currentUri.fsPath) continue;

			const diffAreas = this.diffAreasOfURI[uriPath];
			if (!diffAreas || diffAreas.size === 0) continue;

			// 检查是否有非流式传输的DiffZone
			let hasDiffZone = false;
			for (const diffareaid of diffAreas) {
				const diffArea = this.diffAreaOfId[diffareaid];
				if (diffArea?.type === 'DiffZone' && !diffArea._streamState.isStreaming) {
					hasDiffZone = true;
					break;
				}
			}

			if (hasDiffZone) {
				urisWithDiffs.push(URI.parse(uriPath));
			}
		}

		if (urisWithDiffs.length === 0) return;

		// 打开第一个包含diff的文件
		this._editorService.openEditor({ resource: urisWithDiffs[0] });
	}

	// 导航到下一个或上一个差异
	private _navigateToNextDiff(uri: URI, editor: ICodeEditor, step: number): { currentIndex: number; totalDiffs: number } | null {
		// 直接使用已排序的差异数组
		const allDiffs = this._sortedDiffs[uri.fsPath];
		if (!allDiffs || allDiffs.length === 0) {
			return null;
		}

		// 获取当前光标位置
		const position = editor.getPosition();
		if (!position) return null;

		// 查找当前位置之后/之前的下一个差异
		let targetDiff: Diff | undefined;

		if (step > 0) { // 向下导航
			targetDiff = allDiffs.find(diff => diff.startLine > position.lineNumber);
			if (!targetDiff && allDiffs.length > 0) {
				// 如果没有找到，则循环到第一个差异
				targetDiff = allDiffs[0];
			}
		} else { // 向上导航
			// 反向查找
			for (let i = allDiffs.length - 1; i >= 0; i--) {
				const diff = allDiffs[i];
				// 使用startLine作为比较点，避免类型问题
				if (diff.startLine < position.lineNumber) {
					targetDiff = diff;
					break;
				}
			}
			if (!targetDiff && allDiffs.length > 0) {
				// 如果没有找到，则循环到最后一个差异
				targetDiff = allDiffs[allDiffs.length - 1];
			}
		}

		// 如果找到目标差异，则滚动到该位置
		if (targetDiff) {
			editor.revealLineInCenter(targetDiff.startLine, ScrollType.Smooth);
			editor.setPosition({ lineNumber: targetDiff.startLine, column: 1 });
			this._currentDiffIndex = allDiffs.indexOf(targetDiff);
		}

		const result = { currentIndex: this._currentDiffIndex, totalDiffs: allDiffs.length };

		// 触发事件
		this._onDidChangeDiffCount.fire({ uri, currentIndex: result.currentIndex, totalDiffs: result.totalDiffs });

		return result;
	}

	mostRecentTextOfCtrlKZoneId: Record<string, string | undefined> = {};
	mostSelectionsOfCtrlKZoneId: Record<string, StagingSelectionItem[] | undefined> = {}
	private _addCtrlKZoneInput = (ctrlKZone: CtrlKZone) => {
		const { editorId } = ctrlKZone;
		const editor = this._codeEditorService.listCodeEditors().find(e => e.getId() === editorId);
		if (!editor) { return null; }

		let zoneId: string | null = null;
		let viewZone_: IViewZone | null = null;
		const textAreaRef: { current: HTMLTextAreaElement | null } = { current: null };
		const stagingSelections: { current: StagingSelectionItem[] } = { current: [] }


		const paddingLeft = getLeadingWhitespacePx(editor, ctrlKZone.startLine);

		const itemId = this._consistentEditorItemService.addToEditor(editor, () => {
			const domNode = document.createElement('div');
			domNode.style.zIndex = '1';
			domNode.style.height = 'auto';
			domNode.style.paddingLeft = `${paddingLeft}px`;
			const viewZone: IViewZone = {
				afterLineNumber: ctrlKZone.startLine - 1,
				domNode: domNode,
				// heightInPx: 80,
				suppressMouseDown: false,
				showInHiddenAreas: true,
			};
			viewZone_ = viewZone;

			// mount zone
			editor.changeViewZones(accessor => {
				zoneId = accessor.addZone(viewZone);
			});

			// mount react
			this._instantiationService.invokeFunction(accessor => {
				mountCtrlK(domNode, accessor, {

					diffareaid: ctrlKZone.diffareaid,

					textAreaRef: (r) => {
						textAreaRef.current = r;
						if (!textAreaRef.current) return;

						if (!(ctrlKZone.diffareaid in this.mostRecentTextOfCtrlKZoneId)) { // detect first mount this way (a hack)
							this.mostRecentTextOfCtrlKZoneId[ctrlKZone.diffareaid] = undefined;
							setTimeout(() => textAreaRef.current?.focus(), 100);
						}
					},
					onChangeHeight(height) {
						if (height === 0) return; // the viewZone sets this height to the container if it's out of view, ignore it
						viewZone.heightInPx = height;
						// re-render with this new height
						editor.changeViewZones(accessor => {
							if (zoneId) accessor.layoutZone(zoneId);
						});
					},
					onChangeText: (text) => {
						this.mostRecentTextOfCtrlKZoneId[ctrlKZone.diffareaid] = text;
					},
					onClose: () => {
						this._quickEditStateService.setState({ isOpenEdit: false });
						this._quickEditStateService.fireChangeState();
					},
					setSelections: (selections) => {
						this.mostSelectionsOfCtrlKZoneId[ctrlKZone.diffareaid] = [...selections]
						stagingSelections.current = [...selections]
					},
					selections: this.mostSelectionsOfCtrlKZoneId[ctrlKZone.diffareaid] ?? [],
					initText: this.mostRecentTextOfCtrlKZoneId[ctrlKZone.diffareaid] ?? null,
				} satisfies QuickEditPropsType);

			});

			return () => editor.changeViewZones(accessor => {
				if (zoneId)
					accessor.removeZone(zoneId);
			});
		});

		return {
			textAreaRef,
			selections: stagingSelections,
			refresh: () => editor.changeViewZones(accessor => {
				if (zoneId && viewZone_) {
					viewZone_.afterLineNumber = ctrlKZone.startLine - 1;
					accessor.layoutZone(zoneId);
				}
			}),
			dispose: () => {
				this._consistentEditorItemService.removeFromEditor(itemId);
			},
		} satisfies CtrlKZone['_mountInfo'];
	};

	private _refreshCtrlKInputs = async (uri: URI) => {
		for (const diffareaid of this.diffAreasOfURI[uri.fsPath] || []) {
			const diffArea = this.diffAreaOfId[diffareaid];
			if (!diffArea) continue;
			if (diffArea.type !== 'CtrlKZone') continue;
			if (!diffArea._mountInfo) {
				diffArea._mountInfo = this._addCtrlKZoneInput(diffArea);
			}
			else {
				diffArea._mountInfo.refresh();
			}
		}
	};

	private _addDiffStylesToURI = (uri: URI, diff: Diff) => {
		const { type, diffid } = diff;

		const disposeInThisEditorFns: (() => void)[] = [];

		const model = this._modelService.getModel(uri);

		// green decoration and minimap decoration
		if (type !== 'deletion') {
			const fn = this._addLineDecoration(model, diff.startLine, diff.endLine, 'codeseek-greenBG', {
				minimap: { color: { id: 'minimapGutter.addedBackground' }, position: 2 },
				overviewRuler: { color: { id: 'editorOverviewRuler.addedForeground' }, position: 7 }
			});
			disposeInThisEditorFns.push(() => { fn?.(); });
		}


		// red in a view zone
		if (type !== 'insertion') {
			const consistentZoneId = this._consistentItemService.addConsistentItemToURI({
				uri,
				fn: (editor) => {

					const domNode = document.createElement('div');
					domNode.className = 'codeseek-redBG';

					const renderOptions = RenderOptions.fromEditor(editor);

					const processedText = diff.originalCode.replace(/\t/g, ' '.repeat(renderOptions.tabSize));

					const lines = processedText.split('\n');

					const linesContainer = document.createElement('div');
					linesContainer.style.fontFamily = renderOptions.fontInfo.fontFamily;
					linesContainer.style.fontSize = `${renderOptions.fontInfo.fontSize}px`;
					linesContainer.style.lineHeight = `${renderOptions.fontInfo.lineHeight}px`;
					// linesContainer.style.tabSize = `${tabWidth}px` // \t
					linesContainer.style.whiteSpace = 'pre';
					linesContainer.style.position = 'relative';
					linesContainer.style.width = '100%';

					lines.forEach(line => {
						// div for current line
						const lineDiv = document.createElement('div');
						lineDiv.className = 'view-line';
						lineDiv.style.whiteSpace = 'pre';
						lineDiv.style.position = 'relative';
						lineDiv.style.height = `${renderOptions.fontInfo.lineHeight}px`;

						// span (this is just how vscode does it)
						const span = document.createElement('span');
						span.textContent = line || '\u00a0';
						span.style.whiteSpace = 'pre';
						span.style.display = 'inline-block';

						lineDiv.appendChild(span);
						linesContainer.appendChild(lineDiv);
					});

					domNode.appendChild(linesContainer);

					// Calculate height based on number of lines and line height
					const heightInLines = lines.length;
					const minWidthInPx = Math.max(...lines.map(line =>
						Math.ceil(renderOptions.fontInfo.typicalFullwidthCharacterWidth * line.length)
					));

					const viewZone: IViewZone = {
						afterLineNumber: diff.startLine - 1,
						heightInLines,
						minWidthInPx,
						domNode,
						marginDomNode: document.createElement('div'),
						suppressMouseDown: false,
						showInHiddenAreas: false,
					};

					let zoneId: string | null = null;
					editor.changeViewZones(accessor => { zoneId = accessor.addZone(viewZone); });
					return () => editor.changeViewZones(accessor => { if (zoneId) accessor.removeZone(zoneId); });
				},
			});

			disposeInThisEditorFns.push(() => { this._consistentItemService.removeConsistentItemFromURI(consistentZoneId); });

		}



		const diffZone = this.diffAreaOfId[diff.diffareaid];
		if (diffZone.type === 'DiffZone' && !diffZone._streamState.isStreaming) {
			// Accept | Reject widget
			const consistentWidgetId = this._consistentItemService.addConsistentItemToURI({
				uri,
				fn: (editor) => {
					let startLine: number;
					let endLine: number;
					let offsetLines: number;
					if (diff.type === 'insertion' || diff.type === 'edit') {
						startLine = diff.startLine; // green start
						endLine = diff.endLine;
						offsetLines = 0;
					}
					else if (diff.type === 'deletion') {
						// if diff.startLine is out of bounds
						if (diff.originalStartLine === editor.getModel()?.getLineCount()) {
							const numRedLines = diff.originalEndLine - diff.originalStartLine + 1;
							startLine = diff.originalStartLine;
							endLine = diff.originalEndLine;
							offsetLines = -numRedLines;
						}
						else {
							startLine = diff.originalStartLine;
							endLine = diff.originalEndLine;
							offsetLines = 1;
						}
					}
					else { throw 1; }

					const buttonsWidget = new AcceptRejectWidget({
						editor,
						onAccept: () => {
							this.acceptDiff({ diffid });
							// this._metricsService.capture('Accept Diff', {});
						},
						onReject: () => {
							this.rejectDiff({ diffid });
							// this._metricsService.capture('Reject Diff', {});
						},
						diffid: diffid.toString(),
						startLine,
						endLine,
						offsetLines
					});
					return () => { buttonsWidget.dispose(); };
				}
			});
			disposeInThisEditorFns.push(() => { this._consistentItemService.removeConsistentItemFromURI(consistentWidgetId); });
		}

		const disposeInEditor = () => { disposeInThisEditorFns.forEach(f => f()); };
		return disposeInEditor;

	};

	public async shouldDeleteFileOnReject(uri: URI) {
		if (this.createFilePaths.has(uri.fsPath)) {
			// Use a more robust file deletion approach with proper error handling
			try {
				// First find and close any open editors for this file
				const editorsToClose = this._editorService.findEditors(uri);
				if (editorsToClose.length > 0) {
					this._codeseekLogService.info(`Closing editors for file before deletion: ${uri.fsPath}`);
					await this._editorService.closeEditors(editorsToClose);
				}

				// Check if we can delete the file
				const canDelete = await this._fileService.canDelete(uri, { useTrash: true, recursive: false });
				if (canDelete instanceof Error) {
					throw canDelete;
				}

				// Delete the file
				await this._fileService.del(uri, { useTrash: true, recursive: false });
				this.createFilePaths.delete(uri.fsPath);
				this._codeseekLogService.info(`Successfully deleted file: ${uri.fsPath}`);
			} catch (error) {
				this._codeseekLogService.error(`Failed to delete file: ${uri.fsPath}`, error);
			}
		};
	}


	public async openOrCreateFile(uri: URI): Promise<void> {
		try {
			const { workspaceUri } = getWorkspaceUri(this._workspaceService);
			const isInWorkspace = workspaceUri && uri.fsPath.startsWith(workspaceUri.fsPath);
			const exists = await this._fileService.exists(uri);

			// 如果文件不存在并且在工作区中才创建
			if (!exists && isInWorkspace) {
				await this._fileService.createFile(uri);
				this.createFilePaths.add(uri.fsPath);
			}
			await this._commandService.executeCommand("vscode.open", uri, { preview: true });
		} catch (error) {
			this._codeseekLogService.error("Failed to open or create file:", error);
		}
	};

	private _getModel(uri: URI) {
		const model = this._modelService.getModel(uri);
		if (!model || model.isDisposed()) {
			return null;
		}
		return model;
	}
	private _readURI(uri: URI, range?: IRange): string | null {
		if (!range) return this._getModel(uri)?.getValue(EndOfLinePreference.LF) ?? null;
		else return this._getModel(uri)?.getValueInRange(range, EndOfLinePreference.LF) ?? null;
	}
	private _getNumLines(uri: URI): number | null {
		return this._getModel(uri)?.getLineCount() ?? null;
	}
	private _getActiveEditorURI(): URI | null {
		const editor = this._codeEditorService.getActiveCodeEditor();
		if (!editor) return null;
		const uri = editor.getModel()?.uri;
		if (!uri) return null;
		return uri;
	}

	weAreWriting = false;
	private _writeText(uri: URI, text: string, range: IRange, { shouldRealignDiffAreas }: { shouldRealignDiffAreas: boolean }) {
		const model = this._getModel(uri);
		if (!model) return;
		const uriStr = this._readURI(uri, range);
		if (uriStr === null) return;


		// heuristic check if don't need to make edits
		const dontNeedToWrite = uriStr === text;
		if (dontNeedToWrite) {
			// at the end of a write, we still expect to refresh all styles
			// e.g. sometimes we expect to restore all the decorations even if no edits were made when _writeText is used
			this._refreshStylesAndDiffsInURI(uri);
			return;
		}

		// minimal edits so not so flashy
		// const edits = this.worker.$Codeseek_computeMoreMinimalEdits(uri.toString(), [{ range, text }], false)
		this.weAreWriting = true;
		model.applyEdits([{ range, text }]);
		this.weAreWriting = false;
		this._onInternalChangeContent(uri, { shouldRealign: shouldRealignDiffAreas && { newText: text, oldRange: range } });
	}

	private _addToHistory(uri: URI) {

		const getCurrentSnapshot = (): HistorySnapshot => {
			const snapshottedDiffAreaOfId: Record<string, DiffAreaSnapshot> = {};

			for (const diffareaid in this.diffAreaOfId) {
				const diffArea = this.diffAreaOfId[diffareaid];

				if (diffArea._URI.fsPath !== uri.fsPath) continue;

				snapshottedDiffAreaOfId[diffareaid] = structuredClone( // a structured clone must be on a JSON object
					Object.fromEntries(diffAreaSnapshotKeys.map(key => [key, diffArea[key]]))
				) as DiffAreaSnapshot;
			}
			return {
				snapshottedDiffAreaOfId,
				entireFileCode: this._readURI(uri) ?? '', // the whole file's code
			};
		};

		const restoreDiffAreas = (snapshot: HistorySnapshot) => {

			// for each diffarea in this uri, stop streaming if currently streaming
			for (const diffareaid in this.diffAreaOfId) {
				const diffArea = this.diffAreaOfId[diffareaid];
				if (diffArea.type === 'DiffZone')
					this._stopIfStreaming(diffArea);
			}

			// delete all diffareas on this uri (clearing their styles)
			this._deleteAllDiffAreas(uri);
			this.diffAreasOfURI[uri.fsPath].clear();

			const { snapshottedDiffAreaOfId, entireFileCode: entireModelCode } = structuredClone(snapshot); // don't want to destroy the snapshot

			// restore diffAreaOfId and diffAreasOfModelId
			for (const diffareaid in snapshottedDiffAreaOfId) {

				const snapshottedDiffArea = snapshottedDiffAreaOfId[diffareaid];

				if (snapshottedDiffArea.type === 'DiffZone') {
					this.diffAreaOfId[diffareaid] = {
						...snapshottedDiffArea as DiffAreaSnapshot<DiffZone>,
						type: 'DiffZone',
						_diffOfId: {},
						_URI: uri,
						_streamState: { isStreaming: false }, // when restoring, we will never be streaming
						_removeStylesFns: new Set(),
					};
				}
				else if (snapshottedDiffArea.type === 'CtrlKZone') {
					this.diffAreaOfId[diffareaid] = {
						...snapshottedDiffArea as DiffAreaSnapshot<CtrlKZone>,
						_URI: uri,
						_removeStylesFns: new Set<Function>(),
						_mountInfo: null,
						_linkedStreamingDiffZone: null, // when restoring, we will never be streaming
					};
				}
				this.diffAreasOfURI[uri.fsPath].add(diffareaid);
			}
			this._onDidAddOrDeleteDiffZones.fire({ uri });

			// restore file content
			const numLines = this._getNumLines(uri);
			if (numLines === null) return;


			this._writeText(uri, entireModelCode,
				{ startColumn: 1, startLineNumber: 1, endLineNumber: numLines, endColumn: Number.MAX_SAFE_INTEGER },
				{ shouldRealignDiffAreas: false }
			);

			this._computeDiffsAndAddStylesToURI(uri);
			this._refreshStylesAndDiffsInURI(uri);
			this._updateDiffCount(uri);
			const state = this.getURIStreamState({ uri });
			this._onDidChangeURIStreamState.fire({ uri, state });
		};

		const beforeSnapshot: HistorySnapshot = getCurrentSnapshot();
		let afterSnapshot: HistorySnapshot | null = null;

		const elt: IUndoRedoElement = {
			type: UndoRedoElementType.Resource,
			resource: uri,
			label: 'Codeseek Changes',
			code: 'undoredo.editCode',
			undo: () => { restoreDiffAreas(beforeSnapshot); },
			redo: () => { if (afterSnapshot) restoreDiffAreas(afterSnapshot); }
		};
		this._undoRedoService.pushElement(elt);

		const onFinishEdit = () => { afterSnapshot = getCurrentSnapshot(); };
		return { onFinishEdit };
	}

	// delete diffOfId and diffArea._diffOfId
	private _deleteDiff(diff: Diff) {
		const diffArea = this.diffAreaOfId[diff.diffareaid];
		if (diffArea.type !== 'DiffZone') return;
		delete diffArea._diffOfId[diff.diffid];
		delete this.diffOfId[diff.diffid];

		// 直接从排序数组中移除差异
		const uri = diffArea._URI;
		if (this._sortedDiffs[uri.fsPath]) {
			const index = this._sortedDiffs[uri.fsPath].findIndex(d => d.diffid === diff.diffid);
			if (index !== -1) {
				this._sortedDiffs[uri.fsPath].splice(index, 1);
			}
		}
	}

	private _deleteDiffs(diffZone: DiffZone) {
		for (const diffid in diffZone._diffOfId) {
			const diff = diffZone._diffOfId[diffid];
			this._deleteDiff(diff);
		}
	}

	private _clearAllDiffAreaEffects(diffArea: DiffArea) {
		// clear diffZone effects (diffs)
		if (diffArea.type === 'DiffZone')
			this._deleteDiffs(diffArea);

		diffArea._removeStylesFns?.forEach(removeStyles => removeStyles());
		diffArea._removeStylesFns?.clear();
	}


	// clears all Diffs (and their styles) and all styles of DiffAreas, etc
	private _clearAllEffects(uri: URI) {
		for (const diffareaid of this.diffAreasOfURI[uri.fsPath] || []) {
			const diffArea = this.diffAreaOfId[diffareaid];
			if (!diffArea) continue;
			this._clearAllDiffAreaEffects(diffArea);
		}
	}


	// delete all diffs, update diffAreaOfId, update diffAreasOfModelId
	private _deleteDiffZone(diffZone: DiffZone) {
		this._clearAllDiffAreaEffects(diffZone);
		delete this.diffAreaOfId[diffZone.diffareaid];
		this.diffAreasOfURI[diffZone._URI.fsPath].delete(diffZone.diffareaid.toString());
		this._onDidAddOrDeleteDiffZones.fire({ uri: diffZone._URI });
	}

	private _deleteTrackingZone(trackingZone: TrackingZone<unknown>) {
		delete this.diffAreaOfId[trackingZone.diffareaid];
		this.diffAreasOfURI[trackingZone._URI.fsPath].delete(trackingZone.diffareaid.toString());
	}

	private _deleteCtrlKZone(ctrlKZone: CtrlKZone) {
		this._clearAllEffects(ctrlKZone._URI);
		ctrlKZone._mountInfo?.dispose();
		delete this.diffAreaOfId[ctrlKZone.diffareaid];
		this.diffAreasOfURI[ctrlKZone._URI.fsPath].delete(ctrlKZone.diffareaid.toString());
	}


	private _deleteAllDiffAreas(uri: URI) {
		const diffAreas = this.diffAreasOfURI[uri.fsPath];
		diffAreas.forEach(diffareaid => {
			const diffArea = this.diffAreaOfId[diffareaid];
			if (diffArea.type === 'DiffZone')
				this._deleteDiffZone(diffArea);
			else if (diffArea.type === 'CtrlKZone')
				this._deleteCtrlKZone(diffArea);
		});
	}



	private _diffareaidPool = 0; // each diffarea has an id
	private _addDiffArea<T extends DiffArea>(diffArea: Omit<T, 'diffareaid'>): T {
		const diffareaid = this._diffareaidPool++;
		const diffArea2 = { ...diffArea, diffareaid } as T;
		this.diffAreasOfURI[diffArea2._URI.fsPath].add(diffareaid.toString());
		this.diffAreaOfId[diffareaid] = diffArea2;
		return diffArea2;
	}

	private _diffidPool = 0; // each diff has an id
	private _addDiff(computedDiff: ComputedDiff, diffZone: DiffZone): Diff {
		const uri = diffZone._URI;
		const diffid = this._diffidPool++;

		// create a Diff of it
		const newDiff: Diff = {
			...computedDiff,
			diffid: diffid,
			diffareaid: diffZone.diffareaid,
		};

		const fn = this._addDiffStylesToURI(uri, newDiff);
		diffZone._removeStylesFns.add(fn);

		this.diffOfId[diffid] = newDiff;
		diffZone._diffOfId[diffid] = newDiff;

		this._sortedDiffs[uri.fsPath].push(newDiff);
		// 当添加第一个diff时触发通知
		if (Object.keys(diffZone._diffOfId).length === 1) {
			this._onDidAddOrDeleteDiffZones.fire({ uri });
		}

		return newDiff;
	}

	// changes the start/line locations of all DiffAreas on the page (adjust their start/end based on the change) based on the change that was recently made
	private _realignAllDiffAreasLines(uri: URI, text: string, recentChange: { startLineNumber: number; endLineNumber: number }) {

		// console.log('recent change', recentChange)

		const model = this._getModel(uri);
		if (!model) return;

		// compute net number of newlines lines that were added/removed
		const startLine = recentChange.startLineNumber;
		const endLine = recentChange.endLineNumber;

		const newTextHeight = (text.match(/\n/g) || []).length + 1; // number of newlines is number of \n's + 1, e.g. "ab\ncd"

		// compute overlap with each diffArea and shrink/elongate each diffArea accordingly
		for (const diffareaid of this.diffAreasOfURI[model.uri.fsPath] || []) {
			const diffArea = this.diffAreaOfId[diffareaid];

			// if the diffArea is entirely above the range, it is not affected
			if (diffArea.endLine < startLine) {
				// console.log('CHANGE FULLY BELOW DA (doing nothing)')
				continue;
			}
			// if a diffArea is entirely below the range, shift the diffArea up/down by the delta amount of newlines
			else if (endLine < diffArea.startLine) {
				// console.log('CHANGE FULLY ABOVE DA')
				const changedRangeHeight = endLine - startLine + 1;
				const deltaNewlines = newTextHeight - changedRangeHeight;
				diffArea.startLine += deltaNewlines;
				diffArea.endLine += deltaNewlines;
			}
			// if the diffArea fully contains the change, elongate it by the delta amount of newlines
			else if (startLine >= diffArea.startLine && endLine <= diffArea.endLine) {
				// console.log('DA FULLY CONTAINS CHANGE')
				const changedRangeHeight = endLine - startLine + 1;
				const deltaNewlines = newTextHeight - changedRangeHeight;
				diffArea.endLine += deltaNewlines;
			}
			// if the change fully contains the diffArea, make the diffArea have the same range as the change
			else if (diffArea.startLine > startLine && diffArea.endLine < endLine) {
				// console.log('CHANGE FULLY CONTAINS DA')
				diffArea.startLine = startLine;
				diffArea.endLine = startLine + newTextHeight;
			}
			// if the change contains only the diffArea's top
			else if (startLine < diffArea.startLine && diffArea.startLine <= endLine) {
				// console.log('CHANGE CONTAINS TOP OF DA ONLY')
				const numOverlappingLines = endLine - diffArea.startLine + 1;
				const numRemainingLinesInDA = diffArea.endLine - diffArea.startLine + 1 - numOverlappingLines;
				const newHeight = (numRemainingLinesInDA - 1) + (newTextHeight - 1) + 1;
				diffArea.startLine = startLine;
				diffArea.endLine = startLine + newHeight;
			}
			// if the change contains only the diffArea's bottom
			else if (startLine <= diffArea.endLine && diffArea.endLine < endLine) {
				// console.log('CHANGE CONTAINS BOTTOM OF DA ONLY')
				const numOverlappingLines = diffArea.endLine - startLine + 1;
				diffArea.endLine += newTextHeight - numOverlappingLines;
			}
		}

	}

	private _refreshStylesAndDiffsInURI(uri: URI) {

		// 1. clear DiffArea styles and Diffs
		this._clearAllEffects(uri);

		// 2. style DiffAreas (sweep, etc)
		this._addDiffAreaStylesToURI(uri);

		// 3. add Diffs
		this._computeDiffsAndAddStylesToURI(uri);

		// 4. refresh ctrlK zones
		this._refreshCtrlKInputs(uri);
	}

	// @throttle(100)
	//处理流式传输的文本（例如从语言模型 LLM 生成的代码），并将其逐步写入指定文件（通过 uri 定位），
	// 同时保持与原始代码（originalCode）的同步。它使用差异计算（diff）来确定写入位置，并动态更新文件内容
	private _writeStreamedDiffZoneLLMText(uri: URI, originalCode: string, llmTextSoFar: string, deltaText: string, latestMutable: StreamLocationMutable) {

		let numNewLines = 0;

		// ----------- 1. Write the new code to the document -----------
		// figure out where to highlight based on where the AI is in the stream right now, use the last diff to figure that out
		const computedDiffs = findDiffs(originalCode, llmTextSoFar);

		// if streaming, use diffs to figure out where to write new code
		// these are two different coordinate systems - new and old line number
		let endLineInLlmTextSoFar: number; // get file[diffArea.startLine...newFileEndLine] with line=newFileEndLine highlighted
		let startLineInOriginalCode: number; // get original[oldStartingPoint...] (line in the original code, so starts at 1)

		const lastDiff = computedDiffs.pop();

		if (!lastDiff) {
			// console.log('!lastDiff')
			// if the writing is identical so far, display no changes
			startLineInOriginalCode = 1;
			endLineInLlmTextSoFar = 1;
		}
		else {
			startLineInOriginalCode = lastDiff.originalStartLine;
			if (lastDiff.type === 'insertion' || lastDiff.type === 'edit')
				endLineInLlmTextSoFar = lastDiff.endLine;
			else if (lastDiff.type === 'deletion')
				endLineInLlmTextSoFar = lastDiff.startLine;
			else
				throw new Error(`Codeseek: diff.type not recognized on: ${lastDiff}`);
		}

		// at the start, add a newline between the stream and originalCode to make reasoning easier
		if (!latestMutable.addedSplitYet) {
			this._writeText(uri, '\n',
				{ startLineNumber: latestMutable.line, startColumn: latestMutable.col, endLineNumber: latestMutable.line, endColumn: latestMutable.col, },
				{ shouldRealignDiffAreas: true }
			);
			latestMutable.addedSplitYet = true;
			numNewLines += 1;
		}

		// insert deltaText at latest line and col
		this._writeText(uri, deltaText,
			{ startLineNumber: latestMutable.line, startColumn: latestMutable.col, endLineNumber: latestMutable.line, endColumn: latestMutable.col },
			{ shouldRealignDiffAreas: true }
		);
		const deltaNumNewLines = deltaText.split('\n').length - 1;
		latestMutable.line += deltaNumNewLines;
		const lastNewlineIdx = deltaText.lastIndexOf('\n');
		latestMutable.col = lastNewlineIdx === -1 ? latestMutable.col + deltaText.length : deltaText.length - lastNewlineIdx;
		numNewLines += deltaNumNewLines;

		// delete or insert to get original up to speed
		if (latestMutable.originalCodeStartLine < startLineInOriginalCode) {
			// moved up, delete
			const numLinesDeleted = startLineInOriginalCode - latestMutable.originalCodeStartLine;
			this._writeText(uri, '',
				{ startLineNumber: latestMutable.line, startColumn: latestMutable.col, endLineNumber: latestMutable.line + numLinesDeleted, endColumn: Number.MAX_SAFE_INTEGER, },
				{ shouldRealignDiffAreas: true }
			);
			numNewLines -= numLinesDeleted;
		}
		else if (latestMutable.originalCodeStartLine > startLineInOriginalCode) {
			const newText = '\n' + originalCode.split('\n').slice((startLineInOriginalCode - 1), (latestMutable.originalCodeStartLine - 1) - 1 + 1).join('\n');
			this._writeText(uri, newText,
				{ startLineNumber: latestMutable.line, startColumn: latestMutable.col, endLineNumber: latestMutable.line, endColumn: latestMutable.col },
				{ shouldRealignDiffAreas: true }
			);
			numNewLines += newText.split('\n').length - 1;
		}
		latestMutable.originalCodeStartLine = startLineInOriginalCode;

		return { endLineInLlmTextSoFar, numNewLines }; // numNewLines here might not be correct....
	}

	// called first, then call startApplying
	public addCtrlKZone({ startLine, endLine, editor }: AddCtrlKOpts) {

		const uri = editor.getModel()?.uri;
		if (!uri) return;

		// check if there's overlap with any other ctrlKZone and if so, focus it
		const overlappingCtrlKZone = this._findOverlappingDiffArea({ startLine, endLine, uri, filter: (diffArea) => diffArea.type === 'CtrlKZone' });
		if (overlappingCtrlKZone) {
			editor.revealLine(overlappingCtrlKZone.startLine); // important
			setTimeout(() => (overlappingCtrlKZone as CtrlKZone)._mountInfo?.textAreaRef.current?.focus(), 100);
			return;
		}

		const overlappingDiffZone = this._findOverlappingDiffArea({ startLine, endLine, uri, filter: (diffArea) => diffArea.type === 'DiffZone' });
		if (overlappingDiffZone)
			return;

		editor.revealLine(startLine);
		editor.setSelection({ startLineNumber: startLine, endLineNumber: startLine, startColumn: 1, endColumn: 1 });

		const { onFinishEdit } = this._addToHistory(uri);

		const adding: Omit<CtrlKZone, 'diffareaid'> = {
			type: 'CtrlKZone',
			startLine: startLine,
			endLine: endLine,
			editorId: editor.getId(),
			_URI: uri,
			_removeStylesFns: new Set(),
			_mountInfo: null,
			_linkedStreamingDiffZone: null,
		};
		const ctrlKZone = this._addDiffArea(adding);
		this._refreshStylesAndDiffsInURI(uri);

		onFinishEdit();
		return ctrlKZone.diffareaid;
	}

	// _remove means delete and also add to history
	public removeCtrlKZone({ diffareaid }: { diffareaid: number }) {
		const ctrlKZone = this.diffAreaOfId[diffareaid];
		if (!ctrlKZone) return;
		if (ctrlKZone.type !== 'CtrlKZone') return;

		const uri = ctrlKZone._URI;
		const { onFinishEdit } = this._addToHistory(uri);
		this._deleteCtrlKZone(ctrlKZone);
		this._refreshStylesAndDiffsInURI(uri);
		onFinishEdit();
	}

	public addTipZone({ tipArea, editor }: AddTipOpts) {
		this.tips.forEach(tip => {
			tip.dispose();
		});
		this.tips = [];
		let widget: BlankLineTipWidget | TextSelectionTipWidget | undefined = undefined;

		// 检查是否在任何 diff 区域内
		const uri = editor.getModel()?.uri;
		if (uri) {
			const isInDiffArea = this._findOverlappingDiffArea({
				startLine: tipArea.type === 'BlankLineTipZone' ? tipArea.lineNumber : tipArea.startLine,
				endLine: tipArea.type === 'BlankLineTipZone' ? tipArea.lineNumber : tipArea.endLine,
				uri,
				filter: (diffArea) => diffArea.type === 'DiffZone'
			});
			// 如果在 diff 区域内，不显示提示框
			if (isInDiffArea) {
				return;
			}
		}

		if (tipArea.type === tipTypes[0]) {
			widget = new BlankLineTipWidget(
				{
					editor,
					lineNumber: tipArea.lineNumber,
					column: tipArea.column,
				},
				this
			);
		} else if (tipArea.type === tipTypes[1]) {
			const isOpenChat = this._sidebarStateService.isSidebarChatOpen();
			const isOpenEdit = this._quickEditStateService.isOpenEdit();
			const props = {
				editor,
				startLine: tipArea.startLine,
				endLine: tipArea.endLine,
				isOpenChat,
				isOpenEdit,
				onOpenChat: async () => {
					this.disposeTip('TextSelectionTipZone');
					await this._commandService.executeCommand(CODESEEK_OPEN_SIDEBAR_ACTION_ID);
					await this._commandService.executeCommand(CODESEEK_ADD_SELECTION_TO_SIDEBAR_ACTION_ID);
				},
				onOpenEdit: () => {
					this.disposeTip('TextSelectionTipZone');
					this._commandService.executeCommand(CODESEEK_CTRL_K_ACTION_ID);
					this._quickEditStateService.setState({ isOpenEdit: true });
				},
				onExplainCode: () => {
					this.disposeTip('TextSelectionTipZone');
					this._commandService.executeCommand(CODESEEK_EXPLAIN_CODE_ACTION_ID);
				},
			};

			widget = new TextSelectionTipWidget(props, this, this._sidebarStateService, this._quickEditStateService);
		}
		if (widget) {
			editor.addOverlayWidget(widget);
			this.tips.push(widget);
		}
	}

	public disposeTip(type?: (typeof tipTypes)[number]): void {
		if (type) {
			const tipsToDispose: (BlankLineTipWidget | TextSelectionTipWidget)[] = [];
			const tipsToKeep: (BlankLineTipWidget | TextSelectionTipWidget)[] = [];

			this.tips.forEach(tip => {
				if (tip.getType() === type) {
					tipsToDispose.push(tip);
				} else {
					tipsToKeep.push(tip);
				}
			});
			tipsToDispose.forEach(tip => tip.dispose());
			this.tips = tipsToKeep;
		} else {
			this.tips.forEach(tip => {
				tip.dispose();
			});
			this.tips = [];
		}
	}


	public async startApplying(opts: StartApplyingOpts) {
		if (opts.type === 'rewrite') {
			const addedDiffArea = await this._initializeWriteoverStream(opts);
			return addedDiffArea?._URI ?? null;
		}
		else if (opts.type === 'searchReplace') {
			const addedDiffArea = this._initializeSearchAndReplaceStream(opts);
			return addedDiffArea?._URI ?? null;
		}
		else if (opts.type === 'explain') {
			this._initializeWriteoverStream(opts);
		}
		return null;
	}

	private _findOverlappingDiffArea({ startLine, endLine, uri, filter }: { startLine: number; endLine: number; uri: URI; filter?: (diffArea: DiffArea) => boolean }): DiffArea | null {
		// check if there's overlap with any other diffAreas and return early if there is
		for (const diffareaid of this.diffAreasOfURI[uri.fsPath] || []) {
			const diffArea = this.diffAreaOfId[diffareaid];
			if (!diffArea) continue;
			if (!filter?.(diffArea)) continue;
			const noOverlap = diffArea.startLine > endLine || diffArea.endLine < startLine;
			if (!noOverlap) {
				return diffArea;
			}
		}
		return null;
	}

	private async _initializeWriteoverStream(opts: StartApplyingOpts): Promise<DiffZone | undefined> {

		const { from } = opts;

		let startLine: number;
		let endLine: number;
		let uri: URI;
		let instructions: string;
		let selections: StagingSelectionItem[] = []

		if (from === 'ClickApply') {

			const uri_ = this._getActiveEditorURI();
			if (!uri_) return;
			uri = uri_;

			// reject all diffZones on this URI, adding to history (there can't possibly be overlap after this)
			this.removeDiffAreas({ uri, behavior: 'reject', removeCtrlKs: true });

			// in ctrl+L the start and end lines are the full document
			const numLines = this._getNumLines(uri);
			if (numLines === null) return;
			startLine = 1;
			endLine = numLines;

		}
		else if (from === 'QuickEdit') {
			if (opts.type === 'rewrite') {
				const { diffareaid } = opts;
				const ctrlKZone = this.diffAreaOfId[diffareaid];
				if (ctrlKZone.type !== 'CtrlKZone') return;

				const { startLine: startLine_, endLine: endLine_, _URI, _mountInfo } = ctrlKZone;
				uri = _URI;
				startLine = startLine_;
				endLine = endLine_;
				selections = _mountInfo?.selections.current || []
				instructions = _mountInfo?.textAreaRef.current?.value ?? '';
			}
			else if (opts.type === 'explain') {
				instructions = opts.instructions;
				uri = opts.uri;
				startLine = opts.startLine;
				endLine = opts.endLine;
			}
			else {
				throw new Error(`Codeseek: diff.type not recognized on: ${from}`);
			}
		}
		else {
			throw new Error(`Codeseek: diff.type not recognized on: ${from}`);
		}

		const currentFileStr = this._readURI(uri);
		if (currentFileStr === null) return;
		const originalCode = currentFileStr.split('\n').slice((startLine - 1), (endLine - 1) + 1).join('\n');


		const streamRequestIdRef: { current: string | null } = { current: null };


		// add to history
		const { onFinishEdit } = this._addToHistory(uri);

		// __TODO__ let users customize modelFimTags
		const quickEditFIMTags = defaultQuickEditFimTags;

		const adding: Omit<DiffZone, 'diffareaid'> = {
			type: 'DiffZone',
			originalCode,
			startLine,
			endLine,
			_URI: uri,
			_streamState: {
				isStreaming: true,
				streamRequestIdRef,
				line: startLine,
			},
			_diffOfId: {}, // added later
			_removeStylesFns: new Set(),
		};
		const diffZone = this._addDiffArea(adding);
		this._onDidChangeDiffZoneStreaming.fire({ uri, diffareaid: diffZone.diffareaid });
		this._onDidAddOrDeleteDiffZones.fire({ uri });

		if (from === 'QuickEdit') {
			if (opts.type === 'rewrite') {
				const { diffareaid } = opts;
				const ctrlKZone = this.diffAreaOfId[diffareaid];
				if (ctrlKZone.type !== 'CtrlKZone') return;

				ctrlKZone._linkedStreamingDiffZone = diffZone.diffareaid;
				this._onDidChangeCtrlKZoneStreaming.fire({ uri, diffareaid: ctrlKZone.diffareaid });
			}
		}

		// now handle messages
		let messages: LLMChatMessage[];

		if (from === 'ClickApply') {
			const userContent = rewriteCode_userMessage({ originalCode, applyStr: opts.applyStr, uri });
			messages = [
				{ role: 'system', content: rewriteCode_systemMessage, },
				{ role: 'user', content: userContent, }
			];
		}
		else if (from === 'QuickEdit') {
			const { prefix, suffix } = codeseekPrefixAndSuffix({ fullFileStr: currentFileStr, startLine, endLine });
			const language = filenameToVscodeLanguage(uri.fsPath) ?? '';
			const userContent = await ctrlKStream_userMessage({
				selection: originalCode,
				selections,
				instructions: instructions!,
				prefix,
				suffix,
				isOllamaFIM: false,
				fimTags: quickEditFIMTags,
				language,
				codeseekExporerService: this._codeseekExporerService,
				codeseekFileService: this._codeseekFileService,
				modelService: this._modelService,
				workspaceContextService: this._workspaceService
			});
			// type: 'messages',
			messages = [
				{ role: 'system', content: ctrlKStream_systemMessage({ quickEditFIMTags: quickEditFIMTags, rules: this._codeseekSettingsService.state.globalSettings.userRules }), },
				{ role: 'user', content: userContent, }
			];
		}
		else { throw new Error(`featureName ${from} is invalid`); }


		const onDone = (originCode?: string) => {
			diffZone._streamState = { isStreaming: false, applyBoxId: diffZone._streamState.applyBoxId };
			if (originCode) {
				this._onDidChangeDiffZoneStreaming.fire({ uri, diffareaid: diffZone.diffareaid, originCode });
			} else {
				this._onDidChangeDiffZoneStreaming.fire({ uri, diffareaid: diffZone.diffareaid });
			}

			if (from === 'QuickEdit') {
				if (opts.type === 'rewrite') {
					const ctrlKZone = this.diffAreaOfId[opts.diffareaid] as CtrlKZone;

					ctrlKZone._linkedStreamingDiffZone = null;
					this._onDidChangeCtrlKZoneStreaming.fire({ uri, diffareaid: ctrlKZone.diffareaid });
					this._deleteCtrlKZone(ctrlKZone);
				}
			}
			this._refreshStylesAndDiffsInURI(uri);
			onFinishEdit();
		};

		// refresh now in case onText takes a while to get 1st message
		this._refreshStylesAndDiffsInURI(uri);


		const extractText = (fullText: string, recentlyAddedTextLen: number) => {
			if (from === 'QuickEdit') {
				return extractCodeFromFIM({ text: fullText, recentlyAddedTextLen, midTag: quickEditFIMTags.midTag });
			}
			else if (from === 'ClickApply') {
				return extractCodeFromRegular({ text: fullText, recentlyAddedTextLen });
			}
			throw 1;
		};

		const latestStreamInfoMutable: StreamLocationMutable = { line: diffZone.startLine, addedSplitYet: false, col: 1, originalCodeStartLine: 1 };

		// state used in onText:
		let fullText = '';
		let prevIgnoredSuffix = '';
		const startTime = Date.now();
		let firstTokenTime: number | null = null;
		const useProviderFor = opts.from === 'ClickApply' ? FeatureNames.Apply : opts.type === 'explain' ? FeatureNames.CtrlL : FeatureNames.CtrlK
		const reporter = (error?: any) => {
			const firstTokenCostTime = firstTokenTime ? (firstTokenTime - startTime) : 0;
			const totalCostTime = Date.now() - startTime;
			const request = {
				messagesType: 'chatMessages',
				useProviderFor,
				messages,
			};
			const response = fullText;
			if (opts.from === 'QuickEdit') {
				this._metricsService.capture(METRICS_EVENT.CHAT_INLINE, { firstTokenCostTime, totalCostTime, request, response, error });
			} else if (opts.from === 'ClickApply') {
				this._metricsService.capture(METRICS_EVENT.APPLY, { firstTokenCostTime, totalCostTime, request, response, error });
			}
		};

		streamRequestIdRef.current = this._llmMessageService.sendLLMMessage({
			messagesType: 'chatMessages',
			useProviderFor,
			logging: { loggingName: `startApplying - ${from}` },
			messages,
			onText: ({ newText: newText_ }) => {
				firstTokenTime = firstTokenTime ?? Date.now();
				const newText = prevIgnoredSuffix + newText_; // add the previously ignored suffix because it's no longer the suffix!
				fullText += prevIgnoredSuffix + newText; // full text, including ```, etc

				const [croppedText, deltaCroppedText, croppedSuffix] = extractText(fullText, newText.length);
				const { endLineInLlmTextSoFar } = this._writeStreamedDiffZoneLLMText(uri, originalCode, croppedText, deltaCroppedText, latestStreamInfoMutable);
				diffZone._streamState.line = (diffZone.startLine - 1) + endLineInLlmTextSoFar; // change coordinate systems from originalCode to full file

				this._refreshStylesAndDiffsInURI(uri);

				prevIgnoredSuffix = croppedSuffix;
			},
			onFinalMessage: ({ fullText }) => {
				// console.log('DONE! FULL TEXT\n', extractText(fullText), diffZone.startLine, diffZone.endLine)
				// at the end, re-write whole thing to make sure no sync errors
				reporter();
				const [croppedText, _1, _2] = extractText(fullText, 0);
				this._writeText(uri, croppedText,
					{ startLineNumber: diffZone.startLine, startColumn: 1, endLineNumber: diffZone.endLine, endColumn: Number.MAX_SAFE_INTEGER }, // 1-indexed
					{ shouldRealignDiffAreas: true }
				);
				onDone(currentFileStr);
			},
			onError: (e) => {
				this._notifyError(e);
				onDone();
				this._undoHistory(uri);
				reporter(e);
			},

		});

		return diffZone;

	}

	private _initializeSearchAndReplaceStream(opts: StartApplyingOpts & { from: 'ClickApply' }) {
		const { applyStr, uri, applyBoxId } = opts;

		if (!uri) return;

		// generate search/replace block text
		const originalFileCode = this._codeseekFileService.readModel(uri);
		if (originalFileCode === null) return;

		const numLines = this._getNumLines(uri);
		if (numLines === null) return;

		// reject all diffZones on this URI, adding to history (there can't possibly be overlap after this)
		this.removeDiffAreas({ uri, behavior: 'reject', removeCtrlKs: true });

		const startLine = 1;
		const endLine = numLines;

		const userMessageContent = searchReplace_userMessage({ originalCode: originalFileCode, applyStr: applyStr });
		const messages: LLMChatMessage[] = [
			{ role: 'system', content: searchReplace_systemMessage },
			{ role: 'user', content: userMessageContent },
		];

		// can use this as a proxy to set the diffArea's stream state requestId
		const streamRequestIdRef: { current: string | null } = { current: null };

		let { onFinishEdit } = this._addToHistory(uri);

		// TODO replace these with whatever block we're on initially if already started

		type SearchReplaceDiffAreaMetadata = {
			originalBounds: [number, number]; // 1-indexed
			originalCode: string;
		};

		const addedTrackingZoneOfBlockNum: TrackingZone<SearchReplaceDiffAreaMetadata>[] = [];

		const adding: Omit<DiffZone, 'diffareaid'> = {
			type: 'DiffZone',
			originalCode: originalFileCode,
			startLine,
			endLine,
			_URI: uri,
			_streamState: {
				isStreaming: true,
				streamRequestIdRef,
				line: startLine,
				applyBoxId,
			},
			_diffOfId: {}, // added later
			_removeStylesFns: new Set(),
		};
		const diffZone = this._addDiffArea(adding);
		this._onDidChangeDiffZoneStreaming.fire({ uri, diffareaid: diffZone.diffareaid });
		this._onDidAddOrDeleteDiffZones.fire({ uri });


		const revertAndContinueHistory = () => {
			this._undoHistory(uri);
			const { onFinishEdit: onFinishEdit_ } = this._addToHistory(uri);
			onFinishEdit = onFinishEdit_;
		};


		// 将原始代码的行范围映射到最终代码的行范围，考虑之前替换的行数变化
		const convertOriginalRangeToFinalRange = (originalRange: readonly [number, number]): [number, number] => {
			// 通过计算行偏移量来调整范围
			const [originalStart, originalEnd] = originalRange;
			let lineOffset = 0;

			// 遍历所有已追踪的代码块
			for (const blockDiffArea of addedTrackingZoneOfBlockNum) {
				const {
					startLine, endLine, // 当前代码块的起止行
					metadata: { originalBounds: [originalStart2, originalEnd2], }, // 原始代码的起止行
				} = blockDiffArea;

				// 如果原始代码块的开始行在当前范围之后,跳过这个块
				if (originalStart2 >= originalEnd) continue;

				// 计算新代码和原始代码的行数差异
				const numNewLines = endLine - startLine + 1; // 新代码的行数
				const numOldLines = originalEnd2 - originalStart2 + 1; // 原始代码的行数

				// 累加行偏移量(可能为正也可能为负)
				lineOffset += numNewLines - numOldLines;
			}

			// 返回调整后的范围[起始行,结束行]
			return [originalStart + lineOffset, originalEnd + lineOffset];
		};


		const errMsgOfInvalidStr = (str: string & ReturnType<typeof findTextInCode>) => {
			return str === 'Not found' ?
				'I interrupted you because the latest ORIGINAL code could not be found in the file. Please output all SEARCH/REPLACE blocks again, making sure the code in ORIGINAL is identical to a code snippet in the file.'
				: str === 'Not unique' ?
					'I interrupted you because the latest ORIGINAL code shows up multiple times in the file. Please output all SEARCH/REPLACE blocks again, making sure the code in each ORIGINAL section is unique in the file.'
					: '';
		};


		const onDone = (change: boolean = true) => {
			diffZone._streamState = { isStreaming: false, applyBoxId: diffZone._streamState.applyBoxId };
			if (change) {
				this._onDidChangeDiffZoneStreaming.fire({ uri, diffareaid: diffZone.diffareaid });
			} else {
				this._deleteAllDiffAreas(uri);
			}
			this._refreshStylesAndDiffsInURI(uri);


			// delete the tracking zones
			for (const trackingZone of addedTrackingZoneOfBlockNum)
				this._deleteTrackingZone(trackingZone);

			onFinishEdit();
		};

		// refresh now in case onText takes a while to get 1st message
		this._refreshStylesAndDiffsInURI(uri);

		// stream style related
		let latestStreamLocationMutable: StreamLocationMutable | null = null;
		let shouldUpdateOrigStreamStyle = true;

		let oldBlocks: ExtractedSearchReplaceBlock[] = [];

		// this generates >>>>>>> ORIGINAL <<<<<<< REPLACE blocks and and simultaneously applies it
		let shouldSendAnotherMessage = true;
		let nMessagesSent = 0;
		let currStreamingBlockNum = 0;
		let firstTokenTime: number | null = null;
		const startTime = Date.now();
		let response = '';
		const reporter = (error?: any) => {
			const firstTokenCostTime = firstTokenTime ? (firstTokenTime - startTime) : 0;
			const totalCostTime = Date.now() - startTime;
			const request = {
				messagesType: 'chatMessages',
				useProviderFor: FeatureNames.Apply,
				messages,
			};

			this._metricsService.capture(METRICS_EVENT.APPLY, { firstTokenCostTime, totalCostTime, request, response, error });
		};
		while (shouldSendAnotherMessage) {
			shouldSendAnotherMessage = false;
			nMessagesSent += 1;

			streamRequestIdRef.current = this._llmMessageService.sendLLMMessage({
				messagesType: 'chatMessages',
				useProviderFor: FeatureNames.Apply,
				logging: { loggingName: `generateSearchAndReplace` },
				messages,
				onText: ({ fullText }) => {
					firstTokenTime = firstTokenTime ?? Date.now();
					// blocks are [done done done ... {writingFinal|writingOriginal}]
					//               ^
					//              currStreamingBlockNum
					this._codeseekLogService.info('apply onText message');
					// const blocks = extractSearchReplaceBlocks(fullText);

					// for (let blockNum = currStreamingBlockNum; blockNum < blocks.length; blockNum += 1) {
					// 	const block = blocks[blockNum];

					// 	if (block.state === 'writingOriginal') {
					// 		// update stream state to the first line of original if some portion of original has been written
					// 		if (shouldUpdateOrigStreamStyle && block.orig.trim().length >= 20) {
					// 			const startingAtLine = diffZone._streamState.line ?? 1; // dont go backwards if already have a stream line
					// 			const originalRange = findTextInCode(block.orig, originalFileCode, startingAtLine);
					// 			if (typeof originalRange !== 'string') {
					// 				const [startLine, _] = convertOriginalRangeToFinalRange(originalRange);
					// 				diffZone._streamState.line = startLine;
					// 				shouldUpdateOrigStreamStyle = false;
					// 			}
					// 		}
					// 		// must be done writing original to move on to writing streamed content
					// 		continue;
					// 	}
					// 	shouldUpdateOrigStreamStyle = true;


					// 	// if this is the first time we're seeing this block, add it as a diffarea so we can start streaming
					// 	if (!(blockNum in addedTrackingZoneOfBlockNum)) {
					// 		const originalBounds = findTextInCode(block.orig, originalFileCode);

					// 		// if error
					// 		if (typeof originalBounds === 'string') {
					// 			messages.push(
					// 				{ role: 'assistant', content: fullText }, // latest output
					// 				{ role: 'user', content: errMsgOfInvalidStr(originalBounds) } // user explanation of what's wrong
					// 			);
					// 			if (streamRequestIdRef.current) this._llmMessageService.abort(streamRequestIdRef.current);
					// 			shouldSendAnotherMessage = true;
					// 			revertAndContinueHistory();
					// 			continue;
					// 		}

					// 		const [startLine, endLine] = convertOriginalRangeToFinalRange(originalBounds);

					// 		// otherwise if no error, add the position as a diffarea
					// 		const adding: Omit<TrackingZone<SearchReplaceDiffAreaMetadata>, 'diffareaid'> = {
					// 			type: 'TrackingZone',
					// 			startLine: startLine,
					// 			endLine: endLine,
					// 			_URI: uri,
					// 			metadata: {
					// 				originalBounds: [...originalBounds],
					// 				originalCode: block.orig,
					// 			},
					// 		};
					// 		const trackingZone = this._addDiffArea(adding);
					// 		addedTrackingZoneOfBlockNum.push(trackingZone);
					// 		latestStreamLocationMutable = { line: startLine, addedSplitYet: false, col: 1, originalCodeStartLine: 1 };
					// 	} // <-- done adding diffarea


					// 	// should always be in streaming state here
					// 	if (!diffZone._streamState.isStreaming) {
					// 		console.error('DiffZone was not in streaming state in _initializeSearchAndReplaceStream');
					// 		continue;
					// 	}
					// 	if (!latestStreamLocationMutable) continue;

					// 	// if a block is done, finish it by writing all
					// 	if (block.state === 'done') {
					// 		const { startLine: finalStartLine, endLine: finalEndLine } = addedTrackingZoneOfBlockNum[blockNum];
					// 		this._writeText(uri, block.final,
					// 			{ startLineNumber: finalStartLine, startColumn: 1, endLineNumber: finalEndLine, endColumn: Number.MAX_SAFE_INTEGER }, // 1-indexed
					// 			{ shouldRealignDiffAreas: true }
					// 		);
					// 		diffZone._streamState.line = finalEndLine + 1;
					// 		currStreamingBlockNum = blockNum + 1;
					// 		continue;
					// 	}

					// 	// write the added text to the file
					// 	const deltaFinalText = block.final.substring((oldBlocks[blockNum]?.final ?? '').length, Infinity);
					// 	this._writeStreamedDiffZoneLLMText(uri, block.orig, block.final, deltaFinalText, latestStreamLocationMutable);
					// 	oldBlocks = blocks; // oldblocks is only used if writingFinal

					// 	// const { endLine: currentEndLine } = addedTrackingZoneOfBlockNum[blockNum] // would be bad to do this because a lot of the bottom lines might be the same. more accurate to go with latestStreamLocationMutable
					// 	// diffZone._streamState.line = currentEndLine
					// 	diffZone._streamState.line = latestStreamLocationMutable.line;
					// } // end for

					// this._refreshStylesAndDiffsInURI(uri);
				},
				onFinalMessage: async ({ fullText }) => {
					this._codeseekLogService.info('apply final message', fullText);
					response = fullText;
					reporter()
					// 1. wait 500ms and fix lint errors - call lint error workflow
					// (update react state to say "Fixing errors")

					let blocks = extractSearchReplaceBlocks(fullText);
					if (originalFileCode.trim() === '') {
						blocks = [{
							orig: '',
							final: applyStr,
							state: 'done'
						}]
					}

					for (let blockNum = currStreamingBlockNum; blockNum < blocks.length; blockNum += 1) {
						const block = blocks[blockNum];

						if (block.state === 'writingOriginal') {
							// update stream state to the first line of original if some portion of original has been written
							if (shouldUpdateOrigStreamStyle && block.orig.trim().length >= 20) {
								const startingAtLine = diffZone._streamState.line ?? 1; // dont go backwards if already have a stream line
								const originalRange = findTextInCode(block.orig, originalFileCode, startingAtLine);
								if (typeof originalRange !== 'string') {
									const [startLine, _] = convertOriginalRangeToFinalRange(originalRange);
									diffZone._streamState.line = startLine;
									shouldUpdateOrigStreamStyle = false;
								}
							}
							// must be done writing original to move on to writing streamed content
							continue;
						}
						shouldUpdateOrigStreamStyle = true;


						// if this is the first time we're seeing this block, add it as a diffarea so we can start streaming
						if (!(blockNum in addedTrackingZoneOfBlockNum)) {
							const originalBounds = originalFileCode.trim() === '' ? [1, block.final.split('\n').length] as const : findTextInCode(block.orig, originalFileCode);
							// if error
							if (typeof originalBounds === 'string') {
								messages.push(
									{ role: 'assistant', content: fullText }, // latest output
									{ role: 'user', content: errMsgOfInvalidStr(originalBounds) } // user explanation of what's wrong
								);
								if (streamRequestIdRef.current) this._llmMessageService.abort(streamRequestIdRef.current);
								shouldSendAnotherMessage = true;
								revertAndContinueHistory();
								continue;
							}

							const [startLine, endLine] = convertOriginalRangeToFinalRange(originalBounds);

							// otherwise if no error, add the position as a diffarea
							const adding: Omit<TrackingZone<SearchReplaceDiffAreaMetadata>, 'diffareaid'> = {
								type: 'TrackingZone',
								startLine: startLine,
								endLine: endLine,
								_URI: uri,
								metadata: {
									originalBounds: [...originalBounds],
									originalCode: block.orig,
								},
							};
							const trackingZone = this._addDiffArea(adding);
							addedTrackingZoneOfBlockNum.push(trackingZone);
							latestStreamLocationMutable = { line: startLine, addedSplitYet: false, col: 1, originalCodeStartLine: 1 };
						} // <-- done adding diffarea


						// should always be in streaming state here
						if (!diffZone._streamState.isStreaming) {
							console.error('DiffZone was not in streaming state in _initializeSearchAndReplaceStream');
							continue;
						}
						if (!latestStreamLocationMutable) continue;

						// if a block is done, finish it by writing all
						if (block.state === 'done') {
							const { startLine: finalStartLine, endLine: finalEndLine } = addedTrackingZoneOfBlockNum[blockNum];
							this._writeText(uri, block.final,
								{ startLineNumber: finalStartLine, startColumn: 1, endLineNumber: finalEndLine, endColumn: Number.MAX_SAFE_INTEGER }, // 1-indexed
								{ shouldRealignDiffAreas: true }
							);
							diffZone._streamState.line = finalEndLine + 1;
							currStreamingBlockNum = blockNum + 1;
							continue;
						}

						// write the added text to the file
						const deltaFinalText = block.final.substring((oldBlocks[blockNum]?.final ?? '').length, Infinity);
						this._writeStreamedDiffZoneLLMText(uri, block.orig, block.final, deltaFinalText, latestStreamLocationMutable);
						oldBlocks = blocks; // oldblocks is only used if writingFinal

						// const { endLine: currentEndLine } = addedTrackingZoneOfBlockNum[blockNum] // would be bad to do this because a lot of the bottom lines might be the same. more accurate to go with latestStreamLocationMutable
						// diffZone._streamState.line = currentEndLine
						diffZone._streamState.line = latestStreamLocationMutable.line;
					} // end for

					this._refreshStylesAndDiffsInURI(uri);




					if (blocks.length === 0) {
						this._notificationService.info(`Codeseek: We ran Apply, but the LLM didn't output any changes.`);
					}

					// writeover the whole file
					let newCode = originalFileCode;
					for (let blockNum = addedTrackingZoneOfBlockNum.length - 1; blockNum >= 0; blockNum -= 1) {
						const { originalBounds } = addedTrackingZoneOfBlockNum[blockNum].metadata;
						const finalCode = blocks[blockNum].final;

						if (finalCode === null) continue;

						const [originalStart, originalEnd] = originalBounds;
						const lines = newCode.split('\n');
						newCode = [
							...lines.slice(0, (originalStart - 1)),
							...finalCode.split('\n'),
							...lines.slice((originalEnd - 1) + 1, Infinity)
						].join('\n');
					}
					const numLines = this._getNumLines(uri);
					if (numLines !== null) {
						this._writeText(uri, newCode,
							{ startLineNumber: 1, startColumn: 1, endLineNumber: numLines, endColumn: Number.MAX_SAFE_INTEGER },
							{ shouldRealignDiffAreas: true }
						);
					}
					const change = originalFileCode !== newCode;
					onDone(change);
				},
				onError: (e) => {
					this._notifyError(e);
					onDone();
					this._undoHistory(uri);
					reporter(e);
				},

			});
		}


		return diffZone;
	}

	private _stopIfStreaming(diffZone: DiffZone) {
		const uri = diffZone._URI;

		const streamRequestId = diffZone._streamState.streamRequestIdRef?.current;
		if (!streamRequestId) return;

		this._llmMessageService.abort(streamRequestId);

		diffZone._streamState = { isStreaming: false, applyBoxId: diffZone._streamState.applyBoxId };
		this._onDidChangeDiffZoneStreaming.fire({ uri, diffareaid: diffZone.diffareaid });
	}

	_undoHistory(uri: URI) {
		this._undoRedoService.undo(uri);
	}

	_interruptSingleDiffZoneStreaming({ diffareaid }: { diffareaid: number }) {
		const diffZone = this.diffAreaOfId[diffareaid];
		if (diffZone?.type !== 'DiffZone') return;
		if (!diffZone._streamState.isStreaming) return;

		this._stopIfStreaming(diffZone);
		this._undoHistory(diffZone._URI);
	}


	isCtrlKZoneStreaming({ diffareaid }: { diffareaid: number }) {
		const ctrlKZone = this.diffAreaOfId[diffareaid];
		if (!ctrlKZone) return false;
		if (ctrlKZone.type !== 'CtrlKZone') return false;
		return !!ctrlKZone._linkedStreamingDiffZone;
	}

	// diffareaid of the ctrlKZone (even though the stream state is dictated by the linked diffZone)
	interruptCtrlKStreaming({ diffareaid }: { diffareaid: number }) {
		const ctrlKZone = this.diffAreaOfId[diffareaid];
		if (ctrlKZone?.type !== 'CtrlKZone') return;
		if (!ctrlKZone._linkedStreamingDiffZone) return;

		const linkedStreamingDiffZone = this.diffAreaOfId[ctrlKZone._linkedStreamingDiffZone];
		if (!linkedStreamingDiffZone) return;
		if (linkedStreamingDiffZone.type !== 'DiffZone') return;

		this._interruptSingleDiffZoneStreaming({ diffareaid: linkedStreamingDiffZone.diffareaid });
	}

	getURIStreamState = ({ uri, applyBoxId }: { uri: URI | null, applyBoxId?: string }) => {
		if (uri === null) return 'idle';

		const diffZones = [...this.diffAreasOfURI[uri.fsPath].values()]
			.map(diffareaid => this.diffAreaOfId[diffareaid])
			.filter(diffArea => !!diffArea && diffArea.type === 'DiffZone');

		// 如果提供了applyBoxId，只检查对应的diffZone
		let relevantDiffZones = diffZones;
		if (applyBoxId) {
			relevantDiffZones = diffZones.filter(diffZone => diffZone._streamState.applyBoxId === applyBoxId);
		}

		const isStreaming = relevantDiffZones.find(diffZone => !!diffZone._streamState.isStreaming);
		const state: URIStreamState = isStreaming ? 'streaming' : (relevantDiffZones.length === 0 ? 'idle' : 'acceptRejectAll');
		return state;
	};

	interruptURIStreaming({ uri }: { uri: URI }) {
		// brute force for now is OK
		for (const diffareaid of this.diffAreasOfURI[uri.fsPath] || []) {
			const diffArea = this.diffAreaOfId[diffareaid];
			if (diffArea?.type !== 'DiffZone') continue;
			if (!diffArea._streamState.isStreaming) continue;
			this._stopIfStreaming(diffArea);
		}
		this._undoHistory(uri);
	}


	// public removeDiffZone(diffZone: DiffZone, behavior: 'reject' | 'accept') {
	// 	const uri = diffZone._URI
	// 	const { onFinishEdit } = this._addToHistory(uri)

	// 	if (behavior === 'reject') this._revertAndDeleteDiffZone(diffZone)
	// 	else if (behavior === 'accept') this._deleteDiffZone(diffZone)

	// 	this._refreshStylesAndDiffsInURI(uri)
	// 	onFinishEdit()
	// }

	private _revertAndDeleteDiffZone(diffZone: DiffZone) {
		const uri = diffZone._URI;

		const writeText = diffZone.originalCode;
		const toRange: IRange = { startLineNumber: diffZone.startLine, startColumn: 1, endLineNumber: diffZone.endLine, endColumn: Number.MAX_SAFE_INTEGER };
		this._writeText(uri, writeText, toRange, { shouldRealignDiffAreas: true });

		this._deleteDiffZone(diffZone);
	}


	// remove a batch of diffareas all at once (and handle accept/reject of their diffs)
	public removeDiffAreas({ uri, removeCtrlKs, behavior }: { uri: URI; removeCtrlKs: boolean; behavior: 'reject' | 'accept' }) {

		const diffareaids = this.diffAreasOfURI[uri.fsPath];
		if (diffareaids.size === 0) return; // do nothing

		const { onFinishEdit } = this._addToHistory(uri);

		for (const diffareaid of diffareaids) {
			const diffArea = this.diffAreaOfId[diffareaid];
			if (!diffArea) continue;

			if (diffArea.type == 'DiffZone') {
				if (behavior === 'reject') this._revertAndDeleteDiffZone(diffArea);
				else if (behavior === 'accept') this._deleteDiffZone(diffArea);
			}
			else if (diffArea.type === 'CtrlKZone' && removeCtrlKs) {
				this._deleteCtrlKZone(diffArea);
			}
		}

		this._refreshStylesAndDiffsInURI(uri);

		// 触发diff计数变化事件
		this._updateDiffCount(uri);

		onFinishEdit();
	}

	// called on codeseek.acceptDiff
	public async acceptDiff({ diffid }: { diffid: number }) {
		const diff = this.diffOfId[diffid];
		if (!diff) return;

		const { diffareaid } = diff;
		const diffArea = this.diffAreaOfId[diffareaid];
		if (!diffArea) return;

		if (diffArea.type !== 'DiffZone') return;

		const uri = diffArea._URI;

		// add to history
		const { onFinishEdit } = this._addToHistory(uri);

		const originalLines = diffArea.originalCode.split('\n');
		let newOriginalCode: string;

		if (diff.type === 'deletion') {
			newOriginalCode = [
				...originalLines.slice(0, (diff.originalStartLine - 1)), // everything before startLine
				// <-- deletion has nothing here
				...originalLines.slice((diff.originalEndLine - 1) + 1, Infinity) // everything after endLine
			].join('\n');
		}
		else if (diff.type === 'insertion') {
			newOriginalCode = [
				...originalLines.slice(0, (diff.originalStartLine - 1)), // everything before startLine
				diff.code, // code
				...originalLines.slice((diff.originalStartLine - 1), Infinity) // startLine (inclusive) and on (no +1)
			].join('\n');
		}
		else if (diff.type === 'edit') {
			newOriginalCode = [
				...originalLines.slice(0, (diff.originalStartLine - 1)), // everything before startLine
				diff.code, // code
				...originalLines.slice((diff.originalEndLine - 1) + 1, Infinity) // everything after endLine
			].join('\n');
		}
		else {
			throw new Error(`Codeseek error: ${diff}.type not recognized`);
		}

		// update code now accepted as original
		diffArea.originalCode = newOriginalCode;

		// delete the diff
		this._deleteDiff(diff);

		// diffArea should be removed if it has no more diffs in it
		if (Object.keys(diffArea._diffOfId).length === 0) {
			this._deleteDiffZone(diffArea);
		}

		this._refreshStylesAndDiffsInURI(uri);

		// 触发diff计数变化事件
		this._updateDiffCount(uri);

		onFinishEdit();
	}

	// called on codeseek.rejectDiff
	public async rejectDiff({ diffid }: { diffid: number }) {

		const diff = this.diffOfId[diffid];
		if (!diff) return;

		const { diffareaid } = diff;
		const diffArea = this.diffAreaOfId[diffareaid];
		if (!diffArea) return;

		if (diffArea.type !== 'DiffZone') return;

		const uri = diffArea._URI;

		// add to history
		const { onFinishEdit } = this._addToHistory(uri);

		let writeText: string;
		let toRange: IRange;

		// if it was a deletion, need to re-insert
		// (this image applies to writeText and toRange, not newOriginalCode)
		//  A
		// |B   <-- deleted here, diff.startLine == diff.endLine
		//  C
		if (diff.type === 'deletion') {
			// if startLine is out of bounds (deleted lines past the diffarea), applyEdit will do a weird rounding thing, to account for that we apply the edit the line before
			if (diff.startLine - 1 === diffArea.endLine) {
				writeText = '\n' + diff.originalCode;
				toRange = { startLineNumber: diff.startLine - 1, startColumn: Number.MAX_SAFE_INTEGER, endLineNumber: diff.startLine - 1, endColumn: Number.MAX_SAFE_INTEGER };
			}
			else {
				writeText = diff.originalCode + '\n';
				toRange = { startLineNumber: diff.startLine, startColumn: 1, endLineNumber: diff.startLine, endColumn: 1 };
			}
		}
		else if (diff.type === 'insertion') {
			// handle the case where the insertion was a newline at end of diffarea (applying to the next line doesnt work because it doesnt exist, vscode just doesnt delete the correct # of newlines)
			if (diff.endLine === diffArea.endLine) {
				// delete the line before instead of after
				writeText = '';
				toRange = { startLineNumber: diff.startLine - 1, startColumn: Number.MAX_SAFE_INTEGER, endLineNumber: diff.endLine, endColumn: 1 }; // 1-indexed
			}
			else {
				writeText = '';
				toRange = { startLineNumber: diff.startLine, startColumn: 1, endLineNumber: diff.endLine + 1, endColumn: 1 }; // 1-indexed
			}

		}
		else if (diff.type === 'edit') {
			writeText = diff.originalCode;
			toRange = { startLineNumber: diff.startLine, startColumn: 1, endLineNumber: diff.endLine, endColumn: Number.MAX_SAFE_INTEGER }; // 1-indexed
		}
		else {
			throw new Error(`Codeseek error: ${diff}.type not recognized`);
		}

		// update the file
		this._writeText(uri, writeText, toRange, { shouldRealignDiffAreas: true });

		// originalCode does not change!

		// delete the diff
		this._deleteDiff(diff);

		// diffArea should be removed if it has no more diffs in it
		if (Object.keys(diffArea._diffOfId).length === 0) {
			this._deleteDiffZone(diffArea);
		}

		this._refreshStylesAndDiffsInURI(uri);

		// 触发diff计数变化事件
		this._updateDiffCount(uri);

		onFinishEdit();

	}

	/**
	 * 更新当前文件的diff计数并触发事件
	 * @param uri 文件URI
	 */
	private _updateDiffCount(uri: URI): void {
		const allDiffs = this._sortedDiffs[uri.fsPath];
		if (allDiffs.length > 0) {
			const editor = this._editorService.activeEditorPane?.getControl() as ICodeEditor | undefined;
			if (editor) {
				const position = editor.getPosition();
				if (position) {
					// 找到当前光标位置所在或之后最近的diff
					for (let i = 0; i < allDiffs.length; i++) {
						if (allDiffs[i].startLine >= position.lineNumber) {
							this._currentDiffIndex = i;
							break;
						}
						// 如果是最后一个diff且光标在其后，则选中最后一个
						if (i === allDiffs.length - 1) {
							this._currentDiffIndex = i;
						}
					}
				}
			}
		}

		// 触发事件
		this._onDidChangeDiffCount.fire({ uri, currentIndex: this._currentDiffIndex, totalDiffs: allDiffs.length });
	}

	public getCurrentDiffIndex(): number {
		return this._currentDiffIndex;
	}

	public acceptDiffAtIndex({ uri, index }: { uri: URI; index: number }): void {
		const diffs = this._sortedDiffs[uri.fsPath];
		if (!diffs || index < 0 || index >= diffs.length) {
			return;
		}

		this.acceptDiff({ diffid: diffs[index].diffid });
	}

	public rejectDiffAtIndex({ uri, index }: { uri: URI; index: number }): void {
		const diffs = this._sortedDiffs[uri.fsPath];
		if (!diffs || index < 0 || index >= diffs.length) {
			return;
		}

		this.rejectDiff({ diffid: diffs[index].diffid });
	}
}

registerSingleton(IEditCodeService, EditCodeService, InstantiationType.Eager);
