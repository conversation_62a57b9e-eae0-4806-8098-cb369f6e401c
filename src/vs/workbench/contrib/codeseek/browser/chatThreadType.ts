import { TaskInfo } from '../../../api/browser/mainThreadCodeSeekTaskService.js';
import { ChatMode } from '../common/codeseekSettingsService.js';
import { ToolCallType } from '../common/toolsServiceTypes.js';
import { StagingSelectionItem } from '../common/selectedFileService.js';
import { AskResponse, ToolCallReturnType, ToolName } from '../common/toolsServiceTypes.js';
import { Event } from '../../../../base/common/event.js';
import { createDecorator } from '../../../../platform/instantiation/common/instantiation.js';

export const IChatThreadService = createDecorator<IChatThreadService>('codeseekChatThreadService');
export interface IChatThreadService {
	readonly _serviceBrand: undefined;

	readonly containerState: ContainerState;
	readonly streamState: ThreadStreamState;

	setStreamState(containerId: string, threadId: string, state: Partial<NonNullable<ThreadStreamState[string][string]>>): void;

	onDidChangeCurrentContainer: Event<void>;
	onDidChangeCurrentThread: Event<string>;
	onDidChangeStreamState: Event<{ containerId: string, threadId: string }>;
	onDidSetChatTitle: Event<{ containerId: string, message: string }>;

	openNewContainer(): string;
	openNewThread(containerId: string): void;

	getCurrentContainerId(): string;
	getCurrentThreadId(containerId: string): string;

	isCurrentThreadWorking(containerId: string): boolean;
	getCurrentThread(containerId: string): ChatThreads[string];
	getCurrentThreadMessages(containerId: string): ChatMessage[];

	switchToContainer(containerId: string): void;
	deleteContainer(containerId: string): void;
	switchToThread(containerId: string, threadId: string, targetContainerId?: string): void;
	deleteThread(containerId: string, threadId: string): void;

	isFocusingContainer(containerId: string): boolean;

	// you can edit multiple messages
	// the one you're currently editing is "focused", and we add items to that one when you press cmd+L.
	getFocusedMessageIdx(containerId: string): number | undefined;
	isFocusingMessage(containerId: string): boolean;
	setFocusedMessageIdx(containerId: string, messageIdx: number | undefined): void;

	// exposed getters/setters
	getCurrentMessageState(containerId: string, messageIdx: number): UserMessageState;
	setCurrentMessageState(containerId: string, messageIdx: number, newState: Partial<UserMessageState>): void;
	getCurrentThreadStagingSelections(containerId: string): StagingSelectionItem[];
	setCurrentThreadStagingSelections(containerId: string, stagingSelections: StagingSelectionItem[]): void;
	getCurrentThreadStateSelections(containerId: string): StateSelections;
	setCurrentThreadStateSelectionsChangeSelections(containerId: string): void;

	// call to edit a message
	editUserMessageAndStreamResponse({ containerId, userMessage, chatMode, messageIdx }: { containerId: string; userMessage: string; chatMode: ChatMode; messageIdx: number }): Promise<void>;

	// call to add a message
	addUserMessageAndStreamResponse({ containerId, userMessageOpts }:
		{
			containerId: string;
			userMessageOpts: userMessageOpts;
		}):
		Promise<void>;

	cancelStreaming(containerId: string, threadId: string): void;
	dismissStreamError(containerId: string, threadId: string): void;
	setAskResponse(containerId: string, askResponse: AskResponse): void;
	addSelectionToChat(containerId: string, selection?: StagingSelectionItem): void;
	addMessageToThread(containerId: string, threadId: string, message: ChatMessage, affectCurrentThread?: boolean): void;
	finishStreamingTextMessage(containerId: string, threadId: string, content: string, error?: { message: string; fullError: Error | null }): void;
}

export type CodeSeekAsk =
	| 'followup'
	| 'command'
	| 'command_output'
	| 'completion_result'
	| 'tool'
	| 'api_req_failed'
	| 'resume_task'
	| 'resume_completed_task'
	| 'mistake_limit_reached'
	| 'browser_action_launch'
	| 'use_mcp_server';

export type FixMessageOpts = {
	from: 'Fix';
	chatMode: ChatMode;
	userMessage: string;
	linterErrors: string;
	userMessageWithSelection?: string;
	selections?: StagingSelectionItem[];
}

export type ChatMessageOpts = {
	from: 'Chat';
	chatMode: ChatMode;
	userMessage: string;
	selections?: StagingSelectionItem[];
}

export type PluginMessageOpts = {
	from: 'Plugin';
	chatMode: ChatMode;
	userMessage: string;
	taskInfo: TaskInfo;
	selections?: StagingSelectionItem[];
}

export type userMessageOpts = FixMessageOpts | ChatMessageOpts | PluginMessageOpts;

export type ToolMessage<T extends ToolName> = {
	role: 'tool';
	content: string | undefined; // result
	containerId: string;
	threadId: string;
	result: ToolCallReturnType[T] | null; // text message of result
} & ToolCallType;

export type AskMessage = {
	type: string;
	content: ToolCallType | null; // content displayed to the LLM on future calls - allowed to be '', will be replaced with (empty)
	// operate: string | null; // content displayed to user  - allowed to be '', will be ignored
};

// WARNING: changing this format is a big deal!!!!!! need to migrate old format to new format on users' computers so people don't get errors.
export type ChatMessage =
	| {
		role: 'system';
		content: string;
		displayContent?: undefined;
	} | {
		role: 'user';
		content: string | null; // content displayed to the LLM on future calls - allowed to be '', will be replaced with (empty)
		displayContent: string | null; // content displayed to user  - allowed to be '', will be ignored
		state: {
			stagingSelections: StagingSelectionItem[];
			isBeingEdited: boolean;
		};
	} | {
		role: 'assistant';
		content: string | null; // content received from LLM  - allowed to be '', will be replaced with (empty)
		displayContent: string | null; // content displayed to user (this is the same as content for now) - allowed to be '', will be ignored
	}
	| ToolMessage<ToolName>;

export type UserMessageType = ChatMessage & { role: 'user' };
export type UserMessageState = UserMessageType['state'];

export type ChatContainers = {
	[containerId: string]: {
		threadsState: ThreadsState;
	};
};

// a 'thread' means a chat message history
export type ChatThreads = {
	[id: string]: {
		id: string; // store the id here too
		createdAt: string; // ISO string
		lastModified: string; // ISO string
		messagesLength: number;
		firstUserMessage: string;
		state: {
			stateSelections: StateSelections;
			focusedMessageIdx: number | undefined; // index of the message that is being edited (undefined if none)
			isCheckedOfSelectionId: { [selectionId: string]: boolean };
			askMessage?: AskMessage;
			askResponse?: AskResponse;
			askResponseText?: string;
		};
	};
};


export type StateSelections = {
	list: StagingSelectionItem[];
	followEditorActive: boolean;
};

export type ThreadType = ChatThreads[string];

export type ContainerState = {
	allContainers: ChatContainers;
	currentContainerId: string;
};

export type ThreadsState = {
	allThreads: ChatThreads;
	currentThreadId: string; // intended for internal use only
	currentThreadMessages: ChatMessage[];
};

export type ThreadStreamState = {
	[containerId: string]: {
		[threadId: string]: undefined | {
			error?: { message: string; fullError: Error | null };
			messageSoFar?: string;
			streamingToken?: string;
			toolCall?: ToolCallType & { result: ToolCallReturnType[ToolName] | null; content: string };
			isStreaming?: boolean;
		};
	};
};


export type MessageType = {
	userMessageOpts: userMessageOpts;
	chatMode: ChatMode;
	chatSelections?: { prevSelns?: StagingSelectionItem[]; currSelns?: StagingSelectionItem[] };
	taskInfo?: TaskInfo;
}

