/*--------------------------------------------------------------------------------------
 *  Copyright 2025 ZTE, Inc. All rights reserved.
 *  Licensed under the Apache License, Version 2.0. See LICENSE.txt for more information.
 *--------------------------------------------------------------------------------------*/

import { Emitter, Event } from '../../../../base/common/event.js';
import { Disposable } from '../../../../base/common/lifecycle.js';
import { isLinux, isMacintosh, isWindows } from '../../../../base/common/platform.js';
import { IConfigurationService } from '../../../../platform/configuration/common/configuration.js';
import { IContextKeyService } from '../../../../platform/contextkey/common/contextkey.js';
import { InstantiationType, registerSingleton } from '../../../../platform/instantiation/common/extensions.js';
import { createDecorator } from '../../../../platform/instantiation/common/instantiation.js';
import { IWorkspaceContextService } from '../../../../platform/workspace/common/workspace.js';
import { ICodeseekFileService, IDirectoryNode, IFileNode, IProjectStructure } from '../common/codeseekFileService.js';
import { ChatMode, ICodeseekSettingsService } from '../common/codeseekSettingsService.js';
import { getWorkspaceUri } from '../common/helpers/path.js';
import { SHELL_PATHS } from '../electron-main/utils/index.js';
import { StagingSelectionItem } from '../common/selectedFileService.js';
import { TaskInfo } from '../../../api/browser/mainThreadCodeSeekTaskService.js';
import { IChatThreadService } from './chatThreadType.js';
import { ICommandService } from '../../../../platform/commands/common/commands.js';
import { ISidebarStateService } from './sidebarStateService.js';
import { CODESEEK_ADD_SELECTION_TO_SIDEBAR_ACTION_ID, CODESEEK_OPEN_SIDEBAR_ACTION_ID } from './sidebarActions.js';


export type AgentParamsFromIde = {
	rules: string;
	environment_details: EnvironmentDetails;
	system_information: SystemInformation;
};

export type EnvironmentDetails = {
	repo: string;
	branch: string;
	working_directory_file_list?: string;
};

export type SystemInformation = {
	default_shell: string | null;
	current_working_directory: string;
	operating_system?: string;
	home_directory?: string;
};

interface TerminalProfile {
	path?: string;
	source?: "PowerShell" | "WSL";
}

type TerminalProfiles = Record<string, TerminalProfile>;

export interface IPluginTaskService {
	readonly _serviceBrand: undefined;

	sendMessage(taskDesc: string, chatMode: ChatMode, selections: StagingSelectionItem[], taskInfo: TaskInfo): Promise<void>;

	provideIdeParams(): Promise<AgentParamsFromIde>;

	onDidTaskDone: Event<{ taskId: string }>;
	readonly fireTaskDone: (taskId: string) => void;

	onDidTaskError: Event<{ taskId: string }>;
	readonly fireTaskError: (taskId: string) => void;

	onDidToolCall: Event<{ taskId: string; toolName: string; toolParam: { [key: string]: any } }>;
	readonly fireToolCall: (taskId: string, toolName: string, toolParam: { [key: string]: any }) => any;

	onDidReceiveMessage: Event<{ taskId: string; message: { [key: string]: any } }>;
	readonly fireReceiveMessage: (taskId: string, message: { [key: string]: any }) => void;
}


export const IPluginTaskService = createDecorator<IPluginTaskService>('PluginTaskService');
export class PluginTaskService extends Disposable implements IPluginTaskService {
	readonly _serviceBrand: undefined;

	static readonly ID = 'TaskSettingService';

	private readonly _onDidTaskDone = new Emitter<{ taskId: string }>();
	readonly onDidTaskDone: Event<{ taskId: string }> = this._onDidTaskDone.event;

	private readonly _onDidTaskError = new Emitter<{ taskId: string }>();
	readonly onDidTaskError: Event<{ taskId: string }> = this._onDidTaskError.event;

	private readonly _onDidToolCall = new Emitter<{ taskId: string; toolName: string; toolParam: { [key: string]: any } }>();
	readonly onDidToolCall: Event<{ taskId: string; toolName: string; toolParam: { [key: string]: any } }> = this._onDidToolCall.event;

	private readonly _onDidReceiveMessage = new Emitter<{ taskId: string; message: { [key: string]: any } }>();
	readonly onDidReceiveMessage: Event<{ taskId: string; message: { [key: string]: any } }> = this._onDidReceiveMessage.event;

	constructor(
		@IConfigurationService private readonly configurationService: IConfigurationService,
		@IWorkspaceContextService private readonly workspaceService: IWorkspaceContextService,
		@IContextKeyService private readonly contextKeyService: IContextKeyService,
		@ICodeseekFileService private readonly codeseekFileService: ICodeseekFileService,
		@ICodeseekSettingsService private readonly codeseekSettingsState: ICodeseekSettingsService,
		@IChatThreadService private readonly chatThreadService: IChatThreadService,
		@ICommandService private readonly commandService: ICommandService,
		@ISidebarStateService private readonly sidebarStateService: ISidebarStateService,
	) {
		super();
	}

	public async sendMessage(taskDesc: string, chatMode: ChatMode, selections: StagingSelectionItem[], taskInfo: TaskInfo) {
		const containerId = this.chatThreadService.getCurrentContainerId();
		if (this.chatThreadService.isCurrentThreadWorking(containerId)) {
			console.log('chat is working, please wait');
			return;
		}

		if (!this.sidebarStateService.isSidebarChatOpen()) {
			await this.commandService.executeCommand(CODESEEK_OPEN_SIDEBAR_ACTION_ID);
		}
		await this.commandService.executeCommand(CODESEEK_ADD_SELECTION_TO_SIDEBAR_ACTION_ID);
		const agentParamsFromIde = await this.provideIdeParams();
		taskInfo.agentParamsFromIde = agentParamsFromIde;
		this.chatThreadService.addUserMessageAndStreamResponse({
			containerId,
			userMessageOpts: {
				from: 'Plugin',
				chatMode: chatMode,
				userMessage: taskDesc,
				taskInfo: taskInfo,
				selections: selections,
			},
		});
	}

	async provideIdeParams(): Promise<AgentParamsFromIde> {
		return {
			rules: this.getRules(),
			environment_details: await this.getEnvironmentDetails(),
			system_information: this.getSystemInformation(),
		};
	}

	private getSystemInformation(): SystemInformation {
		const { workspaceUri } = getWorkspaceUri(this.workspaceService);

		return {
			"default_shell": this.getShell(),
			"current_working_directory": workspaceUri?.fsPath || '',
		};
	}

	private async getEnvironmentDetails(): Promise<EnvironmentDetails> {
		const repositoryName = this.contextKeyService.getContextKeyValue<string>('scmActiveRepositoryName') || '';
		const branchName = this.contextKeyService.getContextKeyValue<string>('scmActiveRepositoryBranchName') || '';

		const projectStructure = await this.codeseekFileService.getProjectStructure(3);
		const workingDirectoryFileTree = this.convertProjectStructureToString(projectStructure, 200);
		return {
			repo: repositoryName,
			branch: branchName,
			working_directory_file_list: workingDirectoryFileTree,
		};
	}

	private convertProjectStructureToString(projectStructure: IProjectStructure, limit: number): string {
		if (!projectStructure || !projectStructure.root) {
			return '';
		}

		const result = { text: '', count: 0 };
		this.formatDirectoryNode(projectStructure.root, 0, limit, result);
		return result.text;
	}

	private formatDirectoryNode(node: IDirectoryNode, depth: number, limit: number, result: { text: string; count: number }): boolean {
		const indent = '  '.repeat(depth);
		result.text += `${indent}${node.name}/\n`;
		result.count++;

		if (result.count >= limit) {
			return true;
		}

		if (node.children && node.children.length > 0) {
			// 处理目录
			const directories = node.children.filter((child: IDirectoryNode | IFileNode) => child.type === 'directory') as IDirectoryNode[];
			for (const dir of directories) {
				if (this.formatDirectoryNode(dir, depth + 1, limit, result)) {
					return true;
				}
			}

			// 处理文件
			const files = node.children.filter((child: IDirectoryNode | IFileNode) => child.type === 'file') as IFileNode[];
			for (const file of files) {
				result.text += `${indent}  ${file.name}\n`;
				result.count++;

				if (result.count >= limit) {
					return true;
				}
			}
		}

		return false;
	}

	private getRules() {
		return this.codeseekSettingsState.state.globalSettings.userRules;
	}

	private getShell() {
		let defaultProfileName = null;
		let profiles = null;

		if (isWindows) {
			defaultProfileName = this.configurationService.getValue<string>("terminal.integrated.defaultProfile.windows");
			profiles = this.configurationService.getValue<TerminalProfiles>("terminal.integrated.profiles.windows") || {};
		} else if (isMacintosh) {
			defaultProfileName = this.configurationService.getValue<string>("terminal.integrated.defaultProfile.osx");
			profiles = this.configurationService.getValue<TerminalProfiles>("terminal.integrated.profiles.osx") || {};
		} else if (isLinux) {
			defaultProfileName = this.configurationService.getValue<string>("terminal.integrated.defaultProfile.linux");
			profiles = this.configurationService.getValue<TerminalProfiles>("terminal.integrated.profiles.linux") || {};
		}

		if (!defaultProfileName) {
			return null;
		}
		const profile = profiles?.[defaultProfileName];
		if (isWindows) {
			if (defaultProfileName.toLowerCase().includes("powershell")) {
				if (profile?.path) {
					return profile.path;
				} else if (profile?.source === "PowerShell") {
					return SHELL_PATHS.POWERSHELL_7;
				}
				return SHELL_PATHS.POWERSHELL_LEGACY;
			}
			if (profile?.path) {
				return profile.path;
			}
			if (profile?.source === "WSL" || defaultProfileName.toLowerCase().includes("wsl")) {
				return SHELL_PATHS.WSL_BASH;
			}
			return SHELL_PATHS.CMD;
		}
		return profile?.path || null;
	}

	fireTaskDone(taskId: string) {
		this._onDidTaskDone.fire({ taskId });
	}

	fireTaskError(taskId: string) {
		this._onDidTaskError.fire({ taskId });
	}

	fireToolCall(taskId: string, toolName: string, toolParam: { [key: string]: any }): any {
		return this._onDidToolCall.fire({ taskId, toolName, toolParam });
	}

	fireReceiveMessage(taskId: string, message: { [key: string]: any }): any {
		return this._onDidReceiveMessage.fire({ taskId, message: message });
	}
}

registerSingleton(IPluginTaskService, PluginTaskService, InstantiationType.Eager);

