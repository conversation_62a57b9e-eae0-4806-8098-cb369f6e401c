import { createDecorator } from '../../../../platform/instantiation/common/instantiation.js';
import { InstantiationType, registerSingleton } from '../../../../platform/instantiation/common/extensions.js';
import { Disposable } from '../../../../base/common/lifecycle.js';
import { IWorkspaceContextService } from '../../../../platform/workspace/common/workspace.js';
import { getWorkspaceUri } from '../common/helpers/path.js';
import { PlanType } from '../common/remoteAgentServiceType.js';


const folderFilesMap: Map<string, string[]> = new Map();

// 步骤状态类型
type StepStatus = 'not_started' | 'in_progress' | 'completed' | 'failed' | 'blocked';

interface FileInfo {
	name: string;
	path: string;
}

export type State = {
	[threadId: string]: {
		taskId: string;
		task: string;
		taskContext: string;
		steps: Step[];
	};
}

export type Step = {
	content: string;
	detail: string;
	note: string;
	status: StepStatus;
	dependencies: {
		stepIndex: number;
		stepResult: string;
	}[];
	result?: string;
}

export interface ITodolistService {
	readonly _serviceBrand: undefined;
	state: State;

	getTodolist(threadId: string): number[];
	update(threadId: string, plan: PlanType, isReplan: boolean): void;
	markStep(threadId: string, stepIndex: number, stepStatus?: StepStatus, stepNotes?: string): void;
	hasFailedOrBlockedSteps(threadId: string): boolean;
	setTodolistResult(threadId: string, stepIndex: number, result: string): void;
	getTodolistResult(threadId: string, stepIndex: number): string;

	getAllStep(threadId: string): Step[] | null;

	getLastTodolist(threadId: string): Step | undefined;
	clear(threadId: string): void;
}

export const ITodolistService = createDecorator<ITodolistService>('todolistService');

export class TodolistService extends Disposable implements ITodolistService {
	readonly _serviceBrand: undefined;

	state: State = {};

	constructor(
		@IWorkspaceContextService private readonly workspaceContextService: IWorkspaceContextService
	) {
		super();
	}

	public getTodolist(threadId: string): number[] {
		const todolist: number[] = [];

		for (let stepIndex = 0; stepIndex < this.state[threadId].steps.length; stepIndex++) {
			const step = this.state[threadId].steps[stepIndex];
			const dependencies = step.dependencies;

			// 检查所有依赖是否都已完成
			const allDepsCompleted = dependencies.every(dep =>
				this.state[threadId].steps[dep.stepIndex].status !== 'not_started'
			);

			// 检查步骤本身是否未开始
			if (allDepsCompleted && step.status === 'not_started') {
				todolist.push(stepIndex);
			}
		}

		return todolist;
	}

	public update(
		threadId: string,
		plan: PlanType,
		isReplan: boolean
	): void {
		if (!this.state[threadId]) {
			this.state[threadId] = {
				taskId: '',
				task: '',
				taskContext: '',
				steps: []
			};
		}

		if (plan) {
			const newSteps: Step[] = [];
			this.state[threadId].taskId = plan.id;
			this.state[threadId].task = plan.params.task;
			this.state[threadId].taskContext = plan.params.taskContext;

			let startReplaceIndex = 0;
			if (isReplan && this.state[threadId].steps.length > 0) {
				const failedOrBlockedIndex = this.state[threadId].steps.findIndex(
					step => step.status === 'blocked' || step.status === 'failed'
				);

				if (failedOrBlockedIndex !== -1) {
					for (let i = 0; i < failedOrBlockedIndex; i++) {
						newSteps.push(this.state[threadId].steps[i]);
					}
					startReplaceIndex = failedOrBlockedIndex;
				}
			}

			// 处理所有步骤
			plan.params.taskPlan.forEach((step, index) => {
				if (isReplan && index < startReplaceIndex) {
					return;
				}

				if (this.isStepExists(threadId, step.content)) {
					let step_ = this.state[threadId].steps[this.getStepIndex(threadId, step.content)];
					if (!isReplan && step_.status !== 'not_started') {
						// 保持已开始的步骤状态
						step_.dependencies = []
						newSteps.push(step_);
					} else {
						// 保持未开始的步骤
						step_ = {
							...step_,
							detail: step.detail,
							dependencies: []
						}
						newSteps.push(step_);
					}
				} else {
					// 新步骤
					newSteps.push({
						content: step.content,
						detail: step.detail,
						note: '',
						status: 'not_started',
						dependencies: []
					});
				}
			});

			this.state[threadId].steps = newSteps;
		}

		if (plan.params.dependencies) {
			plan.params.dependencies.forEach((value, key) => {
				this.state[threadId].steps[key].dependencies = value.map(dep => ({
					stepIndex: dep,
					stepResult: ''
				}));
			});
		} else if (plan.params.taskPlan) {
			for (let i = 1; i < this.state[threadId].steps.length; i++) {
				this.state[threadId].steps[i].dependencies = [{
					stepIndex: i - 1,
					stepResult: ''
				}];
			}
		}
	}

	private isStepExists(threadId: string, step: string): boolean {
		return this.state[threadId].steps.some(s => s.content === step);
	}

	private getStepIndex(threadId: string, step: string): number {
		return this.state[threadId].steps.findIndex(s => s.content === step);
	}

	public markStep(
		threadId: string,
		stepIndex: number,
		stepStatus?: StepStatus,
		stepNotes?: string,
		stepResult?: string
	): void {
		if (!this.state[threadId]) {
			return;
		}

		if (stepIndex < 0 || stepIndex >= this.state[threadId].steps.length) {
			throw new Error(`Invalid step_index: ${stepIndex}. Valid indices range from 0 to ${this.state[threadId].steps.length - 1}.`);
		}

		const step = this.state[threadId].steps[stepIndex];

		if (stepStatus) {
			step.status = stepStatus;
		}

		if (stepNotes !== undefined) {
			const [processedNotes] = this.processTextWithWorkspace(stepNotes);
			step.note = processedNotes;
		}

		if (stepStatus === 'completed' && stepResult !== undefined) {
			step.result = stepResult;
			this.state[threadId].steps.forEach(s => {
				s.dependencies.forEach(dep => {
					if (dep.stepIndex === stepIndex) {
						dep.stepResult = stepResult;
					}
				})
			})
		}

		// 验证完成状态
		if (stepStatus === 'completed') {
			const dependencies = step.dependencies;
			const allDepsCompleted = dependencies.every(dep =>
				this.state[threadId].steps[dep.stepIndex].status === 'completed'
			);

			if (!allDepsCompleted) {
				throw new Error(`Cannot complete step ${stepIndex} before its dependencies are completed`);
			}
		}
	}

	public hasFailedOrBlockedSteps(threadId: string): boolean {
		if (!this.state[threadId]) {
			return true;
		}
		return this.state[threadId].steps.some(step => step.status === 'failed' || step.status === 'blocked');
	}

	public setTodolistResult(threadId: string, stepIndex: number, stepResult: string): void {
		this.state[threadId].steps[stepIndex].result = stepResult;
		this.state[threadId].steps.forEach(s => {
			s.dependencies.forEach(dep => {
				if (dep.stepIndex === stepIndex) {
					dep.stepResult = stepResult;
				}
			})
		})
	}

	public getTodolistResult(threadId: string, stepIndex: number): string {
		if (!this.state[threadId]) {
			return '';
		}
		return this.state[threadId].steps[stepIndex].result || '';
	}

	public getAllStep(threadId: string): Step[] | null {
		if (!this.state[threadId]) {
			return null;
		}
		return this.state[threadId].steps;
	}

	private isAllStepCompleted(threadId: string): boolean {
		if (!this.state[threadId]) {
			return false;
		}
		return this.state[threadId].steps.every(step => step.status === 'completed');
	}

	private findBlockedOrFailedStep(threadId: string): Step | undefined {
		if (!this.state[threadId]) {
			return undefined;
		}
		return this.state[threadId].steps.find(step => step.status === 'blocked' || step.status === 'failed');
	}

	public getLastTodolist(threadId: string): Step | undefined {
		if (!this.state[threadId]) {
			return undefined;
		}
		if (this.isAllStepCompleted(threadId)) {
			return this.state[threadId].steps[this.state[threadId].steps.length - 1];
		} else {
			return this.findBlockedOrFailedStep(threadId);
		}
	}

	public clear(threadId: string) {
		delete this.state[threadId];
	}

	private extractAndReplacePaths(text: string, folderName: string, workspacePath: string): [string, FileInfo[]] {
		const validExtensions = '(txt|md|pdf|docx|xlsx|csv|json|xml|html|png|jpg|jpeg|svg|py|ts|js)';

		// 路径文件模式
		const pathFilePattern = new RegExp(`([a-zA-Z]:\\\\[^\\s《》]+?\\.${validExtensions}|/[^\\s《》]+?\\.${validExtensions})`, 'g');

		// 中文书名号引用的文件名
		const quotedFilePattern = new RegExp(`《([^《》\\s]+?\\.${validExtensions})》`, 'g');

		const resultList: FileInfo[] = [];

		// 初始化该文件夹的文件列表
		if (!folderFilesMap.has(folderName)) {
			folderFilesMap.set(folderName, []);
		}

		const replacePathFile = (match: string, fullPath: string): string => {
			const filename = fullPath.split(/[/\\]/).pop() || '';
			return `${folderName}/${filename}`;
		};

		const replaceQuotedFile = (match: string, filename: string): string => {
			return `${folderName}/${filename}`;
		};

		let newText = text.replace(pathFilePattern, replacePathFile);
		newText = newText.replace(quotedFilePattern, replaceQuotedFile);

		return [newText, resultList];
	}

	private processTextWithWorkspace(text: string): [string, FileInfo[]] {
		const { workspaceUri, workspaceName } = getWorkspaceUri(this.workspaceContextService);
		if (!workspaceName) {
			return [text, []];
		}
		return this.extractAndReplacePaths(text, workspaceName, workspaceUri.fsPath);
	}
}

registerSingleton(ITodolistService, TodolistService, InstantiationType.Eager);
