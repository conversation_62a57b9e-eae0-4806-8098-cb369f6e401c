import { createDecorator } from '../../../../../platform/instantiation/common/instantiation.js';
import { InstantiationType, registerSingleton } from '../../../../../platform/instantiation/common/extensions.js';
import { Disposable } from '../../../../../base/common/lifecycle.js';
import { IChatThreadService, PluginMessageOpts, userMessageOpts } from '.././chatThreadType.js';
import { IChatAgentService } from './chatAgentService.js';
import { ITodolistService } from '../todolistService.js';
import { ChatMode } from '../../common/codeseekSettingsService.js';
import { FeatureName, FeatureNames } from '../../common/codeseekSettingsTypes.js';
import { AgentParamsFromIde, IPluginTaskService } from '../pluginTaskService.js';
import { ICodeseekRemoteAgentService } from '../../common/remoteAgentService.js';
import { IInstantiationService } from '../../../../../platform/instantiation/common/instantiation.js';
import { Feedback, PlanAgentTopic, PlanType, RemoteAgentMessageType } from '../../common/remoteAgentServiceType.js';
import { ICodeseekLogger } from '../../common/codeseekLogService.js';

export type AgentCreatePlanMessageOpts = {
	messageType: RemoteAgentMessageType.createPlan;
	containerId: string;
	threadId: string;
	featureName: FeatureName;
	messages: string;

	agentParamsFromPlugin?: { [key: string]: any };
	agentParamsFromIde?: AgentParamsFromIde;
};

export type FeedbackMessageOpts = {
	messageType: RemoteAgentMessageType.feedback;
	containerId: string;
	threadId: string;
	feedback: Feedback;
	featureName: FeatureName;
};

export type PlanAgentMessage = AgentCreatePlanMessageOpts | FeedbackMessageOpts;

export interface IPlanAgentService {
	readonly _serviceBrand: undefined;

	createPlan(containerId: string, threadId: string, userMessageOpts: userMessageOpts, feedback?: Feedback): Promise<void>;
	abort(cancelToken: string): void;
}

export const IPlanAgentService = createDecorator<IPlanAgentService>('codeseekPlanAgentService');
export class PlanAgentService extends Disposable implements IPlanAgentService {
	readonly _serviceBrand: undefined;

	constructor(
		@ICodeseekRemoteAgentService private readonly remoteAgentService: ICodeseekRemoteAgentService,
		@ITodolistService private readonly todolistService: ITodolistService,
		@IChatAgentService private readonly chatAgentService: IChatAgentService,
		@IInstantiationService private readonly instantiationService: IInstantiationService,
		@IPluginTaskService private readonly pluginTaskService: IPluginTaskService,
		@ICodeseekLogger private readonly logger: ICodeseekLogger,
	) {
		super();
	}

	public async createPlan(containerId: string, threadId: string, userMessageOpts: userMessageOpts, feedback?: Feedback): Promise<void> {
		this.logger.info(`the containerId: ${containerId} start create plan`);
		if (userMessageOpts.chatMode === ChatMode.Ask) {
			this.logger.info(`the containerId: ${containerId}, the chatMode: ${userMessageOpts.chatMode}`);
			this.todolistService.clear(threadId);
			const plan: PlanType = await this.chatAgentService.createPlan(containerId, threadId, userMessageOpts);
			this.logger.info(`the plan: ${JSON.stringify(plan)}`);
			this.todolistService.update(threadId, plan, false);
		} else if (userMessageOpts.chatMode === ChatMode.Agent) {
			this.logger.info(`the containerId: ${containerId}, the chatMode: ${userMessageOpts.chatMode}`);
			let agentMessage: PlanAgentMessage;
			if (feedback) {
				agentMessage = {
					messageType: RemoteAgentMessageType.feedback,
					containerId,
					threadId,
					feedback: feedback,
					featureName: FeatureNames.CtrlL,
				};
			} else {
				this.todolistService.clear(threadId);
				const promptEnhancementResult = await this.chatAgentService.contextEnhancement(containerId, threadId, userMessageOpts);
				let agentCreatePlanMessageOpts_;
				if (userMessageOpts.from === 'Plugin') {
					agentCreatePlanMessageOpts_ = {
						messages: userMessageOpts.userMessage,
						agentParamsFromPlugin: (userMessageOpts as PluginMessageOpts).taskInfo.agentParamsFromPlugin,
						agentParamsFromIde: (userMessageOpts as PluginMessageOpts).taskInfo.agentParamsFromIde,
					}
				} else {
					agentCreatePlanMessageOpts_ = {
						messages: userMessageOpts.userMessage,
						agentParamsFromPlugin: {
							"topic": PlanAgentTopic,
							"user_input": userMessageOpts.userMessage,
							"task_context": promptEnhancementResult.content,
						},
						agentParamsFromIde: await this.pluginTaskService.provideIdeParams(),
					}
				}
				agentMessage = {
					messageType: RemoteAgentMessageType.createPlan,
					...agentCreatePlanMessageOpts_,
					containerId,
					threadId,
					featureName: FeatureNames.CtrlL,
				}
			}
			const plan = await this.createPlanFromRemoteAgent(agentMessage);
			if (plan) {
				this.logger.info(`the plan: ${JSON.stringify(plan)}`);
				this.todolistService.update(threadId, plan, agentMessage.messageType === 'feedback');
			}
		}
	}

	private async createPlanFromRemoteAgent(agentMessage: PlanAgentMessage): Promise<PlanType | undefined> {
		const { containerId, threadId } = agentMessage;
		let plan_: PlanType | undefined;
		const agentLoop = async () => {
			let res_: () => void;
			const awaitable = new Promise<void>((res, rej) => { res_ = res; });
			const cancelToken = this.remoteAgentService.sendMessage({
				agentMessage,
				onText: () => { },
				onToolCall: ({ fullText, plan }) => {
					plan_ = plan;
				},
				onFinalMessage: async ({ fullText }) => {
					res_()
				},
				onError: (error) => {
					const chatThreadService = this.instantiationService.invokeFunction(accessor => accessor.get(IChatThreadService));
					chatThreadService.finishStreamingTextMessage(containerId, threadId, '', error);
					res_();
				},
			});
			if (cancelToken === null) throw new Error('cancelToken is null');
			const chatThreadService = this.instantiationService.invokeFunction(accessor => accessor.get(IChatThreadService));
			chatThreadService.setStreamState(containerId, threadId, { streamingToken: cancelToken, isStreaming: true });

			await awaitable;
		};

		agentLoop();
		return plan_;
	}

	public abort(cancelToken: string) {
		this.logger.info(`PlanAgentService: aborting requestId: ${cancelToken}`);
		this.chatAgentService.abort(cancelToken);
		this.remoteAgentService.abort(cancelToken);
	}
}

registerSingleton(IPlanAgentService, PlanAgentService, InstantiationType.Eager);
