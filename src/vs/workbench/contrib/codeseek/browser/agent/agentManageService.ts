import { createDecorator, IInstantiationService } from '../../../../../platform/instantiation/common/instantiation.js';
import { InstantiationType, registerSingleton } from '../../../../../platform/instantiation/common/extensions.js';
import { Disposable } from '../../../../../base/common/lifecycle.js';
import { ICodeseekLogger } from '../../common/codeseekLogService.js';
import { userMessageOpts } from '../chatThreadType.js';
import { IPlanAgentService } from './planAgentService.js';
import { ITodolistService } from '../todolistService.js';
import { CodeAgentService } from './codeAgentService.js';
import { ChatMode } from '../../common/codeseekSettingsService.js';
import { Feedback } from '../../common/remoteAgentServiceType.js';
import { Emitter, Event } from '../../../../../base/common/event.js';

export interface IAgentManageService {
	readonly _serviceBrand: undefined;

	onDidAbort: Event<string>;

	agentLoop(containerId: string, threadId: string, userMessageOpts: userMessageOpts): void;

	abort(cancelToken: string): void;
}

export const IAgentManageService = createDecorator<IAgentManageService>('codeseekAgentManageService');
export class AgentManageService extends Disposable implements IAgentManageService {
	readonly _serviceBrand: undefined;
	private isAborted = false;

	private readonly _onDidAbort = new Emitter<string>();
	readonly onDidAbort: Event<string> = this._onDidAbort.event;

	constructor(
		@ICodeseekLogger private readonly logger: ICodeseekLogger,
		@ITodolistService private readonly todolistService: ITodolistService,
		@IPlanAgentService private readonly planAgentService: IPlanAgentService,
		@IInstantiationService private readonly instantiationService: IInstantiationService,
	) {
		super();
	}

	public async agentLoop(containerId: string, threadId: string, userMessageOpts: userMessageOpts) {
		let shouldSendAnotherMessage = true;
		this.isAborted = false;
		let feedback: Feedback | undefined;
		while (shouldSendAnotherMessage) {
			if (this.isAborted) break;
			shouldSendAnotherMessage = false;
			await this.planAgentService.createPlan(containerId, threadId, userMessageOpts, feedback);
			if (this.todolistService.getTodolist(threadId).length === 0 || this.isAborted) {
				break;
			}

			const act = async (stepIndex: number): Promise<void> => {
				this.logger.info(`Starting execution of step ${stepIndex}`);
				const codeAgentService = this.instantiationService.createInstance(CodeAgentService);
				await codeAgentService.act(containerId, threadId, stepIndex, userMessageOpts);
			}

			while (true && !this.isAborted) {
				const todolist = this.todolistService.getTodolist(threadId);
				if (todolist.length === 0 || this.todolistService.hasFailedOrBlockedSteps(threadId)) {
					break;
				}
				const promises = todolist.map(stepIndex => act(stepIndex));
				await Promise.all(promises);
			}

			if (userMessageOpts.chatMode === ChatMode.Agent && !this.isAborted) {
				const lastStep = this.todolistService.getLastTodolist(threadId);
				if (lastStep) {
					feedback = {
						taskId: this.todolistService.state[threadId].taskId,
						taskResult: lastStep.status === 'completed',
						taskSummary: lastStep.result ?? '',
					};
					shouldSendAnotherMessage = true;
				}
			}
		}
	}

	public abort(cancelToken: string) {
		this.logger.info(`AgentManageService: aborting requestId: ${cancelToken}`);
		this.isAborted = true;
		this.todolistService.clear(cancelToken);
		this.planAgentService.abort(cancelToken);
		this._onDidAbort.fire(cancelToken);
	}
}

registerSingleton(IAgentManageService, AgentManageService, InstantiationType.Eager);
