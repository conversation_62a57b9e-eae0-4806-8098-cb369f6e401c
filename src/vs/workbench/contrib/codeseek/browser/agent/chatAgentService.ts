import { Disposable } from '../../../../../base/common/lifecycle.js';
import { createDecorator } from '../../../../../platform/instantiation/common/instantiation.js';
import { userMessageOpts, ChatMessage, ToolMessage, IChatThreadService } from '../chatThreadType.js';
import { ICodeseekLogger } from '../../common/codeseekLogService.js';
import { FeatureNames } from '../../common/codeseekSettingsTypes.js';
import { ILLMMessageService } from '../../common/llmMessageService.js';
import { summaryUserMessage, summarySystemMessage } from '../../common/prompt/prompts.js';
import { IDirectoryStrService } from '../../common/directoryStrService.js';
import { IWorkspaceContextService } from '../../../../../platform/workspace/common/workspace.js';
import { IEditorService } from '../../../../../workbench/services/editor/common/editorService.js';
import { EditorsOrder } from '../../../../../workbench/common/editor.js';
import { getRelativePath } from '../../common/helpers/path.js';
import { InstantiationType, registerSingleton } from '../../../../../platform/instantiation/common/extensions.js';
import { IConvertToLLMMessageService } from '../convertToLLMMessageService.js';
import { LLMChatMessage, SendLLMType } from '../../common/llmMessageTypes.js';
import { generateUuid } from '../../../../../base/common/uuid.js';
import { PlanType } from '../../common/remoteAgentServiceType.js';
import { ToolCallResultCode, ToolCallResultType } from '../../common/toolsServiceTypes.js';
import { IToolsService } from '../../common/toolsService.js';

export type PromptEnhancementResult = {
	status: 'success' | 'failed';
	content: string;
	error?: string;
}

export interface IChatAgentService {
	readonly _serviceBrand: undefined;

	createPlan(containerId: string, threadId: string, userMessageOpts: userMessageOpts): Promise<PlanType>;
	abort(cancelToken: string): void;
	contextEnhancement(containerId: string, threadId: string, userMessageOpts: userMessageOpts): Promise<PromptEnhancementResult>;

}

export interface IOpenTabInfo {
	name: string;
	resource: string;
	isDirty: boolean;
	groupId?: number;
}

export const IChatAgentService = createDecorator<IChatAgentService>('codeseekChatAgentService');


export class ChatAgentService extends Disposable implements IChatAgentService {
	readonly _serviceBrand: undefined;
	constructor(
		@IChatThreadService private readonly chatThreadService: IChatThreadService,
		@ILLMMessageService private readonly llmMessageService: ILLMMessageService,
		@ICodeseekLogger private readonly codeseekLogService: ICodeseekLogger,
		@IDirectoryStrService private readonly directoryStrService: IDirectoryStrService,
		@IWorkspaceContextService private readonly workspaceService: IWorkspaceContextService,
		@IEditorService private readonly editorService: IEditorService,
		@IToolsService private readonly toolsService: IToolsService,
		@IConvertToLLMMessageService private readonly convertToLLMMessageService: IConvertToLLMMessageService,
	) {
		super();
	}

	public async createPlan(containerId: string, threadId: string, userMessageOpts: userMessageOpts): Promise<PlanType> {
		let prompt = await this.contextEnhancement(containerId, threadId, userMessageOpts);
		const id = generateUuid();
		return {
			id,
			params: {
				task: userMessageOpts.userMessage,
				taskContext: prompt.content,
				taskPlan: [{ content: userMessageOpts.userMessage, detail: prompt.content }],
				dependencies: new Map(),
			}
		};
	}

	public abort(cancelToken: string): void {
		this.codeseekLogService.info(`ChatAgentService: aborting requestId: ${cancelToken}`);
		this.llmMessageService.abort(cancelToken);
	}

	async chatHistorySummary(containerId: string, threadId: string, userInput: string): Promise<string> {
		const previousMessages = this.formatChatHistory(containerId);
		if (!previousMessages) {
			return '';
		}
		const userMessage = summaryUserMessage(previousMessages, userInput);
		let res_: () => void;
		const awaitable = new Promise<void>((res, rej) => { res_ = res; });
		let summary = '';
		const cancelToken = this.llmMessageService.sendLLMMessage({
			containerId,
			useProviderFor: FeatureNames.CtrlL,
			messagesType: 'chatMessages',
			messages: [{ role: 'system', content: summarySystemMessage }, { role: 'user', content: userMessage }],
			onText: () => { },
			onFinalMessage: (params) => {
				this.codeseekLogService.info('chatHistorySummary, got result: ', params.fullText);
				summary = params.fullText;
				res_();
			},
			onError: (error) => {
				this.chatThreadService.finishStreamingTextMessage(containerId, threadId, '', error);
				this.codeseekLogService.error('chatHistorySummary, error: ', error.message, error.fullError);
				res_();
			},
			logging: { loggingName: 'ChatAgentService' }
		});
		if (!cancelToken) return '';
		this.chatThreadService.setStreamState(containerId, threadId, { streamingToken: cancelToken, isStreaming: true });
		await awaitable;
		return summary;
	}

	/**
	 * 将 ChatMessage 数组格式化为可读的历史记录字符串
	 * @param containerId 容器ID
	 * @returns 格式化后的历史记录字符串
	 */
	formatChatHistory(containerId: string): string {
		const messages = this.chatThreadService.getCurrentThreadMessages(containerId);
		const assistantMessageLen = messages.filter(msg => msg.role === 'assistant').length;
		if (!messages || messages.length === 0 || assistantMessageLen === 0) {
			return '';
		}

		return messages.filter(msg => msg.role !== 'system').map((msg, index) => {
			// 基本格式：[角色]: 内容
			const roleLabel = this.getRoleLabel(msg.role);
			let content = '';

			if (msg.role === 'user') {
				// 用户消息优先使用 displayContent
				content = msg.displayContent ?? msg.content ?? '';
			} else if (msg.role === 'assistant') {
				// 助手消息优先使用 displayContent
				content = msg.displayContent ?? msg.content ?? '';
			} else if (msg.role === 'tool') {
				// 工具消息需要特殊处理
				content = this.formatToolMessage(msg);
			}

			// 添加消息序号
			const messageNumber = `#${index + 1}`;

			// 基本格式
			let formattedMessage = `${messageNumber} ${roleLabel}: ${content}`;

			// 用户消息，添加选择内容信息
			if (msg.role === 'user') {
				if (msg.state?.stagingSelections?.length > 0) {
					// const selectionsInfo = `\n    选择内容: ${msg.state.stagingSelections.length} 项`;
					// formattedMessage += selectionsInfo;
				}
			}

			return formattedMessage;
		}).join('\n\n');
	}

	/**
	 * 获取角色的显示标签
	 */
	private getRoleLabel(role: string): string {
		switch (role) {
			case 'user': return '用户';
			case 'assistant': return '助手';
			case 'system': return '系统';
			case 'tool': return '工具';
			default: return role;
		}
	}

	/**
	 * 格式化工具消息
	 */
	private formatToolMessage(toolMsg: ChatMessage): string {
		if (toolMsg.role === 'tool') {
			const name = (toolMsg as ToolMessage<any>).name || '工具';
			const result = toolMsg.content ?? '无结果';
			// 始终使用详细格式
			return `${name}\n    结果: ${result}`;
		}
		return toolMsg.content ?? '';
	}

	// 获取打开的标签页URI相对路径
	openTabs(): string {
		try {
			// 获取所有打开的编辑器，按顺序排列
			const editorIdentifiers = this.editorService.getEditors(EditorsOrder.SEQUENTIAL);

			const relativePaths = editorIdentifiers
				.filter(editor => editor.editor.resource !== undefined)
				.map(editor => editor.editor.resource?.fsPath)
				.filter(path => path !== undefined)
				.map(path => getRelativePath(this.workspaceService, path));
			return relativePaths.join('\n');
		} catch (error) {
			this.codeseekLogService.error('获取打开的标签页失败:', error);
			return '';
		}
	}

	async contextEnhancement(containerId: string, threadId: string, userMessageOpts: userMessageOpts): Promise<PromptEnhancementResult> {
		const chatHistory = await this.chatHistorySummary(containerId, threadId, userMessageOpts.userMessage);
		this.codeseekLogService.info('the summary chat history: ', chatHistory);
		const cutOffMessage = `...Directories string cut off, use tools to read more...`;
		const directoryStrDetail = await this.directoryStrService.getAllDirectoriesStr({ cutOffMessage });
		// 获取工作区根目录
		const workingDirectory = this.workspaceService.getWorkspace().folders[0]?.name || '';
		const messages = this.convertToLLMMessageService.prepareLLMChatMessages({
			messagesType: 'chatAgentMessages',
			containerId,
			threadId,
			userMessage: userMessageOpts.userMessage,
			chatHistory,
			directoryStrDetail,
			workingDirectory,
			openTabs: this.openTabs(),
		});

		const result = await this.reAct(messages, containerId, threadId, userMessageOpts);
		return { status: "success", content: result };
	}

	async reAct(messages: SendLLMType, containerId: string, threadId: string, userMessageOpts: userMessageOpts): Promise<string> {
		let shouldSendAnotherMessage = true;
		let nAttempts = 0;
		let result = '';
		while (shouldSendAnotherMessage) {
			shouldSendAnotherMessage = false;
			nAttempts += 1;

			let res_: () => void;
			const awaitable = new Promise<void>((res, rej) => { res_ = res; });

			let llmMessages: LLMChatMessage[];
			if (messages.messagesType === 'chatMessages') {
				llmMessages = messages.messages as LLMChatMessage[];
			} else {
				throw new Error('Expected chatMessages type for promptEnhancement');
			}
			const cancelToken = this.llmMessageService.sendLLMMessage({
				containerId,
				useProviderFor: FeatureNames.CtrlL,
				messagesType: 'chatMessages',
				messages: llmMessages, // Only pass LLMChatMessage[]
				onText: ({ fullText }) => {
					this.chatThreadService.setStreamState(containerId, threadId, { messageSoFar: fullText });
				},
				onFinalMessage: async ({ fullText, toolCalls }) => {
					this.codeseekLogService.info('the fullText: ', fullText);
					result = fullText;
					if (toolCalls && toolCalls.length > 0) {
						this.chatThreadService.setStreamState(containerId, threadId, { messageSoFar: fullText });
					}
					for (const tool of toolCalls ?? []) {
						const toolCallResult: ToolCallResultType = await this.toolsService.executeTool(containerId, threadId, tool, userMessageOpts);
						if (toolCallResult.code === ToolCallResultCode.failure) {
							shouldSendAnotherMessage = false;
							break;
						}
						this.chatThreadService.addMessageToThread(containerId, threadId, { role: 'tool', name: tool.name, params: tool.params, id: tool.id, content: toolCallResult.content, result: toolCallResult.result, containerId, threadId });
						llmMessages.push({ role: 'user', content: toolCallResult.content });
						shouldSendAnotherMessage = true;
					}
					res_();
				},
				onError: (error) => {
					this.codeseekLogService.error('chatHistopromptEnhancementySummary, error: ', error.message, error.fullError);
					this.chatThreadService.finishStreamingTextMessage(containerId, threadId, '', error);
					res_();
				},
				logging: { loggingName: 'ChatAgentService' }
			});
			if (cancelToken === null) break;
			this.chatThreadService.setStreamState(containerId, threadId, { streamingToken: cancelToken, isStreaming: true });
			await awaitable;
		}
		return result;
	}

}

registerSingleton(IChatAgentService, ChatAgentService, InstantiationType.Eager);
