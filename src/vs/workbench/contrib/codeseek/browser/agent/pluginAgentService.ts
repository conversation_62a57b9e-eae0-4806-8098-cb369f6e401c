import { Disposable } from '../../../../../base/common/lifecycle.js';
import { userMessageOpts } from '.././chatThreadType.js';
import { ICodeseekRemoteAgentService } from '../../common/remoteAgentService.js';


export interface IPluginAgent {
	readonly _serviceBrand: undefined;

	act(containerId: string, threadId: string, stepIndex: number, userMessageOpts: userMessageOpts): Promise<void>;
	abort: (requestId: string) => void;
}

export class PluginAgentService extends Disposable implements IPluginAgent {
	readonly _serviceBrand: undefined;

	constructor(
		@ICodeseekRemoteAgentService private readonly remoteAgentService: ICodeseekRemoteAgentService,
	) {
		super();
	}

	public async act(containerId: string, threadId: string, stepIndex: number, userMessageOpts: userMessageOpts): Promise<void> {
		return;
	}

	abort(requestId: string) {
		this.remoteAgentService.abort(requestId);
	}
}
