import { Disposable } from '../../../../../base/common/lifecycle.js';
import { ITodolistService } from '../todolistService.js';
import { IToolsService } from '../../common/toolsService.js';
import { IChatThreadService, userMessageOpts } from '.././chatThreadType.js';
import { ToolCallResultCode, ToolCallResultType, ToolNameEnum } from '../../common/toolsServiceTypes.js';
import { ICodeseekLogger } from '../../common/codeseekLogService.js';
import { ILLMMessageService } from '../../common/llmMessageService.js';
import { FeatureNames } from '../../common/codeseekSettingsTypes.js';
import { CodeAgentConvertMessageOpts, IConvertToLLMMessageService } from '../convertToLLMMessageService.js';
import { IInstantiationService } from '../../../../../platform/instantiation/common/instantiation.js';
import { IAgentManageService } from './agentManageService.js';

const MAX_MESSAGES_SENT = 20;

export interface ICodeAgent {
	readonly _serviceBrand: undefined;

	act(containerId: string, threadId: string, stepIndex: number, userMessageOpts: userMessageOpts): Promise<void>;
}

export class CodeAgentService extends Disposable implements ICodeAgent {
	readonly _serviceBrand: undefined;

	constructor(
		@IChatThreadService private readonly chatThreadService: IChatThreadService,
		@ICodeseekLogger private readonly logger: ICodeseekLogger,
		@IToolsService private readonly toolsService: IToolsService,
		@ITodolistService private readonly todolistService: ITodolistService,
		@ILLMMessageService private readonly llmMessageService: ILLMMessageService,
		@IConvertToLLMMessageService private readonly convertToLLMMessageService: IConvertToLLMMessageService,
		@IInstantiationService private readonly instantiationService: IInstantiationService,
	) {
		super();
		this.instantiationService.invokeFunction(accessor => {
			const agentManageService = accessor.get(IAgentManageService);
			this._register(agentManageService.onDidAbort(cancelToken => {
				this.abort(cancelToken);
			}));
		});
	}

	public async act(containerId: string, threadId: string, stepIndex: number, userMessageOpts: userMessageOpts) {
		this.todolistService.markStep(threadId, stepIndex, 'in_progress');
		const steps = this.todolistService.getAllStep(threadId);
		if (!steps) return
		const agentLoop = async () => {

			let shouldSendAnotherMessage = true;
			let nAttempts = 0;

			while (shouldSendAnotherMessage) {
				shouldSendAnotherMessage = false;
				nAttempts += 1;

				let res_: () => void;
				const awaitable = new Promise<void>((res, rej) => { res_ = res; });
				const convertMessageOpts: CodeAgentConvertMessageOpts = {
					messagesType: 'codeAgentMessages',
					containerId,
					threadId,
					task: this.todolistService.state[threadId].task,
					taskContext: this.todolistService.state[threadId].taskContext,
					step: steps[stepIndex],
					internalTools: [
						ToolNameEnum.READ_FILE,
						ToolNameEnum.LIST_FILES,
						ToolNameEnum.PATHNAME_SEARCH,
						ToolNameEnum.SEARCH,
						ToolNameEnum.CREATE_FILE,
						ToolNameEnum.UPDATE_FILE,
						ToolNameEnum.APPROVE_REQUEST,
						ToolNameEnum.CODEBASE_SEARCH,
					],
				};
				const messages = this.convertToLLMMessageService.prepareLLMChatMessages(convertMessageOpts);
				const cancelToken = this.llmMessageService.sendLLMMessage({
					containerId,
					...messages,
					useProviderFor: FeatureNames.CtrlL,
					logging: { loggingName: userMessageOpts.chatMode },
					onText: ({ fullText }) => {
						this.chatThreadService.setStreamState(containerId, threadId, { messageSoFar: fullText });
					},
					onFinalMessage: async ({ fullText, toolCalls }) => {
						this.logger.info(`Final message, threadId: ${threadId}, fullText: `, fullText, `toolCalls: `, JSON.stringify(toolCalls));
						if (nAttempts > MAX_MESSAGES_SENT) {
							this.todolistService.markStep(threadId, stepIndex, 'blocked');
							this.chatThreadService.finishStreamingTextMessage(containerId, threadId, fullText);
							shouldSendAnotherMessage = false;
							res_();
						}

						if ((toolCalls?.length ?? 0) === 0) {
							this.chatThreadService.finishStreamingTextMessage(containerId, threadId, fullText);
							this.todolistService.markStep(threadId, stepIndex, 'completed', fullText);
						}
						else {
							this.chatThreadService.addMessageToThread(containerId, threadId, { role: 'assistant', content: fullText, displayContent: fullText });
							this.chatThreadService.setStreamState(containerId, threadId, { messageSoFar: undefined }); // clear streaming message
							for (const tool of toolCalls ?? []) {
								const toolCallResult: ToolCallResultType = await this.toolsService.executeTool(containerId, threadId, tool, userMessageOpts);
								if (toolCallResult.code === ToolCallResultCode.failure) {
									shouldSendAnotherMessage = false;
									break;
								}
								this.chatThreadService.addMessageToThread(containerId, threadId, { role: 'tool', name: tool.name, params: tool.params, id: tool.id, content: toolCallResult.content, result: toolCallResult.result, containerId, threadId });
								shouldSendAnotherMessage = true;

							}
						}
						res_();
					},
					onError: (error) => {
						this.todolistService.markStep(threadId, stepIndex, 'failed');
						this.todolistService.setTodolistResult(threadId, stepIndex, this.chatThreadService.streamState[containerId]?.[threadId]?.messageSoFar ?? '');
						this.chatThreadService.finishStreamingTextMessage(containerId, threadId, this.chatThreadService.streamState[containerId]?.[threadId]?.messageSoFar ?? '', error);
						res_();
					},
				});
				if (cancelToken === null) break;
				this.chatThreadService.setStreamState(containerId, threadId, { streamingToken: cancelToken, isStreaming: true });

				await awaitable;
			}
		};

		agentLoop();
	}

	public abort(cancelToken: string) {
		this.logger.info(`CodeAgentService: aborting requestId: ${cancelToken}`);
		this.llmMessageService.abort(cancelToken);
	}

}
