import { Disposable } from '../../../../base/common/lifecycle.js';
import { IChannel } from '../../../../base/parts/ipc/common/ipc.js';
import { InstantiationType, registerSingleton } from '../../../../platform/instantiation/common/extensions.js';
import { createDecorator } from '../../../../platform/instantiation/common/instantiation.js';
import { IMainProcessService } from '../../../../platform/ipc/common/mainProcessService.js';
import { LlmInfoConfig } from '../electron-main/llmMessage/llmInfoConfig.js';

export interface IRestfulApiService {
	readonly _serviceBrand: undefined;
	getAllModels(): Promise<LlmInfoConfig[]>;
}

export const IRestfulApiService = createDecorator<IRestfulApiService>('restfulApiService');

export class RestfulApiService extends Disposable implements IRestfulApiService {
	readonly _serviceBrand: undefined;
	private readonly channel: IChannel;

	constructor(
		@IMainProcessService private readonly mainProcessService: IMainProcessService
	) {
		super();
		this.channel = this.mainProcessService.getChannel('codeseek-channel-restful');
	}

	public async getAllModels(): Promise<LlmInfoConfig[]> {
		const models = await this.channel.call('getAllModels', []);
		console.log('models from restfulApiService', models);
		let allModels: LlmInfoConfig[] = [];

		if (models && Array.isArray(models)) {
			allModels = models.map((model: any) => {
				return {
					modelName: model.modelName,
					contextWindow: model.metadata.contextWindow ?? 32_000,
					maxOutputTokens: model.metadata.maxOutputTokens ?? 8_000,
					cost: model.metadata.cost ?? { input: 0, output: 0 },
					supportsFIM: model.metadata.supportsFIM ?? false,
					supportsTools: model.metadata.supportsTools ?? false,
					supportsSystemMessage: model.metadata.supportsSystemMessage ?? 'system-role',
					supportsReasoningOutput: model.metadata.supportsReasoningOutput ?? false,
					supportsApply: model.metadata.supportsApply ?? false,
					temperature: model.metadata.temperature
				} as LlmInfoConfig;
			}).filter(Boolean);
		} else {
			console.error('不支持的API响应格式');
			return [];
		}

		return allModels;
	}
}

registerSingleton(IRestfulApiService, RestfulApiService, InstantiationType.Delayed);
