import { Disposable } from '../../../../base/common/lifecycle.js';
import { URI } from '../../../../base/common/uri.js';
import { IChannel } from '../../../../base/parts/ipc/common/ipc.js';
import { Event } from '../../../../base/common/event.js';
import { InstantiationType, registerSingleton } from '../../../../platform/instantiation/common/extensions.js';
import { createDecorator } from '../../../../platform/instantiation/common/instantiation.js';
import { IMainProcessService } from '../../../../platform/ipc/common/mainProcessService.js';
import { IWorkspaceContextService } from '../../../../platform/workspace/common/workspace.js';
import { SymbolReference } from '../electron-main/codebase/symbolReferences.js';
import { ICodeseekSettingsService } from '../common/codeseekSettingsService.js';


/**
 * 渲染进程侧的 clangd 符号服务接口
 */
export interface IClangdSymbolService {
	readonly _serviceBrand: undefined;

	/**
	 * 获取符号引用
	 *
	 * @param filePath
	 * @param line
	 * @param character
	 */
	getSymbolReferences(filePath: URI, line: number, character: number): Promise<SymbolReference[]>
	didOpen(documentUri: string): Promise<void>;
}

export const IClangdSymbolService = createDecorator<IClangdSymbolService>('clangdSymbolService');

/**
 * 渲染进程侧的 clangd 符号服务实现
 */
export class ClangdSymbolService extends Disposable implements IClangdSymbolService {
	readonly _serviceBrand: undefined;
	private readonly channel: IChannel;
	private scopeDir: URI | undefined = undefined;
	private clangdCompileCommandsDir: string | undefined = undefined;

	constructor(
		@IMainProcessService private readonly mainProcessService: IMainProcessService,
		@IWorkspaceContextService private workspaceContextService: IWorkspaceContextService,
		@ICodeseekSettingsService private codeseekSettingsService: ICodeseekSettingsService
	) {
		super();

		this.channel = this.mainProcessService.getChannel('codeseek-channel-clangd');
		this.initialize();

		// 监听工作区变化，重新初始化
		this._register(this.workspaceContextService.onDidChangeWorkspaceFolders(() => {
			this.initialize();
		}));

		this.clangdCompileCommandsDir = this.codeseekSettingsService.state.globalSettings.clangdCompileCommandsDir;

		// 添加防抖，避免配置变更时频繁触发（如用户键入时）
		// 使用 Event.debounce 对事件进行防抖处理，延迟500ms
		this._register(Event.debounce<void, void>(
			this.codeseekSettingsService.onDidChangeState,
			(_last: void | undefined, event: void) => event,  // 简单传递事件
			500 // 500毫秒的防抖延迟
		)(() => {
			if (this.clangdCompileCommandsDir !== this.codeseekSettingsService.state.globalSettings.clangdCompileCommandsDir) {
				this.clangdCompileCommandsDir = this.codeseekSettingsService.state.globalSettings.clangdCompileCommandsDir;
				this.initialize();
			}
		}));
	}

	/**
	 * 获取符号引用
	 * @param filePath 文件路径
	 * @param line
	 * @param character
	 * @returns 符号引用数组
	 */
	public async getSymbolReferences(filePath: URI, line: number, character: number): Promise<SymbolReference[]> {
		if (!this.scopeDir) {
			return Promise.resolve([]);
		}
		return this.channel.call('getSymbolReferences', [this.scopeDir, filePath, line, character]);
	}

	public async didOpen(documentUri: string): Promise<void> {
		if (!this.scopeDir) {
			return Promise.resolve();
		}
		return this.channel.call('didOpen', [this.scopeDir, documentUri]);
	}

	/**
	 * 初始化所有工作区文件夹的索引
	 */
	private async initialize(): Promise<void> {
		console.log('initialize clangdCompileCommandsDir', this.clangdCompileCommandsDir);
		const workspace = await this.workspaceContextService.getCompleteWorkspace();
		this.scopeDir = workspace.folders.length > 0 ? workspace.folders[0].uri : undefined;
		if (!this.scopeDir) {
			return Promise.resolve();
		}
		this.channel.call('initialize', [this.scopeDir, this.clangdCompileCommandsDir]);
	}
}

registerSingleton(IClangdSymbolService, ClangdSymbolService, InstantiationType.Delayed);
