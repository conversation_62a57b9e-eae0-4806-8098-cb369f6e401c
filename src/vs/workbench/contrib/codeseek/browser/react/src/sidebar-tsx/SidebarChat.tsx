/*--------------------------------------------------------------------------------------
 *  Copyright 2025 ZTE, Inc. All rights reserved.
 *  Licensed under the Apache License, Version 2.0. See LICENSE.txt for more information.
 *--------------------------------------------------------------------------------------*/

import React, { ButtonHTMLAttributes, Fragment, KeyboardEvent, useCallback, useEffect, useMemo, memo, useRef, useState } from 'react';
import { ChatMessage, CodeSeekAsk, ToolMessage } from '../../.././chatThreadType.js';
import { AskReponseType, ToolNameEnum } from '../../../../../../../workbench/contrib/codeseek/common/toolsServiceTypes.js';
import { getIconClasses } from '../../../../../../../editor/common/services/getIconClasses.js';
import { useAccessor, useSidebarState, useChatThreadsState, useChatThreadsStreamState, useSettingsState, useChatContainerState } from '../util/services.js';

import { BlockCode } from '../markdown/BlockCode.js';
import { ChatMarkdownRender } from '../markdown/ChatMarkdownRender.js';
import { IDisposable } from '../../../../../../../base/common/lifecycle.js';
import { ErrorDisplay } from './ErrorDisplay.js';
import { TextAreaFns, CodeseekInputBox2, CodeseekButton, CodeseekButton2 } from '../util/inputs.js';
import { ModelDropdown, } from '../codeseek-settings-tsx/ModelDropdown.js';
import { ModeDropdown } from '../codeseek-settings-tsx/ModeDropdown.js';
import { SidebarThreadSelector } from './SidebarThreadSelector.js';
import { useScrollbarStyles } from '../util/useScrollbarStyles.js';
import { CODESEEK_CTRL_L_ACTION_ID } from '../../../actionIDs.js';
import { ChevronRight, Pencil, X } from 'lucide-react';
import { FeatureName, FeatureNames, isFeatureNameDisabled } from '../../../../../../../workbench/contrib/codeseek/common/codeseekSettingsTypes.js';
import { ChatMessageLocation } from '../../../aiRegexService.js';
import { ToolCallParamsType, ToolName } from '../../../../common/toolsServiceTypes.js';
import { ContextMenuOptionType, ContextMenuQueryItem } from '../contextMenu/context-mentions.js';
import { StagingSelectionItem, TerminalSelection } from '../../../../common/selectedFileService.js';
import { FileKind } from '../../../../../../../platform/files/common/files.js';
import { filenameToVscodeLanguage } from '../../../../common/helpers/detectLanguage.js';
import { highLightMentionsFormater } from '../shared/context-mentions.js';
import { URI } from '../../../../../../../base/common/uri.js';
import { ChatMode } from '../../../../common/codeseekSettingsService.js';



export const IconX = ({ size, className = '', ...props }: { size: number, className?: string } & React.SVGProps<SVGSVGElement>) => {
	return (
		<svg
			xmlns='http://www.w3.org/2000/svg'
			width={size}
			height={size}
			viewBox='0 0 24 24'
			fill='none'
			stroke='currentColor'
			className={className}
			{...props}
		>
			<path
				strokeLinecap='round'
				strokeLinejoin='round'
				d='M6 18 18 6M6 6l12 12'
			/>
		</svg>
	);
};


export const IconWarning = ({ size, className = '' }: { size: number, className?: string }) => {
	return (
		<svg
			className={className}
			stroke="currentColor"
			fill="currentColor"
			strokeWidth="0"
			viewBox="0 0 16 16"
			width={size}
			height={size}
			xmlns="http://www.w3.org/2000/svg"
		>
			<path
				fillRule="evenodd"
				clipRule="evenodd"
				d="M7.56 1h.88l6.54 12.26-.44.74H1.44L1 13.26 7.56 1zM8 2.28L2.28 13H13.7L8 2.28zM8.625 12v-1h-1.25v1h1.25zm-1.25-2V6h1.25v4h-1.25z"
			/>
		</svg>
	);
};


export const IconLoading = ({ className = '' }: { className?: string }) => {

	const [loadingText, setLoadingText] = useState('.');

	useEffect(() => {
		let intervalId;

		// Function to handle the animation
		const toggleLoadingText = () => {
			if (loadingText === '...') {
				setLoadingText('.');
			} else {
				setLoadingText(loadingText + '.');
			}
		};

		// Start the animation loop
		intervalId = setInterval(toggleLoadingText, 300);

		// Cleanup function to clear the interval when component unmounts
		return () => clearInterval(intervalId);
	}, [loadingText, setLoadingText]);

	return <div className={`${className}`}>{loadingText}</div>;

}


interface CodeseekChatAreaProps {
	// Required
	children: React.ReactNode; // This will be the input component

	// Form controls
	onSubmit: () => void;
	onAbort: () => void;
	isStreaming: boolean;
	isDisabled?: boolean;
	divRef?: React.RefObject<HTMLDivElement>;

	// UI customization
	containerId?: string;
	featureName: FeatureName;
	className?: string;
	showModeDropdown?: boolean;
	showModelDropdown?: boolean;
	showSelections?: boolean;

	selections?: StagingSelectionItem[]
	setSelections?: (s: StagingSelectionItem[]) => void
	// selections?: any[];
	// onSelectionsChange?: (selections: any[]) => void;

	onClickAnywhere?: () => void;
	// Optional close button
	onClose?: () => void;
	onShowContextMenu?: () => void;
}

export const CodeseekChatArea: React.FC<CodeseekChatAreaProps> = ({
	children,
	onSubmit,
	onAbort,
	onClose,
	onClickAnywhere,
	onShowContextMenu,
	divRef,
	isStreaming = false,
	isDisabled = false,
	className = '',
	showModeDropdown = true,
	showModelDropdown = true,
	featureName,
	showSelections = false,
	selections,
	setSelections,
	containerId,
}) => {
	const accessor = useAccessor()
	const quickEditStateService = accessor.get('IQuickEditStateService')
	const [showAddContentText, setShowAddContentText] = useState(false)
	useEffect(() => {
		setShowAddContentText(Array.isArray(selections) && selections.length === 0)
	}, [selections])

	const [showDetailSelection, setShowDetailSelection] = useState<StagingSelectionItem | undefined>(undefined);

	return (
		<div
			ref={divRef}
			className={`
                flex flex-col gap-1 relative input text-left shrink-0
                transition-all duration-200
                rounded-md
                bg-codeseek-bg-1
                border border-codeseek-border-3 focus-within:border-codeseek-border-1 hover:border-codeseek-border-1
                ${className}
            `}
			onClick={(e) => {
				onClickAnywhere?.()
			}}
		>
			{/* Selections section */}
			{showSelections && (
				<div className='flex flex-row gap-1'>
					<div className='flex flex-row items-center px-[2.5px] pb-[0.5px] border border-codeseek-border-3 rounded-sm cursor-pointer hover:bg-codeseek-bg-4'
						onClick={(e) => {
							onShowContextMenu?.();
							e.preventDefault();
							e.stopPropagation();
						}}
					>
						<span className='flex items-center justify-center codicon codicon-mention'
							style={{
								fontSize: '13px',
							}}
						/>
						{showAddContentText && (
							<span className='px-0.5 text-sm'>Add Context</span>
						)}
					</div>
					{selections && setSelections && (
						<SelectedFiles
							type='staging'
							selections={selections}
							setSelections={setSelections}
							setShowDetailSelIdx={(selIdx) => {
								if(selIdx) {
									setShowDetailSelection(selections[selIdx])
								} else {
									setShowDetailSelection(undefined)
								}
							}}
						/>

					)}

					{/* Close button (X) if onClose is provided */}
					{onClose && (
						<div className='absolute top-1 right-1 cursor-pointer z-2'>
							<IconX
								size={12}
								className="stroke-[2] opacity-80 text-codeseek-fg-3 hover:brightness-95"
								onClick={onClose}
							/>
						</div>
					)}
				</div>
			)}
			{showDetailSelection && showDetailSelection.selectionStr &&  showDetailSelection.type !== 'Terminal' &&
				<div
					className='w-full rounded-md pl-2 show-detail-sel'
					onClick={(e) => {
						e.stopPropagation(); // don't focus input box
					}}
				>
					<BlockCode
						tokenId={showDetailSelection.fileURI.toString(true)}
						initValue={showDetailSelection.selectionStr}
						language={filenameToVscodeLanguage(showDetailSelection.fileURI.path)}
						maxHeight={200}
						showScrollbars={true}
					/>
				</div>
			}
			{/* Input section */}
			<div className="relative w-full">
				{children}
				{onClose && !showSelections && (
					<div className='absolute -top-1 -right-2 cursor-pointer z-2'>
						<IconX
							size={12}
							className="stroke-[2] opacity-80 text-codeseek-fg-3 hover:brightness-95"
							onClick={onClose}
						/>
					</div>
				)}
			</div>

			{/* Bottom row */}
			<div className='flex flex-row items-end gap-1'>
				<div className='flex flex-row gap-1'>
					{showModeDropdown && (
						<div className='max-w-[150px] @@[&_select]:!codeseek-border-none @@[&_select]:!codeseek-outline-none flex-grow mt-[1px] pt-[1px]'
							onClick={(e) => { e.preventDefault(); e.stopPropagation(); }}>
							<ModeDropdown className='bg-codeseek-bg-4 rounded' containerId={containerId} />
						</div>
					)}
					{showModelDropdown && (
						<div className='max-w-[150px] @@[&_select]:!codeseek-border-none @@[&_select]:!codeseek-outline-none flex-grow'
							onClick={(e) => { e.preventDefault(); e.stopPropagation(); quickEditStateService.fireFocusChat() }}>
							<ModelDropdown featureName={featureName} containerId={containerId} />
						</div>
					)}
				</div>
				<div className='ml-auto'>
					{isStreaming ? (
						<ButtonStop
							featureName={featureName}
							onClick={onAbort}
						/>
					) : (
						<ButtonSubmit
							featureName={featureName}
							onClick={() => { !isDisabled && onSubmit() }}
							disabled={isDisabled}
						/>
					)}
				</div>
			</div>
		</div>
	);
};

const useResizeObserver = () => {
	const ref = useRef(null);
	const [dimensions, setDimensions] = useState({ height: 0, width: 0 });

	useEffect(() => {
		if (ref.current) {
			const resizeObserver = new ResizeObserver((entries) => {
				if (entries.length > 0) {
					const entry = entries[0];
					setDimensions({
						height: entry.contentRect.height,
						width: entry.contentRect.width
					});
				}
			});

			resizeObserver.observe(ref.current);

			return () => {
				if (ref.current)
					resizeObserver.unobserve(ref.current);
			};
		}
	}, []);

	return [ref, dimensions] as const;
};



type ButtonProps = ButtonHTMLAttributes<HTMLButtonElement> & {
	featureName?: FeatureName;
};

export const ButtonSubmit = ({ featureName, className, disabled, ...props }: ButtonProps & Required<Pick<ButtonProps, 'disabled'>>) => {
	return <span
		className={`flex flex-col justify-end input-icon-button ${disabled ? 'disabled' : ''} codicon codicon-send
			${className}
			${featureName === 'Ctrl+K' ? 'mr-0' : 'mr-1'}
		`}
		style={{
			fontSize: featureName === 'Ctrl+K' ? 12 : 13,
		}}
		{...props}
	/>
}

export const ButtonStop = ({ featureName, className, ...props }: ButtonProps) => {

	return <span
		className={`flex flex-col justify-end input-icon-button codicon codicon-stop-circle
			${className}
			${featureName === 'Ctrl+K' ? 'mr-0' : 'mr-1'}
		`}
		style={{
			fontSize: featureName === 'Ctrl+K' ? 12 : 15,
		}}
		{...props}
	/>
}

export const ButtonClose = ({ className, ...props }: ButtonHTMLAttributes<HTMLButtonElement>) => {

	return <span
		className={`input-icon-button codicon codicon-close
			${className}
		`}
		{...props}
	/>
}

export const ButtonDelete = ({ className, ...props }: ButtonHTMLAttributes<HTMLButtonElement>) => {

	return <span
		className={`input-icon-button codicon codicon-trash
			${className}
		`}
		{...props}
	/>
}


type ScrollToBottomContainerProps = {
	children: React.ReactNode,
	className?: string,
	style?: React.CSSProperties,
	scrollContainerRef: React.MutableRefObject<HTMLDivElement | null>
	onScrollToBottom?: (isAtBottom: boolean) => void
	disableAutoScrollRef?: React.MutableRefObject<boolean>
}

const BOTTOM_THRESHOLD = 20;
const ScrollToBottomContainer = ({ children, className, style, scrollContainerRef, onScrollToBottom, disableAutoScrollRef }: ScrollToBottomContainerProps) => {
	const [isAtBottom, setIsAtBottom] = useState(true); // Start at bottom

	const divRef = scrollContainerRef

	const scrollToBottom = () => {
		if (divRef.current) {
			divRef.current.scrollTop = divRef.current.scrollHeight;
		}
	};

	const onScroll = () => {
		const div = divRef.current;
		if (!div) return;

		const isBottom = Math.abs(
			div.scrollHeight - div.clientHeight - div.scrollTop
		) < BOTTOM_THRESHOLD;

		setIsAtBottom(isBottom);
		onScrollToBottom?.(isBottom);

		// 如果用户滚动到底部，重新启用自动滚动
		if (isBottom && disableAutoScrollRef) {
			disableAutoScrollRef.current = false;
		}
	};

	// When children change (new messages added)
	useEffect(() => {
		if (isAtBottom) {
			scrollToBottom();
		}
	}, [children, isAtBottom]); // Dependency on children to detect new messages

	// Initial scroll to bottom
	useEffect(() => {
		scrollToBottom();
	}, []);

	return (
		<div
			// options={{ vertical: ScrollbarVisibility.Auto, horizontal: ScrollbarVisibility.Auto }}
			ref={divRef}
			onScroll={onScroll}
			className={className}
			style={style}
		>
			{children}
		</div>
	);
};



const getBasename = (pathStr: string) => {
	// 'unixify' path
	pathStr = pathStr.replace(/[/\\]+/g, '/') // replace any / or \ or \\ with /
	const parts = pathStr.split('/') // split on /
	return parts[parts.length - 1]
}

export const SelectedFiles = (
	{ type, selections, setSelections, setShowDetailSelIdx, className }:
		| { type: 'past', selections: StagingSelectionItem[]; setSelections?: undefined; setShowDetailSelIdx?:(selIdx?: number) => void; className?: string }
		| { type: 'staging', selections: StagingSelectionItem[]; setSelections: ((newSelections: StagingSelectionItem[]) => void); setShowDetailSelIdx?:(selIdx?: number) => void; className?: string }
) => {

	const [curShowDetailSelIdx, setCurOpenedSelIdx] = useState<number | undefined>()
	// 添加用于跟踪悬停状态的数组
	const [hoveredIndices, setHoveredIndices] = useState<Record<number, boolean>>({})

	// state for tracking hover on clear all button
	const [isClearHovered, setIsClearHovered] = useState(false)

	const accessor = useAccessor()
	const commandService = accessor.get('ICommandService')
	const getTitle = useCallback((selection: StagingSelectionItem): string => {
		return selection.title.length > 20 ? selection.title.substring(0, 17) + '...' : selection.title;
	},[])

	const allSelections = [...selections]
	if (allSelections.length === 0) {
		return null
	}

	return (
		<div className={`flex items-center flex-wrap text-left relative ${className}`}>

			{allSelections.map((selection, i) => {
				const isThisSelectionAFile = selection.selectionStr === null
				const isThisSelectionProspective = i > selections.length - 1
				const isHovered = hoveredIndices[i] || false

				const thisKey = `${isThisSelectionProspective}-${i}-${selections.length}`
				const selectionHTML = (<div key={thisKey} // container for `selectionSummary` and `selectionText`
				>
					<div
						className='flex items-center gap-1 mr-1'
					>
						<div // styled summary box
							className={`flex items-center relative
									w-fit h-fit
									select-none
									${type === 'staging' ? 'cursor-pointer bg-codeseek-bg-1 hover:bg-codeseek-bg-4' : 'bg-codeseek-bg-1'}
									text-codeseek-fg-1 text-xs text-nowrap
									border rounded-sm ${isClearHovered && !isThisSelectionProspective ? 'border-codeseek-border-1' : 'border-codeseek-border-2'} hover:border-codeseek-border-1
									${i === curShowDetailSelIdx ? 'show-detail-sel': ''}
									transition-all duration-150`}
							onClick={() => {
								if (isThisSelectionProspective) { // add prospective selection to selections
									if (type !== 'staging') return; // (never)
									setSelections([...selections, selection])

								} else if (isThisSelectionAFile) { // open files
									commandService.executeCommand('vscode.open', selection.fileURI, {
										preview: true,
									});
								} else if (selection.type !== 'Terminal') { // show text for non-Terminal types
									if(curShowDetailSelIdx === i) {
										setShowDetailSelIdx?.(undefined)
										setCurOpenedSelIdx(undefined)
									} else {
										setShowDetailSelIdx?.(i)
										setCurOpenedSelIdx(i)
									}
								}
							}}
							onMouseEnter={() => {
								setHoveredIndices(prev => ({ ...prev, [i]: true }))
							}}
							onMouseLeave={() => {
								setHoveredIndices(prev => ({ ...prev, [i]: false }))
							}}
						>
							{type === 'staging' && !isThisSelectionProspective && (
								<div className='flex items-center justify-center select-file-icon-container'>
									{isHovered ? (
										<ButtonClose
											className='cursor-pointer z-1 flex items-center justify-center select-file-close-icon'
											onClick={(e) => {
												e.stopPropagation();
												setSelections([...selections.slice(0, i), ...selections.slice(i + 1)])
												setHoveredIndices(prev => {
													const newHovered = { ...prev }
													delete newHovered[i]
													return newHovered
												})
												if(curShowDetailSelIdx === i) {
													setShowDetailSelIdx?.(undefined)
													setCurOpenedSelIdx(undefined)
												}
											}}
										/>
									) : (
										selection.type === 'Codebase' ? (
											<span className='codicon codicon-layers flex items-center justify-center select-file-icon' />
										) : selection.type === 'Folder' ? (
											<span className='codicon codicon-folder flex items-center justify-center select-file-icon' />
										) : selection.type === 'Terminal' ? (
											<span className='codicon codicon-terminal flex items-center justify-center select-file-icon' />
										) : (selection.type === 'Url' || selection.type === 'ICenter') ? (
											<span className='codicon codicon-link flex items-center justify-center select-file-icon' />
										) : (
											<VSCodeFileIcon uri={selection.fileURI!} fileKind={FileKind.FILE} />
										)
									)}
								</div>
							)}
							{type === 'past' && (
								selection.type === 'Codebase' ? (
									<span className='codicon codicon-layers flex items-center justify-center select-file-icon' />
								) : selection.type === 'Folder' ? (
									<span className='codicon codicon-folder flex items-center justify-center select-file-icon' />
								) : selection.type === 'Terminal' ? (
									<span className='codicon codicon-terminal flex items-center justify-center select-file-icon' />
								) : (selection.type === 'Url' || selection.type === 'ICenter') ? (
									<span className='codicon codicon-link flex items-center justify-center select-file-icon' />
								) : (
									<VSCodeFileIcon uri={selection.fileURI!} fileKind={FileKind.FILE} className='mx-[1px]' />
								)
							)}
							<span className='mr-1 pb-[1px]'
								title={selection.type === 'Terminal'
									? (selection as TerminalSelection).content
									: undefined}
							>
								{/* file name */}
								{getTitle(selection)}
								{/* selection range */}
								{!isThisSelectionAFile && selection.range ? ` (${selection.range.startLineNumber}-${selection.range.endLineNumber})` : ''}
							</span>
						</div>

						{/* clear all selections button */}
						{/* {type !== 'staging' || selections.length === 0 || i !== selections.length - 1
							? null
							: <div className={`flex items-center ${isThisSelectionOpened ? 'w-full' : ''}`}>
								<div
									className='rounded-md'
									onMouseEnter={() => setIsClearHovered(true)}
									onMouseLeave={() => setIsClearHovered(false)}
								>
									<Delete
										size={16}
										className={`stroke-[1]
												stroke-codeseek-fg-1
												fill-codeseek-bg-3
												opacity-40
												hover:opacity-60
												transition-all duration-150
												cursor-pointer
											`}
										onClick={() => { setSelections([]) }}
									/>
								</div>
							</div>
						} */}
					</div>
				</div>)

				return <Fragment key={thisKey}>
					{/* divider between `selections` and `prospectiveSelections` */}
					{/* {selections.length > 0 && i === selections.length && <div className='w-full'></div>} */}
					{selectionHTML}
				</Fragment>

			})}


		</div>

	)
}


type ToolReusltToComponent = { [T in ToolName]: (props: { message: ToolMessage<T> }, messageIdx: number,  isExecuting: boolean) => React.ReactNode }
interface ToolResultProps {
	actionTitle: string;
	actionParam?: string;
	actionNumResults?: number;
	children?: React.ReactNode;
	showParamHtml?: React.ReactElement;
	isExecuting: boolean;
}

const ToolResult = ({
	actionTitle,
	actionParam,
	actionNumResults,
	children,
	showParamHtml,
	isExecuting,
}: ToolResultProps) => {
	const [isExpanded, setIsExpanded] = useState(false);

	const isDropdown = !!children

	return (
		<div className="mx-2 select-none mt-0.5">
			<div className="border border-codeseek-border-3 rounded px-1 py-1 bg-codeseek-bg-tool">
				<div
					className={`flex items-center min-h-[20px] ${isDropdown ? 'cursor-pointer hover:brightness-125 transition-all duration-150' : 'mx-1'}`}
					onClick={() => children && setIsExpanded(!isExpanded)}
				>
					{isDropdown && (
						<ChevronRight
							className={`text-codeseek-fg-3 mr-0.5 h-5 w-5 flex-shrink-0 transition-transform duration-100 ease-[cubic-bezier(0.4,0,0.2,1)] ${isExpanded ? 'rotate-90' : ''}`}
						/>
					)}
					<div className="flex items-center flex-wrap gap-x-2 gap-y-0.5 relative flex-grow">
						<span className="text-codeseek-fg-3">{actionTitle}</span>
						{showParamHtml ? (
							showParamHtml
						) : (
							<span className="text-codeseek-fg-4 text-xs italic">{`"`}{actionParam}{`"`}</span>
						)}
						{actionNumResults !== undefined && (
							<span className="pt-[1px] text-codeseek-fg-4 text-xs">
								{`(`}{actionNumResults}{` result`}{actionNumResults !== 1 ? 's' : ''}{`)`}
							</span>
						)}
						{isExecuting && (
							<span className="ml-auto mr-2 mt-0.5 animate-spin text-codeseek-fg-3">
								<span className="codicon codicon-loading" style={{ fontSize: '9px' }}></span>
							</span>
						)}
					</div>
				</div>
				<div
					className={`overflow-hidden transition-all duration-200 ease-in-out ${isExpanded ? 'max-h-[500px] opacity-100 overflow-y-auto' : 'max-h-0 opacity-0'}`}
				>
					{children}
				</div>
			</div>
		</div>
	);
};



const toolResultToComponent: ToolReusltToComponent = {
	[ToolNameEnum.READ_FILE]: ({ message }, messageIdx: number, isExecuting: boolean) => {
		const params = message.params as ToolCallParamsType[ToolNameEnum.READ_FILE]
		const showParamHtml = (
			<div className="flex items-center gap-x-1 text-codeseek-fg-4 text-xs">
				<VSCodeFileIcon uri={URI.file(params.path)} fileKind={FileKind.FILE} />
				<span className="text-codeseek-fg-4 text-xs italic">{getBasename(params.path)}:{message.result?.startLine}-{message.result?.endLine}</span>
			</div>
		)
		return (
			<ToolResult
				actionTitle="Read file"
				showParamHtml={showParamHtml}
				isExecuting={isExecuting}
			/>
		)
	},
	[ToolNameEnum.LIST_FILES]: ({ message }, messageIdx: number, isExecuting: boolean) => {
		const params = message.params as ToolCallParamsType[ToolNameEnum.LIST_FILES]
		return <ToolResult
			actionTitle="Inspected folder"
			actionParam={`${getBasename(params.path)}/`}
			actionNumResults={message.result?.children?.length}
			isExecuting={isExecuting}
		>
			<div className="text-codeseek-fg-2">
				{message.result?.children?.map((item, i) => (
					<div key={i} className="pl-2 py-0.5 mb-1 bg-codeseek-bg-1 rounded">
						{item.name}
						{item.isDirectory && '/'}
					</div>
				))}
				{message.result?.hasNextPage && (
					<div className="pl-2 text-codeseek-fg-3 italic">
						{message.result.itemsRemaining} more items...
					</div>
				)}
			</div>
		</ToolResult>
	},
	[ToolNameEnum.PATHNAME_SEARCH]: ({ message }, messageIdx: number, isExecuting: boolean) => {
		const params = message.params as ToolCallParamsType[ToolNameEnum.PATHNAME_SEARCH]
		return <ToolResult
			actionTitle="Searched filename"
			actionParam={params.query}
			actionNumResults={Array.isArray(message.result?.uris) ? message.result.uris.length : 0}
			isExecuting={isExecuting}
		>
			<div className="text-codeseek-fg-2">
				{Array.isArray(message.result?.uris) ?
					message.result?.uris.map((uri, i) => (
						<div key={i} className="pl-2 py-0.5 mb-1 bg-codeseek-bg-1 rounded">
							<a
								href={uri.toString()}
								className="text-codeseek-accent hover:underline"
							>
								{uri.fsPath.split('/').pop()}
							</a>
						</div>
					)) :
					<div className="pl-2">{message.result?.uris}</div>
				}
				{message.result?.hasNextPage && (
					<div className="pl-2 text-codeseek-fg-3 italic">
						More results available...
					</div>
				)}
			</div>
		</ToolResult>
	},
	[ToolNameEnum.SEARCH]: ({ message }, messageIdx: number, isExecuting: boolean) => {
		const params = message.params as ToolCallParamsType[ToolNameEnum.SEARCH]
		return <ToolResult
			actionTitle="Searched"
			actionParam={params.query}
			actionNumResults={Array.isArray(message.result?.uris) ? message.result.uris.length : 0}
			isExecuting={isExecuting}
		>
			<div className="text-codeseek-fg-2">
				{typeof message.result?.uris === 'string' ?
					message.result?.uris :
					message.result?.uris.map((uri, i) => (
						<div key={i} className="pl-2 py-0.5 mb-1 bg-codeseek-bg-1 rounded">
							<a
								href={uri.toString()}
								className="text-codeseek-accent hover:underline"
							>
								{uri.fsPath}
							</a>
						</div>
					))
				}
				{message.result?.hasNextPage && (
					<div className="pl-2 text-codeseek-fg-3 italic">
						More results available...
					</div>
				)}
			</div>
		</ToolResult>
	},
	[ToolNameEnum.CREATE_FILE]: ({ message }, messageIdx: number, isExecuting: boolean) => {
		const params = message.params as ToolCallParamsType[ToolNameEnum.CREATE_FILE]
		return <ToolResult
			actionTitle="create_file"
			actionParam={params.path}
			actionNumResults={0}
			isExecuting={isExecuting}
		>
		</ToolResult>
	},
	[ToolNameEnum.UPDATE_TO_FILE]: ({ message }, messageIdx: number, isExecuting: boolean) => {
		const params = message.params as ToolCallParamsType[ToolNameEnum.UPDATE_TO_FILE]
		return <ToolResult
			actionTitle="update_to_file"
			actionParam={params.content}
			actionNumResults={0}
			isExecuting={isExecuting}
		>
			<div className="text-codeseek-fg-2">
				<div className="pl-2 py-0.5 mb-1 bg-codeseek-bg-1 rounded">
					<a
						href={message.result?.uri.toString()}
						className="text-codeseek-accent hover:underline"
					>
						{message.result?.uri.fsPath}
					</a>
				</div>
			</div>
		</ToolResult>
	},
	[ToolNameEnum.APPROVE_REQUEST]: ({ message }) => {
		return null
	},
	[ToolNameEnum.ASK_FOLLOWUP_QUESTION]: ({ message }) => {
		return null
	},
	[ToolNameEnum.CTAGS_QUERY]: ({ message }, messageIdx: number, isExecuting: boolean) => {
		const params = message.params as ToolCallParamsType[ToolNameEnum.CTAGS_QUERY]
		const accessor = useAccessor()
		const commandService = accessor.get('ICommandService')

		return <ToolResult
			actionTitle="使用 ctags 工具查询符号定义"
			actionParam={params.symbol}
			actionNumResults={Array.isArray(message.result) ? message.result.length : 0}
			isExecuting={isExecuting}
		>
			<div className="text-codeseek-fg-2">
				{Array.isArray(message.result) && message.result.length > 0 ? (
					message.result.map((symbolDefinition, i) => (
						<div key={i} className="pl-2 py-1 mb-1 bg-codeseek-bg-1 rounded">
							<div className="flex items-center gap-1">
								{symbolDefinition.path && (
									<VSCodeFileIcon uri={URI.file(symbolDefinition.path)} fileKind={FileKind.FILE} />
								)}
								<a
									href="#"
									className="text-codeseek-accent hover:underline"
									onClick={(e) => {
										e.preventDefault();
										if (symbolDefinition.path) {
											const uri = URI.file(symbolDefinition.path);
											const line = symbolDefinition.line || (symbolDefinition.positions && symbolDefinition.positions[1]) || 1;
											commandService.executeCommand('vscode.open', uri, {
												selection: { startLineNumber: line, startColumn: 1, endLineNumber: line, endColumn: 1 },
												preserveFocus: false, // 确保编辑器获得焦点
												preview: true,
											}).then(() => {
												// 使用组合命令确保视图滚动和光标定位
												commandService.executeCommand('revealLine', {
													lineNumber: line,
													at: 'center' // 将目标行居中显示
												}).then(() => {
													// 确保光标定位并可见
													commandService.executeCommand('editor.action.goToLocations',
														uri,
														line,
														1,
														[],
														'goto'
													);
												});
											});
										}
									}}
								>
									{symbolDefinition.name}
								</a>
								<span className="text-codeseek-fg-4 text-xs">
									{symbolDefinition.kind && `(${symbolDefinition.kind})`}
								</span>
							</div>
							{symbolDefinition.path && (
								<div
									className="pl-4 text-xs text-codeseek-fg-3"
									title={symbolDefinition.path}
								>
									{getBasename(symbolDefinition.path)}
									{symbolDefinition.line && `:${symbolDefinition.line}`}
								</div>
							)}
						</div>
					))
				) : (
					<div className="pl-2 py-1 text-codeseek-fg-3 italic">
						没有找到相关符号
					</div>
				)}
			</div>
		</ToolResult>
	},
	[ToolNameEnum.CLANGD_QUERY]: ({ message }, messageIdx: number, isExecuting: boolean) => {
		const params = message.params as ToolCallParamsType[ToolNameEnum.CLANGD_QUERY]
		const accessor = useAccessor()
		const commandService = accessor.get('ICommandService')

		return <ToolResult
			actionTitle="使用 clangd 工具查询符号定义"
			actionParam={`${params.filePath}:${params.line}:${params.character}`}
			actionNumResults={Array.isArray(message.result) ? message.result.length : 0}
			isExecuting={isExecuting}
		>
			<div className="text-codeseek-fg-2">
				<div className="max-h-[300px] overflow-y-auto pr-1" style={{ scrollbarWidth: 'thin' }}>
					{Array.isArray(message.result) && message.result.length > 0 ? (
						message.result.map((reference, i) => (
							<div key={i} className="pl-2 py-1 mb-1 bg-codeseek-bg-1 rounded">
								<div className="flex items-center gap-1">
									{reference.uri && (
										<VSCodeFileIcon uri={URI.parse(reference.uri)} fileKind={FileKind.FILE} />
									)}
									<a
										href="#"
										className="text-codeseek-accent hover:underline"
										onClick={(e) => {
											e.preventDefault();
											if (reference.uri) {
												const uri = URI.parse(reference.uri);
												const line = reference.range.start.line + 1 || 1;
												const character = reference.range.start.character + 1 || 1;

												commandService.executeCommand('vscode.open', uri, {
													selection: {
														startLineNumber: line,
														startColumn: character,
														endLineNumber: line,
														endColumn: character
													},
													preserveFocus: false,
													preview: true,
												}).then(() => {
													// 确保视图滚动和光标定位
													commandService.executeCommand('revealLine', {
														lineNumber: line,
														at: 'center' // 将目标行居中显示
													});
												});
											}
										}}
									>
										{getBasename(URI.parse(reference.uri).fsPath)}:{reference.range.start.line + 1}:{reference.range.start.character + 1}
									</a>
								</div>
								{reference.uri && (
									<div className="pl-4 text-xs text-codeseek-fg-3 truncate" title={reference.uri}>
										{URI.parse(reference.uri).fsPath}
									</div>
								)}
							</div>
						))
					) : (
						<div className="pl-2 py-1 text-codeseek-fg-3 italic">
							没有找到相关符号
						</div>
					)}
				</div>
			</div>
		</ToolResult>
	},
	[ToolNameEnum.SHOW_SUMMARY]: ({ message }, messageIdx: number, isExecuting: boolean) => {
		const params = message.params as ToolCallParamsType[ToolNameEnum.SHOW_SUMMARY]
		const accessor = useAccessor()
		const chatThreadsService = accessor.get('IChatThreadService')

		const toolMarkdownIdOffset = 10_000;

		const chatMessageLocation: ChatMessageLocation = {
			containerId: message.containerId,
			threadId: message.threadId,
			messageIdx: messageIdx! + toolMarkdownIdOffset,
		}

		return (
			<ToolResult
				actionTitle={params.summary}
				isExecuting={false}
			>
				<ChatMarkdownRender string={params.detail ?? ''} chatMessageLocation={chatMessageLocation} />
			</ToolResult>
		);
	},
}



type ChatBubbleMode = 'display' | 'edit'
const ChatBubble = memo(
	({ chatMessage, messageIdx, isStreaming, isCloseEdit, setIsCloseEdit }: { chatMessage: ChatMessage, messageIdx?: number, isStreaming: boolean, isCloseEdit?: boolean, setIsCloseEdit?: (isCloseEdit: boolean) => void }) => {

		const role = chatMessage.role

		const accessor = useAccessor()
		const settingsState = useSettingsState()
		const chatThreadsService = accessor.get('IChatThreadService')
		const containerId = chatThreadsService.getCurrentContainerId()

		// global state
		let isBeingEdited = false
		let stagingSelections: StagingSelectionItem[] = []
		let setIsBeingEdited = (_: boolean) => { }
		let setStagingSelections = (_: StagingSelectionItem[]) => { }

		if (messageIdx !== undefined) {
			const _state = chatThreadsService.getCurrentMessageState(containerId, messageIdx)
			isBeingEdited = _state.isBeingEdited
			stagingSelections = _state.stagingSelections
			setIsBeingEdited = (v) => chatThreadsService.setCurrentMessageState(containerId, messageIdx, { isBeingEdited: v })
			setStagingSelections = (s) => chatThreadsService.setCurrentMessageState(containerId, messageIdx, { stagingSelections: s })
		}


		// local state
		const mode: ChatBubbleMode = isBeingEdited ? 'edit' : 'display'
		const [isFocused, setIsFocused] = useState(false)
		const [isHovered, setIsHovered] = useState(false)
		const [isDisabled, setIsDisabled] = useState(false)
		const [textAreaRefState, setTextAreaRef] = useState<HTMLTextAreaElement | null>(null)
		const textAreaFnsRef = useRef<TextAreaFns | null>(null)
		// initialize on first render, and when edit was just enabled
		const _mustInitialize = useRef(true)
		const _justEnabledEdit = useRef(false)
		useEffect(() => {
			const canInitialize = role === 'user' && mode === 'edit' && textAreaRefState
			const shouldInitialize = _justEnabledEdit.current || _mustInitialize.current
			if (canInitialize && shouldInitialize) {
				setStagingSelections(chatMessage.state.stagingSelections || [])

				if (textAreaFnsRef.current)
					textAreaFnsRef.current.setValue(chatMessage.displayContent || '')

				textAreaRefState.focus();

				_justEnabledEdit.current = false
				_mustInitialize.current = false
			}

		}, [chatMessage, role, mode, _justEnabledEdit, textAreaRefState, textAreaFnsRef.current, _justEnabledEdit.current, _mustInitialize.current])
		const EditSymbol = mode === 'display' ? Pencil : X
		const onOpenEdit = () => {
			setIsBeingEdited(true)
			chatThreadsService.setFocusedMessageIdx(containerId, messageIdx)
			_justEnabledEdit.current = true
		}
		const onCloseEdit = () => {
			setIsFocused(false)
			setIsHovered(false)
			setIsBeingEdited(false)
			chatThreadsService.setFocusedMessageIdx(containerId, undefined)
		}

		useEffect(() => {
			if (role === 'user' && mode === 'edit' && isCloseEdit) {
				onCloseEdit()
				if (setIsCloseEdit) {
					setIsCloseEdit(false);
				}
			}
		}, [isCloseEdit, setIsCloseEdit, role, mode, onCloseEdit])

		// set chat bubble contents
		let chatbubbleContents: React.ReactNode
		if (role === 'user') {
			if (mode === 'display') {
				chatbubbleContents = <>
					<SelectedFiles type='past' selections={chatMessage.state.stagingSelections || []} className='mb-[2px]' />
					<div className='py-[2px]' dangerouslySetInnerHTML={{ __html: highLightMentionsFormater(chatMessage.displayContent || '', chatMessage.state.stagingSelections, true) }} />
				</>
			}
			else if (mode === 'edit') {
				const onSubmit = async () => {
					if (isDisabled) return;
					if (!textAreaRefState) return;
					if (messageIdx === undefined) return;

					// cancel any streams on this thread
					const threadId = chatThreadsService.getCurrentThreadId(containerId)
					chatThreadsService.cancelStreaming(containerId, threadId)

					// reset state
					setIsBeingEdited(false)
					chatThreadsService.setFocusedMessageIdx(containerId, undefined)

					// stream the edit
					const userMessage = textAreaRefState.value;
					const codeseekSettingsService = accessor.get('ICodeseekSettingsService')
					const chatMode = codeseekSettingsService.getChatModeForContainer(containerId)
					await chatThreadsService.editUserMessageAndStreamResponse({containerId, userMessage, chatMode, messageIdx, })
				}

				const onAbort = () => {
					const threadId = chatThreadsService.getCurrentThreadId(containerId)
					chatThreadsService.cancelStreaming(containerId, threadId)
				}

				const onKeyDown = (e: KeyboardEvent<HTMLTextAreaElement>) => {
					if (e.key === 'Escape') {
						onCloseEdit()
					}
					if (e.key === 'Enter' && !e.shiftKey) {
						onSubmit()
					}
				}

				if (!chatMessage.content) { // don't show if empty and not loading (if loading, want to show)
					return null
				}

				chatbubbleContents = <>
					<CodeseekChatArea
						onSubmit={onSubmit}
						onAbort={onAbort}
						isStreaming={false}
						isDisabled={isDisabled}
						showSelections={true}
						featureName={FeatureNames.CtrlL}
						selections={stagingSelections}
						setSelections={setStagingSelections}
						className='pl-1.5 pr-1 pt-1.5 pb-1'
						containerId={containerId}
					>
						<CodeseekInputBox2
							ref={setTextAreaRef}
							className='min-h-[75px] max-h-[500px] p-1'
							placeholder="Edit your message..."
							selections={stagingSelections}
							onChangeText={(text) => setIsDisabled(!text)}
							onFocus={() => {
								setIsFocused(true)
								chatThreadsService.setFocusedMessageIdx(containerId, messageIdx);
							}}
							onBlur={() => {
								setIsFocused(false)
							}}
							onKeyDown={onKeyDown}
							fnsRef={textAreaFnsRef}
							multiline={true}
							setSelections={setStagingSelections}
						/>
					</CodeseekChatArea>
				</>
			}
		}
		else if (role === 'assistant') {
			const thread = chatThreadsService.getCurrentThread(containerId)

			const chatMessageLocation: ChatMessageLocation = {
				threadId: thread.id,
				messageIdx: messageIdx!,
				containerId: containerId,
			}

			chatbubbleContents = <ChatMarkdownRender string={chatMessage.displayContent ?? ''} chatMessageLocation={chatMessageLocation} />
		}
		else if (role === 'tool') {
			if (!chatMessage.name) return null;
			if (toolResultToComponent[chatMessage.name as ToolName]) {
				const ToolComponent = toolResultToComponent[chatMessage.name as ToolName] as ({ message }: { message: any }, messageIdx: number, isExecuting: boolean) => React.ReactNode // ts isnt smart enough to deal with the types here...
				chatbubbleContents = ToolComponent({ message: chatMessage }, messageIdx!, isStreaming !== false)
			} else {
				return null;
			}
		}

		return <div
			// align chatbubble accoridng to role
			className={`
				relative
				${mode === 'edit' ? 'px-2 w-full max-w-full mt-[1px]'
					: role === 'user' ? `px-2 self-start w-full max-w-full mt-[1px]`
						: role === 'assistant' ? `pl-2 pr-0.5 self-start w-full max-w-full` : ''
				}
			`}
			onMouseEnter={() => setIsHovered(true)}
			onMouseLeave={() => setIsHovered(false)}
		>
			<div
				// style chatbubble according to role
				className={`
					text-left rounded-lg
					max-w-full
					${mode === 'edit' ? ''
						: role === 'user' ? `px-2 py-1 bg-codeseek-bg-1 text-codeseek-fg-1 overflow-x-auto text-root
						whitespace-pre-wrap border border-codeseek-border-3 rounded-md`
							: role === 'assistant' ? 'px-2 overflow-x-auto text-root' : ''
					}
				`}
			>
				{chatbubbleContents}
			</div>

			{/* edit button */}
			{role === 'user' && !isStreaming && <EditSymbol
				size={18}
				className={`
					absolute -top-0 right-1
					translate-x-0 -translate-y-0
					cursor-pointer z-1
					p-[2px]
					bg-codeseek-bg-1 border border-codeseek-border-1 rounded-md
					transition-opacity duration-200 ease-in-out
					${isHovered || (isFocused && mode === 'edit') ? 'opacity-100' : 'opacity-0'}
				`}
				onClick={() => {
					if (mode === 'display') {
						onOpenEdit()
					} else if (mode === 'edit') {
						onCloseEdit()
					}
				}}
			/>}
		</div>
	},
	(prevProps, nextProps) => {
		// Only re-render if relevant props change
		return (
			prevProps.chatMessage === nextProps.chatMessage &&
			prevProps.messageIdx === nextProps.messageIdx &&
			prevProps.isStreaming === nextProps.isStreaming &&
			prevProps.isCloseEdit === nextProps.isCloseEdit
		);
	}
);


export const SidebarChat = () => {

	const textAreaRef = useRef<HTMLTextAreaElement | null>(null)
	const textAreaFnsRef = useRef<TextAreaFns | null>(null)

	const accessor = useAccessor()
	const chatThreadsService = accessor.get('IChatThreadService')

	const settingsState = useSettingsState()

	// container state
	const chatContainerState = useChatContainerState()
	const containerId = chatContainerState.currentContainerId

	// ----- HIGHER STATE -----
	// sidebar state
	const sidebarStateService = accessor.get('ISidebarStateService')
	useEffect(() => {
		const disposables: IDisposable[] = []
		disposables.push(
			sidebarStateService.onDidFocusChat(() => {
				!chatThreadsService.isFocusingMessage(containerId) && textAreaRef.current?.focus()
			}),
			sidebarStateService.onDidBlurChat(() => { !chatThreadsService.isFocusingMessage(containerId) && textAreaRef.current?.blur() }),
			sidebarStateService.onDidOpenNewChat(() => { !chatThreadsService.isFocusingMessage(containerId) && textAreaRef.current?.value && (textAreaRef.current.value = '') && textAreaRef.current?.focus() }),
		)
		return () => disposables.forEach(d => d.dispose())
	}, [sidebarStateService, textAreaRef, containerId])

	const [showScrollToBottom, setShowScrollToBottom] = useState(false)
	const [isShowContextMenu, setIsShowContextMenu] = useState(false)
	const [isCloseEdit, setIsCloseEdit] = useState(false)
	const { isHistoryOpen } = useSidebarState()

	// threads state
	const chatThreadsState = useChatThreadsState()
	const currentThread = chatThreadsService.getCurrentThread(containerId)
	const previousMessages = chatThreadsService.getCurrentThreadMessages(containerId)
	const askMessage = currentThread.state.askMessage

	const selections = chatThreadsService.getCurrentThreadStagingSelections(containerId)
	const setSelections = useCallback((s: StagingSelectionItem[]) => {
		chatThreadsService.setCurrentThreadStagingSelections(containerId, s);
	}, [chatThreadsService, containerId]);

	// stream state
	const currThreadStreamState = useChatThreadsStreamState(containerId, chatThreadsState.currentThreadId)
	const isStreaming = !!currThreadStreamState?.isStreaming
	const latestError = currThreadStreamState?.error
	const messageSoFar = currThreadStreamState?.messageSoFar
	const toolCall = currThreadStreamState?.toolCall

	// ----- SIDEBAR CHAT state (local) -----

	// state of current message
	const initVal = ''
	const [instructionsAreEmpty, setInstructionsAreEmpty] = useState(!initVal)

	const isDisabled = instructionsAreEmpty || !!isFeatureNameDisabled(FeatureNames.CtrlL, settingsState)

	const [sidebarRef, sidebarDimensions] = useResizeObserver()
	// const [chatAreaRef, chatAreaDimensions] = useResizeObserver()
	const [inputFormRef, inputFormDimensions] = useResizeObserver()
	const [historyRef, historyDimensions] = useResizeObserver()

	const scrollToBottom = () => {
		if (!disableAutoScrollRef.current && scrollContainerRef.current) {
			scrollContainerRef.current.scrollTo({
				top: scrollContainerRef.current.scrollHeight,
				behavior: isStreaming ? 'auto' : 'smooth'
			})
			disableAutoScrollRef.current = false;
		}
	}

	useEffect(() => {
		if (isStreaming && messageSoFar) {
			// 当正在流式传输且有内容更新时，只有在用户未手动滚动时才滚动到底部
			const div = scrollContainerRef.current;
			if (div) {
				const isUserAtBottom = Math.abs(div.scrollHeight - div.clientHeight - div.scrollTop) < BOTTOM_THRESHOLD;
				if (isUserAtBottom) {
					scrollToBottom();
				} else {
					// 用户已经滚动到其他位置，暂时禁用自动滚动
					disableAutoScrollRef.current = true;
				}
			}
		}
	}, [isStreaming, previousMessages.length, toolCall, messageSoFar])

	useScrollbarStyles(sidebarRef)
	useEffect(() => {
		if (settingsState.chatMode === ChatMode.Agent) {
			setTextAreaEnabled(false)
		} else {
			setTextAreaEnabled(true)
		}
	}, [settingsState.chatMode])

	const onSubmit = useCallback(async () => {

		if (isDisabled) return
		if (isStreaming) return

		// 获取容器特定的聊天模式
		const codeseekSettingsService = accessor.get('ICodeseekSettingsService')
		const chatMode = codeseekSettingsService.getChatModeForContainer(containerId)

		// send message to LLM
		const userMessage = textAreaRef.current?.value ?? ''
		chatThreadsService.addUserMessageAndStreamResponse({ containerId, userMessageOpts: { from: 'Chat', userMessage, chatMode } })

		textAreaFnsRef.current?.setValue('')
		textAreaRef.current?.focus() // focus input after submit

		setPrimaryButtonText(undefined)
		setSecondaryButtonText(undefined)

		setTimeout(() => {
			scrollToBottom()
		}, 100)
	}, [chatThreadsService, isDisabled, isStreaming, textAreaRef, textAreaFnsRef, containerId, accessor])

	const onSelectContextMenu = useCallback((option: ContextMenuQueryItem) => {
		chatThreadsService.setCurrentThreadStateSelectionsChangeSelections(containerId)
		if (option.type === ContextMenuOptionType.File) {
			setIsShowContextMenu(false)
		}
	}, [setSelections, setIsShowContextMenu])

	const onShowContextMenu = useCallback(() => {
		setIsShowContextMenu(!isShowContextMenu)
	}, [setIsShowContextMenu, isShowContextMenu])

	const onAbort = () => {
		const threadId = currentThread.id
		chatThreadsService.cancelStreaming(containerId, threadId)
	}

	// const [_test_messages, _set_test_messages] = useState<string[]>([])

	const keybindingString = accessor.get('IKeybindingService').lookupKeybinding(CODESEEK_CTRL_L_ACTION_ID)?.getLabel()

	// scroll to top on thread switch
	const scrollContainerRef = useRef<HTMLDivElement | null>(null)
	useEffect(() => {
		if (scrollContainerRef.current) {
			if (isHistoryOpen) {
				scrollContainerRef.current.scrollTo({ top: 0, left: 0, behavior: 'auto' });
			} else {
				scrollToBottom();
			}
			setShowScrollToBottom(false);
		}
	}, [isHistoryOpen, currentThread.id]);

	useEffect(() => {
		setShowScrollToBottom(false);
	}, [currentThread.id]);

	const prevMessagesHTML = useMemo(() => {
		return previousMessages.slice(0, -1).map((message, i) =>
			<ChatBubble key={i} chatMessage={message} messageIdx={i} isStreaming={isStreaming} isCloseEdit={isCloseEdit} setIsCloseEdit={setIsCloseEdit} />
		)
	}, [previousMessages, isCloseEdit, isStreaming])

	const lastMessagesHTML = useMemo(() => {
		const lastIndex = previousMessages.length - 1
		return previousMessages.slice(lastIndex).map((message) =>
			<ChatBubble key={lastIndex} chatMessage={message} messageIdx={lastIndex} isStreaming={isStreaming} isCloseEdit={isCloseEdit} setIsCloseEdit={setIsCloseEdit} />
		)
	}, [previousMessages, isCloseEdit, isStreaming])


	const threadSelector = <div ref={historyRef}
		className={`h-auto ${isHistoryOpen ? '' : 'hidden'} z-10 border border-codeseek-border-3 rounded-md mx-2 mt-1.5`}
	>
		<SidebarThreadSelector />
	</div>
	// we need to hold on to the ask because useEffect > lastMessage will always let us know when an ask comes in and handle it, but by the time handleMessage is called, the last message might not be the ask anymore (it could be a say that followed)
	const [codeSeekAsk, setCodeSeekAsk] = useState<CodeSeekAsk | undefined>(undefined)
	const [textAreaEnabled, setTextAreaEnabled] = useState(true)
	const [primaryButtonText, setPrimaryButtonText] = useState<string | undefined>('Approve')
	const disableAutoScrollRef = useRef(false)
	const [isShowButtons, setIsShowButtons] = useState<boolean>(false)
	const [command, setCommand] = useState<string | undefined>(undefined)
	const [secondaryButtonText, setSecondaryButtonText] = useState<string | undefined>('Reject')
	const [didClickCancel, setDidClickCancel] = useState(false)
	const [inputValue, setInputValue] = useState('')


	useEffect(() => {
		// if last message is an ask, show user ask UI
		// if user finished a task, then start a new task with a new conversation history since in this moment that the extension is waiting for user response, the user could close the extension and the conversation history would be lost.
		// basically as long as a task is active, the conversation history will be persisted
		// const askMessage = currentThread.state.askMessage
		if (askMessage && askMessage.type === 'tool' && askMessage.content) {
			switch (askMessage.content.name) {
				case "approve_request":
					// extension waiting for feedback. but we can just present a new task button
					// playSound("celebration")
					setTextAreaEnabled(false)
					setCodeSeekAsk("completion_result")
					setPrimaryButtonText("Start New Task")
					setIsShowButtons(true)
					setSecondaryButtonText(undefined)
					break
				default:
					setTextAreaEnabled(true)
					setCodeSeekAsk(askMessage.type)
					setCommand(askMessage.content.command ?? undefined)
					setIsShowButtons(true)
					setPrimaryButtonText("Approve")
					setSecondaryButtonText("Reject")
			}
		} else {
			// this would get called after sending the first message, so we have to watch messages.length instead
			// No messages, so user has to submit a task
			setTextAreaEnabled(true)
			setCodeSeekAsk(undefined)
			setIsShowButtons(false)
			setCommand(undefined)
			setPrimaryButtonText(undefined)
			setSecondaryButtonText(undefined)
		}
	}, [askMessage])

	const handleApproveButtonClick = useCallback(
		(text?: string) => {
			const trimmedInput = text?.trim()
			let message;
			if (trimmedInput) {
				message = {
					type: "tool",
					response: AskReponseType.yesButtonClicked,
					text: trimmedInput,
				}
			} else {
				message = {
					type: "tool",
					response: AskReponseType.yesButtonClicked,
				}
			}
			// Clear input state after sending
			setInputValue('')
			if (message) {
				chatThreadsService.setAskResponse(containerId, message)
			}
			setPrimaryButtonText(undefined)
			setSecondaryButtonText(undefined)
			setTextAreaEnabled(true)
			setCodeSeekAsk(undefined)
			setCommand(undefined)
			setIsShowButtons(false)
			disableAutoScrollRef.current = false
		},
		[containerId],
	)


	useEffect(() => {
		if (primaryButtonText && settingsState.globalSettings.autoApprove) {
			handleApproveButtonClick(inputValue)
		}
	}, [primaryButtonText])


	const handleRejectButtonClick = useCallback(
		(text?: string) => {
			const trimmedInput = text?.trim()
			// Only send text/images if they exist
			if (trimmedInput) {
				chatThreadsService.setAskResponse(containerId, {
					type: "tool",
					response: AskReponseType.noButtonClicked,
					text: trimmedInput,
				})
			} else {
				// responds to the API with a "This operation failed" and lets it try again
				chatThreadsService.setAskResponse(containerId, {
					type: "tool",
					response: AskReponseType.noButtonClicked,
				})
			}
			// Clear input state after sending
			setInputValue("")
			setTextAreaEnabled(true)
			setCodeSeekAsk(undefined)
			setCommand(undefined)
			setIsShowButtons(false)
			setPrimaryButtonText(undefined)
			setSecondaryButtonText(undefined)
			disableAutoScrollRef.current = false
		},
		[containerId],
	)

	const commandAskHTML = <>
		{(isShowButtons || command) && (
			<div className='flex items-center justify-center border border-codeseek-border-1 rounded-[2px] px-2 py-[4px]'>
				{command && <div className="flex items-center justify-start w-full text-md">
					{command}
				</div>}
				<div className="flex items-center justify-end gap-1">
					{isShowButtons && primaryButtonText && (
						<CodeseekButton2
							className='text-sm px-1.5 pt-[0.5px] pb-[2px]'
							onClick={() => handleApproveButtonClick(inputValue)}>
							{primaryButtonText}
						</CodeseekButton2>
					)}
					{isShowButtons && secondaryButtonText && (
						<CodeseekButton2
							className='text-sm px-1.5 pt-[0.5px] pb-[2px]'
							onClick={(e) => handleRejectButtonClick(inputValue)}>
							{secondaryButtonText}
						</CodeseekButton2>
					)}
				</div>
			</div>
		)}
	</>

	const messagesHTML = <ScrollToBottomContainer
		key={currentThread.id} // force rerender on all children if id changes
		scrollContainerRef={scrollContainerRef}
		disableAutoScrollRef={disableAutoScrollRef}
		className={`w-full flex flex-col overflow-x-hidden overflow-y-auto pb-4
		${(prevMessagesHTML.length === 0 && lastMessagesHTML.length === 0) && !messageSoFar ? 'hidden' : ''}
	`}
		style={{ height: sidebarDimensions.height - historyDimensions.height - inputFormDimensions.height }} // the height of the previousMessages is determined by all other heights
		onScrollToBottom={(e) => {
			setShowScrollToBottom(!e)
		}
		}
	>
		{/* previous messages */}
		<div>
			{prevMessagesHTML}
		</div>

		<div
			style={prevMessagesHTML.length !== 0 && scrollContainerRef.current &&
				scrollContainerRef.current.scrollHeight > scrollContainerRef.current.clientHeight ? {
				minHeight: (sidebarDimensions.height - historyDimensions.height - inputFormDimensions.height) * 0.8,
			} : {}}
		>
			{/* user message */}
			{lastMessagesHTML}

			{/* message stream */}
			{messageSoFar && <ChatBubble chatMessage={{ role: 'assistant', content: messageSoFar, displayContent: messageSoFar }} isStreaming={isStreaming} isCloseEdit={isCloseEdit} setIsCloseEdit={setIsCloseEdit} />}

			{/* tool stream */}
			{toolCall && toolCall.name && <ChatBubble chatMessage={{ role: 'tool', content: toolCall.content, id: toolCall.id, name: toolCall.name as ToolName, params: toolCall.params, result: toolCall.result, containerId: containerId, threadId: currentThread.id }} isStreaming={isStreaming} isCloseEdit={isCloseEdit} setIsCloseEdit={setIsCloseEdit} />}

			{isStreaming && <IconLoading className='opacity-50 text-sm px-2 ml-2' />}

			{/* error message */}
			{latestError === undefined ? null :
				<div className='px-2 my-1'>
					<ErrorDisplay
						message={latestError.message}
						fullError={latestError.fullError}
						onDismiss={() => { chatThreadsService.dismissStreamError(containerId, currentThread.id) }}
						showDismiss={true}
					/>

					{/* <WarningBox className='text-sm my-2 mx-4' onClick={() => { commandService.executeCommand(CODESEEK_OPEN_SETTINGS_ACTION_ID) }} text='Open settings' /> */}
				</div>
			}
			<div className='reserve-seat-space'>
				<div style={{ opacity: 0.5 }}> </div>
			</div>
		</div>
	</ScrollToBottomContainer>

	const onChangeText = useCallback((newStr: string) => {
		setInstructionsAreEmpty(!newStr)
		setInputValue(inputValue)
	}, [setInstructionsAreEmpty])
	const onKeyDown = useCallback((e: KeyboardEvent<HTMLTextAreaElement>) => {
		if (e.key === 'Enter' && !e.shiftKey) {
			onSubmit()
		}
	}, [onSubmit])
	const onFocus = useCallback(() => {
		chatThreadsService.setFocusedMessageIdx(containerId, undefined);
		setIsCloseEdit(true);
	}, [chatThreadsService, containerId, setIsCloseEdit])
	const inputForm = <div ref={inputFormRef} className={`right-0 left-0 mx-2 mb-2 z-[1999] transform ${previousMessages.length > 0 ? 'absolute bottom-0' : ''}`}>
		{showScrollToBottom && !isStreaming && (
			<div className="scroll-to-bottom-button">
				<ScrollToBottomButton
					onClick={() => {
						scrollContainerRef.current?.scrollTo({ top: scrollContainerRef.current.scrollHeight, left: 0 })
						disableAutoScrollRef.current = false
					}}
				>
					<span className="codicon codicon-fold-down opacity-50 hover:opacity-100" style={{ fontSize: "12px" }}></span>
				</ScrollToBottomButton>
			</div>
		)}
		{commandAskHTML}
		<CodeseekChatArea
			// divRef={chatAreaRef}
			onSubmit={onSubmit}
			onAbort={onAbort}
			onShowContextMenu={onShowContextMenu}
			isStreaming={isStreaming}
			isDisabled={isDisabled}
			showSelections={true}
			selections={selections}
			setSelections={setSelections}
			onClickAnywhere={() => { textAreaRef.current?.focus() }}
			featureName={FeatureNames.CtrlL}
			className='pl-1.5 pr-1 pt-1.5 pb-1'
			containerId={containerId}
		>
			<CodeseekInputBox2
				className='min-h-[75px] p-1'
				placeholder={`${keybindingString ? `${keybindingString} to select.` : ''} @ to mention(with filter).`}
				onChangeText={onChangeText}
				onKeyDown={onKeyDown}
				onFocus={onFocus}
				ref={textAreaRef}
				fnsRef={textAreaFnsRef}
				multiline={true}
				onSelectContextMenu={onSelectContextMenu}
				selections={selections}
				setSelections={setSelections}
				enabled={textAreaEnabled}
				isShowContextMenu={isShowContextMenu}
			/>
		</CodeseekChatArea>
	</div>

	return <div ref={sidebarRef} className={`w-full h-full`}>
		{threadSelector}

		{messagesHTML}

		{inputForm}

	</div>
}


interface ScrollToBottomButtonProps {
	onClick: () => void;
	children?: React.ReactNode;
}

const ScrollToBottomButton: React.FC<ScrollToBottomButtonProps> = ({ onClick, children }) => {
	return (
		<CodeseekButton
			style={{
				backgroundColor: 'color-mix(in srgb, var(--vscode-toolbar-hoverBackground) 55%, transparent)',
				borderRadius: '13px',
				overflow: 'hidden',
				cursor: 'pointer',
				display: 'flex',
				justifyContent: 'center',
				alignItems: 'center',
				height: '26px',
				width: '26px',
				margin: '5px auto'
			}}
			onClick={onClick}
			onMouseEnter={(e) => {
				e.currentTarget.style.backgroundColor = 'color-mix(in srgb, var(--vscode-toolbar-hoverBackground) 90%, transparent)';
			}}
			onMouseLeave={(e) => {
				e.currentTarget.style.backgroundColor = 'color-mix(in srgb, var(--vscode-toolbar-hoverBackground) 55%, transparent)';
			}}
			onMouseDown={(e) => {
				e.currentTarget.style.backgroundColor = 'color-mix(in srgb, var(--vscode-toolbar-hoverBackground) 70%, transparent)';
			}}
			onMouseUp={(e) => {
				e.currentTarget.style.backgroundColor = 'color-mix(in srgb, var(--vscode-toolbar-hoverBackground) 90%, transparent)';
			}}
		>
			{children || ''}
		</CodeseekButton>
	);
};

// 文件图标组件
export const VSCodeFileIcon = ({ uri, fileKind = FileKind.FILE, className }: { uri: URI, fileKind: FileKind, className?: string }) => {
	const accessor = useAccessor();
	const modelService = accessor.get('IModelService');
	const languageService = accessor.get('ILanguageService');
	const themeService = accessor.get('IWorkbenchThemeService');

	const [iconClasses, setIconClasses] = useState('');
	const [cssLoaded, setCssLoaded] = useState(true);

	// 检查和更新图标类
	useEffect(() => {
		if (!uri) return;

		const classes = getIconClasses(modelService, languageService, uri, fileKind);
		setIconClasses(classes.join(' '));

		// 监听主题变化
		const disposable = themeService.onDidFileIconThemeChange(() => {
			const updatedClasses = getIconClasses(modelService, languageService, uri, fileKind);
			setIconClasses(updatedClasses.join(' '));
		});

		return () => {
			disposable.dispose();
		};
	}, [uri, fileKind, modelService, languageService, themeService]);

	return (
		<div className={`file-icon-themable-tree show-file-icons ${className}`}>
			<span
				className={`flex items-center justify-center file-icon ${iconClasses}`}
				style={{
					marginTop: '2px',
					transition: 'none',
					animation: 'none'
				}}
			></span>
		</div>
	);
};
