/*--------------------------------------------------------------------------------------
 *  Copyright 2025 ZTE, Inc. All rights reserved.
 *  Licensed under the Apache License, Version 2.0. See LICENSE.txt for more information.
 *--------------------------------------------------------------------------------------*/

import React, { useState, useEffect, useCallback } from 'react'
import { RefreshableProviderName, SettingsOfProvider } from '../../../../../../../workbench/contrib/codeseek/common/codeseekSettingsTypes.js'
import { IDisposable } from '../../../../../../../base/common/lifecycle.js'
import { CodeseekSidebarState } from '../../../sidebarStateService.js'
import { CodeseekSettingsState } from '../../../../../../../workbench/contrib/codeseek/common/codeseekSettingsService.js'
import { ColorScheme } from '../../../../../../../platform/theme/common/theme.js'
import { CodeseekQuickEditState } from '../../../quickEditStateService.js'
import { RefreshModelStateOfProvider } from '../../../../../../../workbench/contrib/codeseek/common/refreshModelService.js'

import { ServicesAccessor } from '../../../../../../../editor/browser/editorExtensions.js';
import { IModelService } from '../../../../../../../editor/common/services/model.js';
import { IClipboardService } from '../../../../../../../platform/clipboard/common/clipboardService.js';
import { IContextViewService, IContextMenuService } from '../../../../../../../platform/contextview/browser/contextView.js';
import { IFileService } from '../../../../../../../platform/files/common/files.js';
import { IHoverService } from '../../../../../../../platform/hover/browser/hover.js';
import { IThemeService } from '../../../../../../../platform/theme/common/themeService.js';
import { ILLMMessageService } from '../../../../../../../workbench/contrib/codeseek/common/llmMessageService.js';
import { IRefreshModelService } from '../../../../../../../workbench/contrib/codeseek/common/refreshModelService.js';
import { ICodeseekSettingsService } from '../../../../../../../workbench/contrib/codeseek/common/codeseekSettingsService.js';
import { IEditCodeService, URIStreamState } from '../../../editCodeService.js';
import { IQuickEditStateService } from '../../../quickEditStateService.js';
import { ISidebarStateService } from '../../../sidebarStateService.js';
import { IChatThreadService } from '../../.././chatThreadType.js';
import { IInstantiationService } from '../../../../../../../platform/instantiation/common/instantiation.js'
import { ICodeEditorService } from '../../../../../../../editor/browser/services/codeEditorService.js'
import { ICommandService } from '../../../../../../../platform/commands/common/commands.js'
import { IContextKeyService } from '../../../../../../../platform/contextkey/common/contextkey.js'
import { INotificationService } from '../../../../../../../platform/notification/common/notification.js'
import { IAccessibilityService } from '../../../../../../../platform/accessibility/common/accessibility.js'
import { ILanguageConfigurationService } from '../../../../../../../editor/common/languages/languageConfigurationRegistry.js'
import { ILanguageFeaturesService } from '../../../../../../../editor/common/services/languageFeatures.js'
import { ILanguageDetectionService } from '../../../../../../services/languageDetection/common/languageDetectionWorkerService.js'
import { IKeybindingService } from '../../../../../../../platform/keybinding/common/keybinding.js'
import { IEnvironmentService } from '../../../../../../../platform/environment/common/environment.js'
import { IConfigurationService } from '../../../../../../../platform/configuration/common/configuration.js'
import { IPathService } from '../../../../../../../workbench/services/path/common/pathService.js'
import { IMetricsService } from '../../../../../../../workbench/contrib/codeseek/common/metricsService.js'
import { ICodeSeekExporerService } from '../../../../../../../workbench/contrib/codeseek/common/codeseekExporerService.js'
import { URI } from '../../../../../../../base/common/uri.js'
import { ITextAreaService } from '../../../InputBox2Service.js'
import { ILanguageService } from '../../../../../../../editor/common/languages/language.js'
import { IWorkbenchThemeService } from '../../../../../../../workbench/services/themes/common/workbenchThemeService.js'
import { IWorkspaceContextService } from '../../../../../../../platform/workspace/common/workspace.js'
import { IStorageService } from '../../../../../../../platform/storage/common/storage.js'
import { ICodebaseSearchService } from '../../../codebaseSearchService.js'
import { ICodeseekUacLoginService } from '../../../../common/uac/UacloginTypes.js'
import { IMentionService } from '../../../../common/selectedFileService.js'
import { ContainerState, ThreadsState, ThreadStreamState } from '../../.././chatThreadType.js'


// normally to do this you'd use a useEffect that calls .onDidChangeState(), but useEffect mounts too late and misses initial state changes

// even if React hasn't mounted yet, the variables are always updated to the latest state.
// React listens by adding a setState function to these listeners.
let quickEditState: CodeseekQuickEditState
const quickEditStateListeners: Set<(s: CodeseekQuickEditState) => void> = new Set()

let sidebarState: CodeseekSidebarState
const sidebarStateListeners: Set<(s: CodeseekSidebarState) => void> = new Set()

let chatContainerState: ContainerState
const chatContainerStateListeners: Set<(s: ContainerState) => void> = new Set()

let chatThreadsState: ThreadsState
const chatThreadsStateListeners: Set<(s: ThreadsState) => void> = new Set()

let chatThreadsStreamState: ThreadStreamState
const chatThreadsStreamStateListeners: Set<(containerId: string, threadId: string) => void> = new Set()

let settingsState: CodeseekSettingsState
const settingsStateListeners: Set<(s: CodeseekSettingsState) => void> = new Set()

let refreshModelState: RefreshModelStateOfProvider
const refreshModelStateListeners: Set<(s: RefreshModelStateOfProvider) => void> = new Set()
const refreshModelProviderListeners: Set<(p: RefreshableProviderName, s: RefreshModelStateOfProvider) => void> = new Set()

let colorThemeState: ColorScheme
const colorThemeStateListeners: Set<(s: ColorScheme) => void> = new Set()

const ctrlKZoneStreamingStateListeners: Set<(diffareaid: number, s: boolean) => void> = new Set()
const uriStreamingStateListeners: Set<(uri: URI, s: URIStreamState) => void> = new Set()



// must call this before you can use any of the hooks below
// this should only be called ONCE! this is the only place you don't need to dispose onDidChange. If you use state.onDidChange anywhere else, make sure to dispose it!
let wasCalled = false
export const _registerServices = (accessor: ServicesAccessor) => {

	const disposables: IDisposable[] = []

	// don't register services twice
	if (wasCalled) {
		return
		// console.error(`⚠️ Codeseek _registerServices was called again! It should only be called once.`)
	}
	wasCalled = true

	_registerAccessor(accessor)

	const stateServices = {
		quickEditStateService: accessor.get(IQuickEditStateService),
		sidebarStateService: accessor.get(ISidebarStateService),
		chatThreadsStateService: accessor.get(IChatThreadService),
		settingsStateService: accessor.get(ICodeseekSettingsService),
		refreshModelService: accessor.get(IRefreshModelService),
		themeService: accessor.get(IThemeService),
		editCodeService: accessor.get(IEditCodeService),
	}

	const { sidebarStateService, quickEditStateService, settingsStateService, chatThreadsStateService, refreshModelService, themeService, editCodeService } = stateServices

	quickEditState = quickEditStateService.state
	disposables.push(
		quickEditStateService.onDidChangeState(() => {
			quickEditState = quickEditStateService.state
			quickEditStateListeners.forEach(l => l(quickEditState))
		})
	)

	sidebarState = sidebarStateService.state
	disposables.push(
		sidebarStateService.onDidChangeState(() => {
			sidebarState = sidebarStateService.state
			sidebarStateListeners.forEach(l => l(sidebarState))
		})
	)

	chatContainerState = chatThreadsStateService.containerState
	disposables.push(
		chatThreadsStateService.onDidChangeCurrentContainer(() => {
			chatContainerState = chatThreadsStateService.containerState
			chatContainerStateListeners.forEach(l => l(chatContainerState))
		})
	)

	chatThreadsState = chatThreadsStateService.containerState.allContainers[chatThreadsStateService.containerState.currentContainerId].threadsState
	disposables.push(
		chatThreadsStateService.onDidChangeCurrentThread((containerId) => {
			chatThreadsState = chatThreadsStateService.containerState.allContainers[containerId].threadsState
			chatThreadsStateListeners.forEach(l => l(chatThreadsState))
		})
	)

	// same service, different state
	chatThreadsStreamState = chatThreadsStateService.streamState
	disposables.push(
		chatThreadsStateService.onDidChangeStreamState(({ containerId, threadId }) => {
			chatThreadsStreamState = chatThreadsStateService.streamState
			chatThreadsStreamStateListeners.forEach(l => l(containerId, threadId))
		})
	)

	settingsState = settingsStateService.state
	disposables.push(
		settingsStateService.onDidChangeState(() => {
			settingsState = settingsStateService.state
			settingsStateListeners.forEach(l => l(settingsState))
		})
	)

	refreshModelState = refreshModelService.state
	disposables.push(
		refreshModelService.onDidChangeState((providerName) => {
			refreshModelState = refreshModelService.state
			refreshModelStateListeners.forEach(l => l(refreshModelState))
			refreshModelProviderListeners.forEach(l => l(providerName, refreshModelState)) // no state
		})
	)

	colorThemeState = themeService.getColorTheme().type
	disposables.push(
		themeService.onDidColorThemeChange(theme => {
			colorThemeState = theme.type
			colorThemeStateListeners.forEach(l => l(colorThemeState))
		})
	)

	// no state
	disposables.push(
		editCodeService.onDidChangeCtrlKZoneStreaming(({ diffareaid }) => {
			const isStreaming = editCodeService.isCtrlKZoneStreaming({ diffareaid })
			ctrlKZoneStreamingStateListeners.forEach(l => l(diffareaid, isStreaming))
		})
	)
	disposables.push(
		editCodeService.onDidChangeURIStreamState(({ uri }) => {
			const isStreaming = editCodeService.getURIStreamState({ uri })
			uriStreamingStateListeners.forEach(l => l(uri, isStreaming))
		})
	)

	return disposables
}

const getReactAccessor = (accessor: ServicesAccessor) => {
	const reactAccessor = {
		IModelService: accessor.get(IModelService),
		IClipboardService: accessor.get(IClipboardService),
		IContextViewService: accessor.get(IContextViewService),
		IContextMenuService: accessor.get(IContextMenuService),
		IFileService: accessor.get(IFileService),
		IHoverService: accessor.get(IHoverService),
		IThemeService: accessor.get(IThemeService),
		ILLMMessageService: accessor.get(ILLMMessageService),
		IRefreshModelService: accessor.get(IRefreshModelService),
		ICodeseekSettingsService: accessor.get(ICodeseekSettingsService),
		IEditCodeService: accessor.get(IEditCodeService),
		IQuickEditStateService: accessor.get(IQuickEditStateService),
		ISidebarStateService: accessor.get(ISidebarStateService),
		IChatThreadService: accessor.get(IChatThreadService),
		IStorageService: accessor.get(IStorageService),
		ICodebaseSearchService: accessor.get(ICodebaseSearchService),

		IInstantiationService: accessor.get(IInstantiationService),
		ICodeEditorService: accessor.get(ICodeEditorService),
		ICommandService: accessor.get(ICommandService),
		IContextKeyService: accessor.get(IContextKeyService),
		INotificationService: accessor.get(INotificationService),
		IAccessibilityService: accessor.get(IAccessibilityService),
		ILanguageConfigurationService: accessor.get(ILanguageConfigurationService),
		ILanguageDetectionService: accessor.get(ILanguageDetectionService),
		ILanguageFeaturesService: accessor.get(ILanguageFeaturesService),
		IKeybindingService: accessor.get(IKeybindingService),

		IEnvironmentService: accessor.get(IEnvironmentService),
		IConfigurationService: accessor.get(IConfigurationService),
		IPathService: accessor.get(IPathService),
		IMetricsService: accessor.get(IMetricsService),
		ITextAreaService: accessor.get(ITextAreaService),
		ICodeSeekExporerService: accessor.get(ICodeSeekExporerService),
		ILanguageService: accessor.get(ILanguageService),
		IWorkbenchThemeService: accessor.get(IWorkbenchThemeService),
		IWorkspaceContextService: accessor.get(IWorkspaceContextService),
		ICodeseekUacLoginService: accessor.get(ICodeseekUacLoginService),
		IMentionService: accessor.get(IMentionService),
	} as const
	return reactAccessor
}

export type ReactAccessor = ReturnType<typeof getReactAccessor>


let reactAccessor_: ReactAccessor | null = null
const _registerAccessor = (accessor: ServicesAccessor) => {
	const reactAccessor = getReactAccessor(accessor)
	reactAccessor_ = reactAccessor
}

// -- services --
export const useAccessor = () => {
	if (!reactAccessor_) {
		throw new Error(`⚠️ Codeseek useAccessor was called before _registerServices!`)
	}

	return { get: <S extends keyof ReactAccessor,>(service: S): ReactAccessor[S] => reactAccessor_![service] }
}



// -- state of services --
export const useQuickEditState = () => {
	const [s, ss] = useState(quickEditState)
	useEffect(() => {
		ss(quickEditState)
		quickEditStateListeners.add(ss)
		return () => { quickEditStateListeners.delete(ss) }
	}, [ss])
	return s
}

export const useSidebarState = () => {
	const [s, ss] = useState(sidebarState)
	useEffect(() => {
		ss(sidebarState)
		sidebarStateListeners.add(ss)
		return () => { sidebarStateListeners.delete(ss) }
	}, [ss])
	return s
}

export const useSettingsState = () => {
	const [s, ss] = useState(settingsState)
	useEffect(() => {
		ss(settingsState)
		settingsStateListeners.add(ss)
		return () => { settingsStateListeners.delete(ss) }
	}, [ss])
	return s
}

export const useChatContainerState = () => {
	const [s, ss] = useState(chatContainerState)
	useEffect(() => {
		ss(chatContainerState)
		chatContainerStateListeners.add(ss)
		return () => { chatContainerStateListeners.delete(ss) }
	}, [ss])
	return s
}

export const useChatThreadsState = () => {
	const [s, ss] = useState(chatThreadsState)
	useEffect(() => {
		ss(chatThreadsState)
		chatThreadsStateListeners.add(ss)
		return () => { chatThreadsStateListeners.delete(ss) }
	}, [ss])
	return s
}


export const useChatThreadsStreamState = (containerId: string, threadId: string) => {
	const [s, ss] = useState<ThreadStreamState[string][string] | undefined>(chatThreadsStreamState[containerId]?.[threadId])
	useEffect(() => {
		ss(chatThreadsStreamState[containerId]?.[threadId])
		const listener = (containerId_: string, threadId_: string) => {
			if (containerId_ !== containerId || threadId_ !== threadId) return
			ss(chatThreadsStreamState[containerId_]?.[threadId_])
		}
		chatThreadsStreamStateListeners.add(listener)
		return () => { chatThreadsStreamStateListeners.delete(listener) }
	}, [ss, containerId, threadId])
	return s
}




export const useRefreshModelState = () => {
	const [s, ss] = useState(refreshModelState)
	useEffect(() => {
		ss(refreshModelState)
		refreshModelStateListeners.add(ss)
		return () => { refreshModelStateListeners.delete(ss) }
	}, [ss])
	return s
}


export const useRefreshModelListener = (listener: (providerName: RefreshableProviderName, s: RefreshModelStateOfProvider) => void) => {
	useEffect(() => {
		refreshModelProviderListeners.add(listener)
		return () => { refreshModelProviderListeners.delete(listener) }
	}, [listener, refreshModelProviderListeners])
}

export const useCtrlKZoneStreamingState = (listener: (diffareaid: number, s: boolean) => void) => {
	useEffect(() => {
		ctrlKZoneStreamingStateListeners.add(listener)
		return () => { ctrlKZoneStreamingStateListeners.delete(listener) }
	}, [listener, ctrlKZoneStreamingStateListeners])
}

export const useURIStreamState = (listener: (uri: URI, s: URIStreamState) => void) => {
	useEffect(() => {
		uriStreamingStateListeners.add(listener)
		return () => { uriStreamingStateListeners.delete(listener) }
	}, [listener, uriStreamingStateListeners])
}


export const useIsDark = () => {
	const [s, ss] = useState(colorThemeState)
	useEffect(() => {
		ss(colorThemeState)
		colorThemeStateListeners.add(ss)
		return () => { colorThemeStateListeners.delete(ss) }
	}, [ss])

	// s is the theme, return isDark instead of s
	const isDark = s === ColorScheme.DARK || s === ColorScheme.HIGH_CONTRAST_DARK
	return isDark

}

