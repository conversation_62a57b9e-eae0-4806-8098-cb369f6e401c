import { createDecorator } from '../../../../platform/instantiation/common/instantiation.js';
import { InstantiationType, registerSingleton } from '../../../../platform/instantiation/common/extensions.js';
import { URI } from '../../../../base/common/uri.js';
import { SymbolDefinition } from '../electron-main/ctags/ctagsRunner.js';
import { IMainProcessService } from '../../../../platform/ipc/common/mainProcessService.js';
import { IChannel } from '../../../../base/parts/ipc/common/ipc.js';
import { Disposable } from '../../../../base/common/lifecycle.js';
import { IWorkspaceContextService } from '../../../../platform/workspace/common/workspace.js';
import { IProductService } from '../../../../platform/product/common/productService.js';

/**
 * 渲染进程侧的 CTags 符号服务接口
 */
export interface ICtagsSymbolService {
	readonly _serviceBrand: undefined;

	/**
	 * 获取符号定义
	 * @param symbolName 要查询的符号名称
	 * @param scopeDirs 要查询的目录范围，不提供时使用当前工作区
	 * @param filter 可选的过滤条件
	 */
	getSymbolDefinitions(symbolName: string, scopeDirs?: URI[], filter?: {
		kind?: string[],       // 符号类型过滤，如'function'、'class'等
		language?: string[],   // 语言过滤
		path?: string          // 路径过滤
	}): Promise<SymbolDefinition[]>;

	/**
	 * 确保指定目录的索引已创建
	 */
	ensureIndex(scopeDir: URI, options?: {
		retryIfLastAttemptFailed?: boolean,
		ignoreExisting?: boolean,
		languages?: string[]    // 可选的语言过滤器
	}): Promise<void>;

	/**
	 * 删除索引
	 */
	deleteIndex(scopeDir: URI): Promise<void>;

	/**
	 * 获取索引状态
	 */
	getIndexStatus(scopeDir: URI): Promise<'unindexed' | 'indexing' | 'ready' | 'failed'>;

	/**
	 * 如果索引过期则重建
	 */
	reindexIfStale(scopeDir: URI): Promise<void>;
}

export const ICtagsSymbolService = createDecorator<ICtagsSymbolService>('ctagsSymbolService');

/**
 * 渲染进程侧的 CTags 符号服务实现
 */
export class CtagsSymbolService extends Disposable implements ICtagsSymbolService {
	readonly _serviceBrand: undefined;
	private readonly channel: IChannel;

	constructor(
		@IMainProcessService private readonly mainProcessService: IMainProcessService,
		@IWorkspaceContextService private workspaceContextService: IWorkspaceContextService,
		@IProductService private readonly productService: IProductService
	) {
		super();

		this.channel = this.mainProcessService.getChannel('codeseek-channel-ctags');
		this.initialize();

		// 监听工作区变化，重新初始化
		this._register(this.workspaceContextService.onDidChangeWorkspaceFolders(() => {
			this.initialize();
		}));
	}

	/**
	 * 初始化所有工作区文件夹的索引
	 */
	private async initialize(): Promise<void> {
		const workspace = await this.workspaceContextService.getCompleteWorkspace();
		if (workspace.folders.length > 0) {
			for (const folder of workspace.folders) {
				await this.ensureIndex(folder.uri, {
					retryIfLastAttemptFailed: true,
					ignoreExisting: true,
					languages: ['C', 'C++']
				});
			}
		}
	}

	async getSymbolDefinitions(symbolName: string, scopeDirs?: URI[], filter?: {
		kind?: string[],
		language?: string[],
		path?: string
	}): Promise<SymbolDefinition[]> {
		// 如果未指定范围，则使用当前工作区文件夹
		if (!scopeDirs || scopeDirs.length === 0) {
			scopeDirs = this.workspaceContextService.getWorkspace().folders.map(f => f.uri);
		}

		return this.channel.call('getSymbolDefinitions', [symbolName, scopeDirs, filter]);
	}

	async ensureIndex(scopeDir: URI, options = {
		retryIfLastAttemptFailed: false,
		ignoreExisting: false,
		languages: ['C', 'C++']
	}): Promise<void> {
		const dataFolderName = this.productService.dataFolderName;
		return this.channel.call('ensureIndex', [scopeDir, dataFolderName, options]);
	}

	async deleteIndex(scopeDir: URI): Promise<void> {
		return this.channel.call('deleteIndex', [scopeDir]);
	}

	async getIndexStatus(scopeDir: URI): Promise<'unindexed' | 'indexing' | 'ready' | 'failed'> {
		return this.channel.call('getIndexStatus', [scopeDir]);
	}

	async reindexIfStale(scopeDir: URI): Promise<void> {
		return this.channel.call('reindexIfStale', [scopeDir]);
	}
}

// 注册为单例服务
registerSingleton(ICtagsSymbolService, CtagsSymbolService, InstantiationType.Delayed);
