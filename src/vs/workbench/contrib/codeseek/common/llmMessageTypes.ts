/*--------------------------------------------------------------------------------------
 *  Copyright 2025 ZTE, Inc. All rights reserved.
 *  Licensed under the Apache License, Version 2.0. See LICENSE.txt for more information.
 *--------------------------------------------------------------------------------------*/

import { ChatMessage } from '../browser/chatThreadType.js';
import { ToolCallParamsType, ToolName, ToolCallType } from './toolsServiceTypes.js';
import { FeatureName, ProviderNames } from './codeseekSettingsTypes.js';
import { LLMMessageChannel } from '../electron-main/channel/llmMessageChannel.js';
import { CodeseekSettingsState } from './codeseekSettingsService.js';


export const errorDetails = (fullError: Error | null): string | null => {
	if (fullError === null) {
		return null;
	}
	else if (typeof fullError === 'object') {
		if (Object.keys(fullError).length === 0) return null;
		return JSON.stringify(fullError, null, 2);
	}
	else if (typeof fullError === 'string') {
		return null;
	}
	return null;
};


export type LLMChatMessage = {
	role: 'system' | 'user';
	content: string;
} | {
	role: 'assistant';
	content: string;
} | {
	role: 'tool';
	content: string; // result
	name: string;
	params: ToolCallParamsType[ToolName];
	id: string;
};

export type OnText = (p: { newText: string; fullText: string; newReasoning: string; fullReasoning: string }) => void;
export type OnFinalMessage = (p: { fullText: string; toolCalls?: ToolCallType[] }) => void; // id is tool_use_id
export type OnError = (p: { message: string; fullError: Error | null }) => void;
export type AbortRef = { current: (() => void) | null };


export const toLLMChatMessage = (c: ChatMessage): LLMChatMessage => {
	if (c.role === 'system' || c.role === 'user') {
		return { role: c.role, content: c.content || '(empty message)' };
	}
	else if (c.role === 'assistant')
		return { role: c.role, content: c.content || '(empty message)' };
	else if (c.role === 'tool')
		return { role: c.role, id: c.id, name: c.name, params: c.params, content: c.content || '(empty output)' };
	else {
		throw 1;
	}
};


export type LLMFIMMessage = {
	prefix: string;
	suffix: string;
	stopTokens: string[];
};

export type ChatMessageOpts = {
	messagesType: 'chatMessages';
	messages: LLMChatMessage[];
}

export type FimMessageOpts = {
	messagesType: 'FIMMessage';
	messages: LLMFIMMessage;
}

export type SendLLMType = ChatMessageOpts | FimMessageOpts;

// service types
export type ServiceSendLLMMessageParams = {
	containerId?: string;
	onText: OnText;
	onFinalMessage: OnFinalMessage;
	onError: OnError;
	logging: { loggingName: string };
	useProviderFor: FeatureName;
} & SendLLMType;

/**
 * Parameters for sending a language model (LLM) message with various configuration and callback options.
 * Combines message type-specific parameters with common LLM communication settings.
 *
 * @param onText Callback for receiving text chunks during message generation
 * @param onToolCall Optional callback for handling tool invocations
 * @param onFinalMessage Callback for receiving the final complete message
 * @param onError Callback for handling any errors during message generation
 * @param logging Logging configuration with a name for tracking
 * @param abortRef Reference to a function for cancelling the message generation
 * @param aiInstructions Specific instructions for the AI model
 * @param providerName Name of the LLM provider
 * @param modelName Specific model to use for message generation
 * @param settingsOfProvider Provider-specific configuration settings
 * @param requestId Optional unique identifier for the request
 * @param agentParamsFromPlugin Optional parameters from a plugin
 * @param llmChannel Optional communication channel for the LLM
 * @param agentParamsFromIde Optional parameters from the IDE
 */
export type SendLLMMessageParams = {
	onText: OnText;
	onFinalMessage: OnFinalMessage;
	onError: OnError;
	logging: { loggingName: string };
	abortRef: AbortRef;

	providerName: ProviderNames;
	modelName: string;
	codeseekSettingsState: CodeseekSettingsState;
	requestId: string;
	llmChannel?: LLMMessageChannel;
} & SendLLMType;



// can't send functions across a proxy, use listeners instead
export type BlockedMainLLMMessageParams = 'onText' | 'onFinalMessage' | 'onError' | 'abortRef';
export type MainSendLLMMessageParams = Omit<SendLLMMessageParams, BlockedMainLLMMessageParams> & SendLLMType;
export type MainLLMMessageAbortParams = { requestId: string };

export type EventLLMMessageOnTextParams = Parameters<OnText>[0] & { requestId: string };
export type EventLLMMessageOnFinalMessageParams = Parameters<OnFinalMessage>[0] & { requestId: string };
export type EventLLMMessageOnErrorParams = Parameters<OnError>[0] & { requestId: string };


// service -> main -> internal -> event (back to main)
// (browser)









// These are from 'ollama' SDK
interface OllamaModelDetails {
	parent_model: string;
	format: string;
	family: string;
	families: string[];
	parameter_size: string;
	quantization_level: string;
}

export type OllamaModelResponse = {
	name: string;
	modified_at: Date;
	size: number;
	digest: string;
	details: OllamaModelDetails;
	expires_at: Date;
	size_vram: number;
};

type OpenaiCompatibleModelResponse = {
	id: string;
	created: number;
	object: 'model';
	owned_by: string;
};

export type VLLMModelResponse = OpenaiCompatibleModelResponse;



// params to the true list fn
export type ModelListParams<ModelResponse> = {
	providerName: ProviderNames;
	codeseekSettingsState: CodeseekSettingsState;
	onSuccess: (param: { models: ModelResponse[] }) => void;
	onError: (param: { error: string }) => void;
};

// params to the service
export type ServiceModelListParams<modelResponse> = {
	onSuccess: (param: { models: modelResponse[] }) => void;
	onError: (param: { error: any }) => void;
};

type BlockedMainModelListParams = 'onSuccess' | 'onError';
export type MainModelListParams<modelResponse> = Omit<ModelListParams<modelResponse>, BlockedMainModelListParams> & { requestId: string };

export type EventModelListOnSuccessParams<modelResponse> = Parameters<ModelListParams<modelResponse>['onSuccess']>[0] & { requestId: string };
export type EventModelListOnErrorParams<modelResponse> = Parameters<ModelListParams<modelResponse>['onError']>[0] & { requestId: string };
