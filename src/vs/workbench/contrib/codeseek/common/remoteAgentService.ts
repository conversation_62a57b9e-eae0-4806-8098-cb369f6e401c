import { Disposable } from '../../../../base/common/lifecycle.js';
import { IChannel } from '../../../../base/parts/ipc/common/ipc.js';
import { IMainProcessService } from '../../../../platform/ipc/common/mainProcessService.js';
import { ICodeseekLogger } from './codeseekLogService.js';
import { ICodeseekSettingsService } from './codeseekSettingsService.js';
import { Event } from '../../../../base/common/event.js';
import { generateUuid } from '../../../../base/common/uuid.js';
import { InstantiationType, registerSingleton } from '../../../../platform/instantiation/common/extensions.js';
import { createDecorator } from '../../../../platform/instantiation/common/instantiation.js';
import { ChannelSendMessageParams } from '../electron-main/channel/remoteAgentChannel.js';
import { AgentSendMessageOpts, EventAgentMessageOnErrorParams, EventAgentMessageOnFinalMessageParams, EventAgentMessageOnTextParams, EventAgentMessageOnToolCallParams } from './remoteAgentServiceType.js';

export interface ICodeseekRemoteAgentService {
	readonly _serviceBrand: undefined;

	sendMessage(agentSendMessageOpts: AgentSendMessageOpts): string;
	abort(requestId: string): void;
}

export const ICodeseekRemoteAgentService = createDecorator<ICodeseekRemoteAgentService>('codeseekRemoteAgentService');
export class CodeseekRemoteAgentService extends Disposable implements ICodeseekRemoteAgentService {
	readonly _serviceBrand: undefined;
	private readonly channel: IChannel;
	private readonly agentMessageHooks = {
		onText: {} as { [eventId: string]: ((params: EventAgentMessageOnTextParams) => void) },
		onToolCall: {} as { [eventId: string]: ((params: EventAgentMessageOnToolCallParams) => void) },
		onError: {} as { [eventId: string]: ((params: EventAgentMessageOnErrorParams) => void) },
		onFinalMessage: {} as { [eventId: string]: ((params: EventAgentMessageOnFinalMessageParams) => void) },
	};

	constructor(
		@ICodeseekSettingsService private readonly codeseekSettingsService: ICodeseekSettingsService,
		@IMainProcessService private readonly mainProcessService: IMainProcessService,
		@ICodeseekLogger private readonly logger: ICodeseekLogger,
	) {
		super();
		this.channel = this.mainProcessService.getChannel('codeseek-channel-remoteAgent');

		this._register((this.channel.listen('onText_sendRemoteAgentMessage') satisfies Event<EventAgentMessageOnTextParams>)(e => { this.agentMessageHooks.onText[e.requestId]?.(e); }));
		this._register((this.channel.listen('onError_sendRemoteAgentMessage') satisfies Event<EventAgentMessageOnErrorParams>)(e => { this.agentMessageHooks.onError[e.requestId]?.(e); }));
		this._register((this.channel.listen('onFinalMessage_sendRemoteAgentMessage') satisfies Event<EventAgentMessageOnFinalMessageParams>)(e => { this.agentMessageHooks.onFinalMessage[e.requestId]?.(e); this._onRequestIdDone(e.requestId); }));
		this._register((this.channel.listen('onToolCall_sendRemoteAgentMessage') satisfies Event<EventAgentMessageOnToolCallParams>)(e => { this.agentMessageHooks.onToolCall[e.requestId]?.(e); }));
	}

	public sendMessage(agentSendMessageOpts: AgentSendMessageOpts): string {
		const { onText, onToolCall, onFinalMessage, onError, agentMessage } = agentSendMessageOpts;

		const modelSelection = this.codeseekSettingsService.getModelSelectionForContainer(
			agentMessage.featureName, agentMessage.containerId
		);
		if (modelSelection === null) {
			throw new Error('No model/provider selected');
		}
		const codeseekSettingsState = this.codeseekSettingsService.state;
		const { providerName, modelName } = modelSelection;

		let requestId: string;
		if (agentMessage.messageType === 'feedback') {
			requestId = agentMessage.feedback.taskId;
		} else {
			requestId = generateUuid();
		}
		this.agentMessageHooks.onText[requestId] = onText;
		this.agentMessageHooks.onToolCall[requestId] = onToolCall;
		this.agentMessageHooks.onFinalMessage[requestId] = onFinalMessage;
		this.agentMessageHooks.onError[requestId] = onError;

		this.channel.call('sendMessage', {
			agentMessage,
			codeseekSettingsState,
			requestId,
			providerName,
			modelName,
		} satisfies ChannelSendMessageParams);
		return requestId;
	}

	abort(requestId: string) {
		this.logger.info(`RemoteAgentService: aborting requestId: ${requestId}`);
		this.channel.call('abort', { requestId });
		this._onRequestIdDone(requestId);
	}

	_onRequestIdDone(requestId: string) {
		delete this.agentMessageHooks.onText[requestId];
		delete this.agentMessageHooks.onToolCall[requestId];
		delete this.agentMessageHooks.onError[requestId];
		delete this.agentMessageHooks.onFinalMessage[requestId];
	}

}

registerSingleton(ICodeseekRemoteAgentService, CodeseekRemoteAgentService, InstantiationType.Eager);
