import { CancellationToken } from '../../../../base/common/cancellation.js';
import { URI } from '../../../../base/common/uri.js';
import { IModelService } from '../../../../editor/common/services/model.js';
import { ICommandService } from '../../../../platform/commands/common/commands.js';
import { IFileService } from '../../../../platform/files/common/files.js';
import { InstantiationType, registerSingleton } from '../../../../platform/instantiation/common/extensions.js';
import { createDecorator, IInstantiationService } from '../../../../platform/instantiation/common/instantiation.js';
import { IWorkspaceContextService } from '../../../../platform/workspace/common/workspace.js';
import { QueryBuilder } from '../../../services/search/common/queryBuilder.js';
import { ISearchService } from '../../../services/search/common/search.js';
import { TerminalCommandId } from '../../terminal/common/terminal.js';
import { ICodeseekLogger } from './codeseekLogService.js';
import { ICtagsSymbolService } from '../browser/ctagsSymbolService.js';
import { SymbolDefinition } from '../electron-main/ctags/ctagsRunner.js';
import { IClangdSymbolService } from '../browser/clangdSymbolService.js';
import { ICodeseekFileService } from './codeseekFileService.js';
import { filenameToVscodeLanguage } from './helpers/detectLanguage.js';
import { convertFilePathToUri, getWorkspaceUri } from './helpers/path.js';
import {
	ApproveRequestResultType, AskReponseType, AskResponse, codeseekTools,
	DirectoryItem, ToolCallParamsType, ToolCallReturnType, ToolFns, ToolName,
	ToolNameEnum, ToolResultToString, ToolCallResultCode, ToolCallResultType, ToolCallType
} from './toolsServiceTypes.js';
import { LogLevel } from '../../../../platform/log/common/log.js';
import { AskMessage, IChatThreadService, PluginMessageOpts, userMessageOpts } from '../browser/chatThreadType.js';
import { IPluginTaskService } from '../browser/pluginTaskService.js';
import pWaitFor from '../../../common/pWaitFor.js';
import { ICodebaseRemoteService } from './codebaseRemoteService.js';
import { ICodebaseService, ISearchResult } from './codebaseTypes.js';
import { CodebaseSelection } from './selectedFileService.js';
import { stringifyCodebaseSelections } from './prompt/prompts.js';

const MAX_CHILDREN_URIs_PAGE = 500;
const computeDirectoryResult = async (
	fileService: IFileService,
	rootURI: URI,
	pageNumber: number = 1
): Promise<ToolCallReturnType[ToolNameEnum.LIST_FILES]> => {
	const stat = await fileService.resolve(rootURI, { resolveMetadata: false });
	if (!stat.isDirectory) {
		return { rootURI, children: null, hasNextPage: false, hasPrevPage: false, itemsRemaining: 0 };
	}

	const originalChildrenLength = stat.children?.length ?? 0;
	const fromChildIdx = MAX_CHILDREN_URIs_PAGE * (pageNumber - 1);
	const toChildIdx = MAX_CHILDREN_URIs_PAGE * pageNumber - 1; // INCLUSIVE
	const listChildren = stat.children?.slice(fromChildIdx, toChildIdx + 1) ?? [];

	const children: DirectoryItem[] = listChildren.map(child => ({
		name: child.name,
		isDirectory: child.isDirectory,
		isSymbolicLink: child.isSymbolicLink || false
	}));

	const hasNextPage = (originalChildrenLength - 1) > toChildIdx;
	const hasPrevPage = pageNumber > 1;
	const itemsRemaining = Math.max(0, originalChildrenLength - (toChildIdx + 1));

	return {
		rootURI,
		children,
		hasNextPage,
		hasPrevPage,
		itemsRemaining
	};
};

// this is just for ease of readability
export const tripleTick = ['```', '```'];

const fileContentsToString = (result: ToolCallReturnType[ToolNameEnum.READ_FILE]): string => {
	return `\
${result.uri.fsPath}
${tripleTick[0]}${filenameToVscodeLanguage(result.uri.fsPath) ?? ''}
${result.fileContents}
${tripleTick[1]}
`;
};
const directoryResultToString = (result: ToolCallReturnType[ToolNameEnum.LIST_FILES]): string => {
	if (!result.children) {
		return `Error: ${result.rootURI} is not a directory`;
	}

	let output = '';
	const entries = result.children;

	if (!result.hasPrevPage) {
		output += `${result.rootURI}\n`;
	}

	for (let i = 0; i < entries.length; i++) {
		const entry = entries[i];
		const isLast = i === entries.length - 1 && !result.hasNextPage;
		const prefix = isLast ? '└── ' : '├── ';

		output += `${prefix}${entry.name}${entry.isDirectory ? '/' : ''}${entry.isSymbolicLink ? ' (symbolic link)' : ''}\n`;
	}

	if (result.hasNextPage) {
		output += `└── (${result.itemsRemaining} results remaining...)\n`;
	}

	return output;
};

const validateQueryStr = (queryStr: unknown) => {
	if (typeof queryStr !== 'string') throw new Error('Error calling tool: provided query must be a string.');
	return queryStr;
};


// TODO!!!! check to make sure in workspace
const validateURI = (uriStr: unknown, _workspaceContextService: IWorkspaceContextService) => {
	if (typeof uriStr !== 'string') throw new Error('Error calling tool: provided uri must be a string.');
	return convertFilePathToUri(uriStr, _workspaceContextService);
};

const validatePageNum = (pageNumberUnknown: unknown) => {
	const proposedPageNum = Number.parseInt(pageNumberUnknown + '');
	const num = Number.isInteger(proposedPageNum) ? proposedPageNum : 1;
	const pageNumber = num < 1 ? 1 : num;
	return pageNumber;
};

export interface IToolsService {
	readonly _serviceBrand: undefined;
	toolFns: ToolFns;
	toolResultToString: ToolResultToString;
	isNeedApprove(toolName: ToolName): boolean;

	findSymbolCtagsDefinitions(symbolName: string): Promise<SymbolDefinition[]>;
	executeTool(containerId: string, threadId: string, toolCall: ToolCallType, userMessageOpts: userMessageOpts): Promise<ToolCallResultType>;
}

export const IToolsService = createDecorator<IToolsService>('ToolsService');

export class ToolsService implements IToolsService {

	readonly _serviceBrand: undefined;

	public toolFns: ToolFns;
	public toolResultToString: ToolResultToString;


	constructor(
		@IFileService fileService: IFileService,
		@IWorkspaceContextService private readonly _workspaceContextService: IWorkspaceContextService,
		@ISearchService searchService: ISearchService,
		@IInstantiationService instantiationService: IInstantiationService,
		@ICodeseekFileService codeseekFileService: ICodeseekFileService,
		@ICommandService commandService: ICommandService,
		@IModelService modelService: IModelService,
		@ICtagsSymbolService private readonly _ctagsSymbolService: ICtagsSymbolService,
		@ICodeseekLogger private readonly _codeseekLogService: ICodeseekLogger,
		@IClangdSymbolService private readonly _clangdSymbolService: IClangdSymbolService,
		@IChatThreadService private readonly _chatThreadService: IChatThreadService,
		@IPluginTaskService private readonly _pluginTaskService: IPluginTaskService,
		@ICodebaseRemoteService private readonly codebaseRemoteService: ICodebaseService,
	) {
		_codeseekLogService.setLevel(LogLevel.Info);
		const queryBuilder = instantiationService.createInstance(QueryBuilder);
		this.toolFns = {
			[ToolNameEnum.READ_FILE]: async (p: ToolCallParamsType[ToolNameEnum.READ_FILE], callback?: () => any) => {
				const { path: uriStr } = p;

				const uri = validateURI(uriStr, this._workspaceContextService);

				const readFileContents = await codeseekFileService.readFile(uri);
				const startLine = 1;
				let lines = 0;
				if (readFileContents) {
					const model = modelService.getModel(uri);
					if (model) {
						lines = model.getLineCount();
					} else {
						lines = (readFileContents.match(/\n/g) || []).length + (readFileContents.length > 0 ? 1 : 0);
					}
				}
				return { uri, fileContents: readFileContents, hasNextPage: false, startLine, endLine: startLine + lines };
			},
			[ToolNameEnum.LIST_FILES]: async (p: ToolCallParamsType[ToolNameEnum.LIST_FILES], callback?: () => any) => {
				const { path: pathStr } = p;

				const uri = validateURI(pathStr, this._workspaceContextService);
				// const pageNumber = validatePageNum(pageNumberUnknown);

				const dirResult = await computeDirectoryResult(fileService, uri);
				console.log('list_files result:', dirResult);

				return dirResult;
			},
			[ToolNameEnum.PATHNAME_SEARCH]: async (p: ToolCallParamsType[ToolNameEnum.PATHNAME_SEARCH], callback?: () => any) => {
				const { query: queryUnknown } = p;

				const queryStr = validateQueryStr(queryUnknown);
				// const pageNumber = validatePageNum(pageNumberUnknown);

				const query = queryBuilder.file(this._workspaceContextService.getWorkspace().folders.map(f => f.uri), { filePattern: queryStr, });
				const data = await searchService.fileSearch(query, CancellationToken.None);

				// const fromIdx = MAX_CHILDREN_URIs_PAGE * (pageNumber - 1);
				// const toIdx = MAX_CHILDREN_URIs_PAGE * pageNumber - 1;
				const uris = data.results
					// .slice(fromIdx, toIdx + 1) // paginate
					.map(({ resource, results }) => resource);

				// const hasNextPage = (data.results.length - 1) - toIdx >= 1;
				const hasNextPage = false;
				console.log('pathname_search result:', uris);

				return { queryStr, uris, hasNextPage };
			},
			[ToolNameEnum.SEARCH]: async (p: ToolCallParamsType[ToolNameEnum.SEARCH], callback?: () => any) => {
				const { query: queryUnknown } = p;

				const queryStr = validateQueryStr(queryUnknown);
				// const pageNumber = validatePageNum(pageNumberUnknown);

				const query = queryBuilder.text({ pattern: queryStr, }, this._workspaceContextService.getWorkspace().folders.map(f => f.uri));
				const data = await searchService.textSearch(query, CancellationToken.None);

				// const fromIdx = MAX_CHILDREN_URIs_PAGE * (pageNumber - 1);
				// const toIdx = MAX_CHILDREN_URIs_PAGE * pageNumber - 1;
				const uris = data.results
					// .slice(fromIdx, toIdx + 1) // paginate
					.map(({ resource, results }) => resource);

				// const hasNextPage = (data.results.length - 1) - toIdx >= 1;
				const hasNextPage = false;

				console.log('search result:', uris);

				return { queryStr, uris, hasNextPage };
			},
			[ToolNameEnum.CREATE_FILE]: async (p: ToolCallParamsType[ToolNameEnum.CREATE_FILE], callback?: () => any) => {
				const { path: pathStr } = p;

				const uri = validateURI(pathStr, this._workspaceContextService);

				return { uri };
			},
			[ToolNameEnum.UPDATE_FILE]: async (p: ToolCallParamsType[ToolNameEnum.UPDATE_FILE], callback?: () => any) => {
				const { path: pathStr, content: contentStr, start: startUnknown, end: endUnknown } = p;

				const uri = validateURI(pathStr, this._workspaceContextService);
				const start = validatePageNum(startUnknown);
				const end = validatePageNum(endUnknown);

				const res = await codeseekFileService.updateFile(uri, contentStr + '', start, end);

				return { content: res, uri: uri };
			},
			[ToolNameEnum.APPROVE_REQUEST]: async (p: ToolCallParamsType[ToolNameEnum.APPROVE_REQUEST], callback?: () => any) => {
				const { command } = p;
				const response: AskResponse = await callback?.();
				if (response.response === AskReponseType.yesButtonClicked) {
					if (command) {
						commandService.executeCommand(TerminalCommandId.SendSequence, command);
					}
					return { content: '用户同意此操作', response: AskReponseType.yesButtonClicked };
				}
				else if (response.response === AskReponseType.noButtonClicked) {
					return { content: '用户拒绝此操作', response: AskReponseType.noButtonClicked };
				}
				else {
					throw new Error('Invalid response type');
				}
			},
			[ToolNameEnum.ASK_FOLLOWUP_QUESTION]: async (p: ToolCallParamsType[ToolNameEnum.ASK_FOLLOWUP_QUESTION], callback?: () => any) => {
				const response: AskResponse = await callback?.();
				if (response.response === AskReponseType.messageResponse) {
					return { content: response.text as string, response: AskReponseType.messageResponse };
				}
				else {
					throw new Error('Invalid response type');
				}
			},
			[ToolNameEnum.CTAGS_QUERY]: async (p: ToolCallParamsType[ToolNameEnum.CTAGS_QUERY], callback?: () => any) => {
				const { symbol: symbolStr } = p;

				if (typeof symbolStr !== 'string') throw new Error('Error calling tool: provided symbol must be a string.');
				const symbol = symbolStr;
				const definitions = await this.findSymbolCtagsDefinitions(symbol);
				_codeseekLogService.info('ctags definitions result:', JSON.stringify(definitions));
				return definitions.map(def => ({
					rawLineContent: def.rawLineContent,
					name: def.name,
					path: def.path,
					scopePath: def.scopePath,
					line: def.line,
					kind: def.kind,
					language: def.language,
					positions: def.positions
				}));
			},
			[ToolNameEnum.CLANGD_QUERY]: async (p: ToolCallParamsType[ToolNameEnum.CLANGD_QUERY], callback?: () => any) => {
				const { filePath, line, character } = p;

				if (typeof filePath !== 'string') throw new Error('Error calling tool: provided filePath must be a string.');
				if (typeof line !== 'number') throw new Error('Error calling tool: provided line must be a number.');
				if (typeof character !== 'number') throw new Error('Error calling tool: provided character must be a number.');

				const uri = convertFilePathToUri(filePath, this._workspaceContextService);
				const result = await this._clangdSymbolService.getSymbolReferences(uri, line, character);
				return result;
			},
			[ToolNameEnum.SHOW_SUMMARY]: async (p: ToolCallParamsType[ToolNameEnum.SHOW_SUMMARY], callback?: () => any) => {

			},
			[ToolNameEnum.CODEBASE_SEARCH]: async (p: ToolCallParamsType[ToolNameEnum.CODEBASE_SEARCH], callback?: () => any) => {
				const { query } = p;
				const workspaceFolders = this._workspaceContextService.getWorkspace().folders;
				if (!workspaceFolders.length) {
					return [];
				}
				const results = await this.codebaseRemoteService.search({ userQuery: query, repoUri: workspaceFolders[0].uri });
				return results;
			},
		};

		const nextPageStr = (hasNextPage: boolean) => hasNextPage ? '\n\n(more on next page...)' : '';

		this.toolResultToString = {
			[ToolNameEnum.READ_FILE]: (result) => {
				const fileContents = fileContentsToString(result);
				return fileContents + nextPageStr(result.hasNextPage);
			},
			[ToolNameEnum.LIST_FILES]: (result) => {
				const dirTreeStr = directoryResultToString(result);
				return dirTreeStr + nextPageStr(result.hasNextPage);
			},
			[ToolNameEnum.PATHNAME_SEARCH]: (result) => {
				if (typeof result.uris === 'string') return result.uris;
				return result.uris.map(uri => uri.fsPath).join('\n') + nextPageStr(result.hasNextPage);
			},
			[ToolNameEnum.SEARCH]: (result) => {
				if (typeof result.uris === 'string') return result.uris;
				return result.uris.map(uri => uri.fsPath).join('\n') + nextPageStr(result.hasNextPage);
			},
			[ToolNameEnum.CREATE_FILE]: (result) => {
				return "File created successfully";
			},
			[ToolNameEnum.UPDATE_FILE]: (result) => {
				return result.content;
			},
			[ToolNameEnum.APPROVE_REQUEST]: (result) => {
				return result.content;
			},
			[ToolNameEnum.ASK_FOLLOWUP_QUESTION]: (result) => {
				return result.content;
			},
			[ToolNameEnum.CTAGS_QUERY]: (result) => {
				if (!Array.isArray(result)) return 'Error: Invalid result format';
				return JSON.stringify(result);
			},
			[ToolNameEnum.CLANGD_QUERY]: (result) => {
				if (!Array.isArray(result)) return 'Error: Invalid result format';
				return JSON.stringify(result);
			},
			[ToolNameEnum.SHOW_SUMMARY]: () => {
				return 'success to show'
			},
			[ToolNameEnum.CODEBASE_SEARCH]: (result: ISearchResult[]) => {
				const codebaseSelections: CodebaseSelection[] = result.map(r => ({
					type: 'Codebase' as const,
					fileURI: r.uri,
					title: 'Codebase',
					selectionStr: r.content,
					range: r.range,
					fromMention: false
				}));
				const workspaceUri = getWorkspaceUri(this._workspaceContextService).workspaceUri;
				const codebaseStr = stringifyCodebaseSelections(codebaseSelections, workspaceUri?.fsPath || '');
				return codebaseStr || '';
			},
		};
	}

	isNeedApprove(toolName: ToolName) {
		return codeseekTools[toolName].needApprove;
	}

	/**
	 * 使用 ctags 查找符号定义
	 * @param symbolName 要查找的符号名称
	 * @param filter 可选的过滤条件
	 * @returns 找到的符号定义数组
	 */
	async findSymbolCtagsDefinitions(symbolName: string): Promise<SymbolDefinition[]> {
		try {
			// 获取当前工作区的所有根目录
			const workspaceFolders = this._workspaceContextService.getWorkspace().folders;
			if (!workspaceFolders.length) {
				return [];
			}

			// 将所有工作区文件夹作为搜索范围
			const scopeDirs = workspaceFolders.map(folder => folder.uri);

			// 调用 ctags 服务进行符号查找
			const results = await this._ctagsSymbolService.getSymbolDefinitions(symbolName, scopeDirs);
			return results;
		} catch (error) {
			this._codeseekLogService.error('查找符号定义失败:', error);
			return [];
		}
	}

	async executeTool(containerId: string, threadId: string, toolCall: ToolCallType, userMessageOpts: userMessageOpts): Promise<ToolCallResultType> {
		const toolName = toolCall.name as ToolName;
		let toolResultVal: ToolCallReturnType[ToolName];
		let content: string = '';
		const ideTool = this.toolFns[toolName];

		const onWait = async () => {
			this._chatThreadService.setStreamState(containerId, threadId, { isStreaming: false });
			const askMessage: AskMessage = {
				type: 'tool',
				content: toolCall,
			};
			this._chatThreadService.getCurrentThread(containerId).state.askMessage = askMessage;
			await pWaitFor(() => this._chatThreadService.getCurrentThread(containerId).state.askResponse !== undefined, { interval: 100 });
			const currentThreadState = this._chatThreadService.getCurrentThread(containerId).state;
			const response = { type: currentThreadState.askResponse!.type, response: currentThreadState.askResponse?.response, text: currentThreadState.askResponse?.text };
			currentThreadState.askResponse = undefined;
			currentThreadState.askResponseText = undefined;
			return response;
		};
		if (ideTool === undefined) {
			let externalToolsNeedApprove = true;
			const filteredTools = (userMessageOpts as PluginMessageOpts).taskInfo?.externalTools?.filter(tool => tool.toolName === toolName);
			if (filteredTools && filteredTools.length > 0) {
				externalToolsNeedApprove = filteredTools[0].needApprove;
			}
			if (externalToolsNeedApprove) {
				toolResultVal = await this.toolFns[ToolNameEnum.APPROVE_REQUEST](toolCall.params as any, onWait);
				const approveResult = toolResultVal as ApproveRequestResultType;
				if (approveResult.response === AskReponseType.yesButtonClicked && userMessageOpts.from === 'Plugin') {
					this._pluginTaskService.fireToolCall((userMessageOpts as PluginMessageOpts).taskInfo.taskId, toolName, toolCall.params);
				}
				return { code: ToolCallResultCode.success, name: toolName, result: toolResultVal, error: '', content };
			}
			this._chatThreadService.setStreamState(containerId, threadId, { isStreaming: true });
			if (userMessageOpts.from === 'Plugin') {
				this._pluginTaskService.fireToolCall((userMessageOpts as PluginMessageOpts).taskInfo.taskId, toolName, toolCall.params);
				return { code: ToolCallResultCode.success, name: toolName, result: true, error: '', content };
			}
		}

		try {
			if (this.isNeedApprove(toolName)) {
				toolResultVal = await this.toolFns[ToolNameEnum.APPROVE_REQUEST](toolCall.params as any, onWait);
				content = this.toolResultToString[toolName](toolResultVal as any);
				const approveResult = toolResultVal as ApproveRequestResultType;
				if (approveResult.response === AskReponseType.noButtonClicked) {
					return { code: ToolCallResultCode.success, name: toolName, result: toolResultVal, error: '', content };
				}
			}
			this._chatThreadService.setStreamState(containerId, threadId, { isStreaming: true });
			toolResultVal = await ideTool(toolCall.params as any);
			content = this.toolResultToString[toolName](toolResultVal as any);
			return { code: ToolCallResultCode.success, name: toolName, result: toolResultVal, error: '', content };
		} catch (error) {
			return { code: ToolCallResultCode.failure, name: toolName, result: null, error: error.message, content };
		}
	}
	//根据目录名，列出目录结构

}

registerSingleton(IToolsService, ToolsService, InstantiationType.Eager);
