import { URI } from '../../../../base/common/uri.js';
import { ISearchResult } from './codebaseTypes.js';

export type ToolCallType = {
	name: ToolName;
	params: ToolCallParamsType[ToolName];
	id: string;
	command?: string;
};

export const enum ToolCallResultCode {
	success = "0",
	failure = "1",
}

export type ToolCallResultType = {
	code: ToolCallResultCode;
	name: ToolName;
	content: string;
	result: ToolCallReturnType[ToolName] | null;
	error: string | null;
};

export enum AskReponseType {
	yesButtonClicked = 'yesButtonClicked',
	noButtonClicked = 'noButtonClicked',
	messageResponse = 'messageResponse',
}

export type AskResponse = {
	type: string;
	response?: AskReponseType;
	text?: string;
};

export type InternalToolInfo = {
	name: string;
	description: string;
	params: {
		[paramName: string]: { type: string; description: string | undefined }; // name -> type
	};
	required: string[]; // required paramNames
	needApprove: boolean;
};

// 定义工具名称枚举
export enum ToolNameEnum {
	READ_FILE = 'read_file',
	LIST_FILES = 'list_files',
	PATHNAME_SEARCH = 'pathname_search',
	SEARCH = 'search',
	CREATE_FILE = 'create_file',
	UPDATE_TO_FILE = 'update_to_file',
	APPROVE_REQUEST = 'approve_request',
	ASK_FOLLOWUP_QUESTION = 'ask_followup_question',
	CTAGS_QUERY = 'ctags_query',
	CLANGD_QUERY = 'clangd_query',
	SHOW_SUMMARY = 'show_summary',
	CODEBASE_SEARCH = 'codebase_search',
}

const paginationHelper = {
	desc: `Very large results may be paginated (indicated in the result). Pagination fails gracefully if out of bounds or invalid page number.`,
	param: { pageNumber: { type: 'number', description: 'The page number (optional, default is 1).' }, }
} as const;

export const codeseekTools = {
	[ToolNameEnum.READ_FILE]: {
		name: ToolNameEnum.READ_FILE,
		description: `请求读取指定相对路径下的文件内容。当您需要查看一个未知内容的现有文件时使用此功能，例如分析代码、审阅文本文件或从配置文件中提取信息。可能不适合其他类型的二进制文件，因为它将原始内容作为字符串返回。`,
		params: {
			uri: { type: 'string', description: "要读取的文件的相对路径（相对于当前工作目录）" },
		},
		required: ['uri'],
		needApprove: false
	},

	[ToolNameEnum.LIST_FILES]: {
		name: ToolNameEnum.LIST_FILES,
		description: `请求列出指定目录中的所有文件和子目录。`,
		params: {
			uri: { type: 'string', description: "要列出内容的目录路径（相对于当前工作目录）" },
		},
		required: ['uri'],
		needApprove: false
	},

	[ToolNameEnum.PATHNAME_SEARCH]: {
		name: ToolNameEnum.PATHNAME_SEARCH,
		description: `Returns all pathnames that match a given grep query. You should use this when looking for a file with a specific name or path. This does NOT search file content. ${paginationHelper.desc}`,
		params: {
			query: { type: 'string', description: "" },
		},
		required: ['query'],
		needApprove: false
	},

	[ToolNameEnum.SEARCH]: {
		name: ToolNameEnum.SEARCH,
		description: `Returns all code excerpts containing the given string or grep query. This does NOT search pathname. As a follow-up, you may want to use read_file to view the full file contents of the results. ${paginationHelper.desc}`,
		params: {
			query: { type: 'string', description: "" },
			...paginationHelper.param,
		},
		required: ['query'],
		needApprove: false
	},

	[ToolNameEnum.CREATE_FILE]: {
		name: ToolNameEnum.CREATE_FILE,
		description: `Creates a new file at the given URI with the given content.`,
		params: {
			uri: { type: 'string', description: "" },
			content: { type: 'string', description: "" },
		},
		required: ['uri', 'content'],
		needApprove: true
	},

	[ToolNameEnum.UPDATE_TO_FILE]: {
		name: ToolNameEnum.UPDATE_TO_FILE,
		description: `Updates the file at the given URI with the given content.`,
		params: {
			uri: { type: 'string', description: "" },
			content: { type: 'string', description: "" },
			start: { type: 'number', description: "" },
			end: { type: 'number', description: "" },
		},
		required: ['uri', 'content', 'start', 'end'],
		needApprove: true
	},

	[ToolNameEnum.APPROVE_REQUEST]: {
		name: ToolNameEnum.APPROVE_REQUEST,
		description: `Attemps to complete the given text at the given position in the given file.`,
		params: {
			result: { type: 'string', description: 'The result from a previous attempt at completion.' },
			command: { type: 'string', description: 'Command to demonstrate result (optional)' },
		},
		required: ['result'],
		needApprove: false
	},

	[ToolNameEnum.ASK_FOLLOWUP_QUESTION]: {
		name: ToolNameEnum.ASK_FOLLOWUP_QUESTION,
		description: `向用户提问以收集完成任务所需额外信息。当您遇到歧义、需要澄清或需要更多细节以有效进行时，应使用此工具。它通过允许与用户直接沟通，实现互动解决问题。谨慎使用此工具，以在收集必要信息和避免过多来回沟通之间保持平衡`,
		params: {
			question: { type: 'string', description: '要问用户的问题。这应该是一个清晰、具体的问题，能够解决您所需的信息。' },
		},
		required: ['question'],
		needApprove: false
	},

	[ToolNameEnum.CTAGS_QUERY]: {
		name: ToolNameEnum.CTAGS_QUERY,
		description: `Use the ctags tool to output the file path, type, and starting line and column number of a given code symbol.`,
		params: {
			symbol: { type: 'string', description: 'The code symbol to query.' },
		},
		required: ['symbol'],
		needApprove: false
	},
	[ToolNameEnum.CLANGD_QUERY]: {
		name: ToolNameEnum.CLANGD_QUERY,
		description: `Use the clangd tool to output the symbol references.`,
		params: {
			filePath: { type: 'string', description: 'The file path to query.' },
			line: { type: 'number', description: 'The symbol line number.' },
			character: { type: 'number', description: 'The symbol character number.' },
		},
		required: ['filePath', 'line', 'character'],
		needApprove: false
	},
	[ToolNameEnum.SHOW_SUMMARY]: {
		name: ToolNameEnum.SHOW_SUMMARY,
		description: `Using folding tags to display detailed information and summaries to users.`,
		params: {
			summary: { type: 'string', description: 'The summary of the information.' },
			detail: { type: 'string', description: 'The detail of the information.' },
		},
		required: ['summary', 'detail'],
		needApprove: false
	},
	[ToolNameEnum.CODEBASE_SEARCH]: {
		name: ToolNameEnum.CODEBASE_SEARCH,
		description: `从代码库中查找与搜索查询最相关的代码片段。这是一个语义搜索工具，因此查询应该在语义上与所需内容匹配。`,
		params: {
			query: { type: 'string', description: '用于查找相关代码的搜索查询。除非有明确的理由，否则您应该重复使用用户的精确查询及其措辞。' },
		},
		required: ['query'],
		needApprove: false
	},
} satisfies { [key in ToolNameEnum]: InternalToolInfo };

export type ToolName = ToolNameEnum;

export const toolNamesSet = new Set<string>(Object.values(ToolNameEnum));
export const isAToolName = (toolName: string): toolName is ToolName => {
	const isAToolName = toolNamesSet.has(toolName);
	return isAToolName;
};


export type ToolParamNames<T extends ToolName> = keyof typeof codeseekTools[T]['params'];
export type ToolParamsObj<T extends ToolName> = { [paramName in ToolParamNames<T>]: unknown };


export type ToolCallParamsType = {
	[ToolNameEnum.READ_FILE]: { path: string };
	[ToolNameEnum.LIST_FILES]: { path: string };
	[ToolNameEnum.PATHNAME_SEARCH]: { query: string };
	[ToolNameEnum.SEARCH]: { query: string };
	[ToolNameEnum.CREATE_FILE]: { path: string };
	[ToolNameEnum.UPDATE_TO_FILE]: { path: string; content: string; start: number; end: number };
	[ToolNameEnum.APPROVE_REQUEST]: { content: string; command: string };
	[ToolNameEnum.ASK_FOLLOWUP_QUESTION]: { question: string };
	[ToolNameEnum.CTAGS_QUERY]: { symbol: string };
	[ToolNameEnum.CLANGD_QUERY]: { filePath: string, line: number, character: number };
	[ToolNameEnum.SHOW_SUMMARY]: { summary: string, detail: string };
	[ToolNameEnum.CODEBASE_SEARCH]: { query: string };
};


export type ReadFileResultType = { uri: URI; fileContents: string; hasNextPage: boolean; startLine: number; endLine: number };
export type ListFilesResultType = { rootURI: URI; children: DirectoryItem[] | null; hasNextPage: boolean; hasPrevPage: boolean; itemsRemaining: number };
export type PathnameSearchResultType = { queryStr: string; uris: URI[] | string; hasNextPage: boolean };
export type SearchResultType = { queryStr: string; uris: URI[] | string; hasNextPage: boolean };
export type CreateFileResultType = {};
export type UpdateToFileResultType = { content: string; uri: URI };
export type ApproveRequestResultType = { content: string; response: AskReponseType.yesButtonClicked | AskReponseType.noButtonClicked };
export type AskFollowupQuestionResultType = { content: string };
export type CtagsQueryResultType = {
	rawLineContent: string;
	name: string;
	path: string;
	scopePath: string;
	line: number;
	kind: string;
	language: string;
	positions?: [number, number];
}[];
export type ClangdQueryResultType = {
	uri: string;
	range: {
		start: { line: number; character: number };
		end: { line: number; character: number };
	};
}[];

export type ToolCallReturnType = {
	[ToolNameEnum.READ_FILE]: ReadFileResultType;
	[ToolNameEnum.LIST_FILES]: ListFilesResultType;
	[ToolNameEnum.PATHNAME_SEARCH]: PathnameSearchResultType;
	[ToolNameEnum.SEARCH]: SearchResultType;
	[ToolNameEnum.CREATE_FILE]: CreateFileResultType;
	[ToolNameEnum.UPDATE_TO_FILE]: UpdateToFileResultType;
	[ToolNameEnum.APPROVE_REQUEST]: ApproveRequestResultType;
	[ToolNameEnum.ASK_FOLLOWUP_QUESTION]: AskFollowupQuestionResultType;
	[ToolNameEnum.CTAGS_QUERY]: CtagsQueryResultType;
	[ToolNameEnum.CLANGD_QUERY]: ClangdQueryResultType;
	[ToolNameEnum.SHOW_SUMMARY]: void;
	[ToolNameEnum.CODEBASE_SEARCH]: ISearchResult[];
};

export type DirectoryItem = {
	name: string;
	isDirectory: boolean;
	isSymbolicLink: boolean;
};

// export type ToolNeedApprove = { [T in ToolName & keyof TooLNeedApproveBool]: TooLNeedApproveBool[T] };
export type ToolFns = { [T in ToolName]: (p: ToolCallParamsType[T], callback?: () => any) => Promise<ToolCallReturnType[T]> };
export type ToolResultToString = { [T in ToolName]: (result: ToolCallReturnType[T]) => string };
