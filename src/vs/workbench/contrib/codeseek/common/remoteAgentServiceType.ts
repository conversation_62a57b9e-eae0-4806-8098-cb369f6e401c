import { PlanAgentMessage } from '../browser/agent/planAgentService.js';

export const PlanAgentTopic = 'plan';

export enum RemoteAgentMessageType {
	createPlan = 'createPlan',
	feedback = 'feedback',
}

export type Feedback = {
	taskId: string;
	taskResult: boolean;
	taskSummary: string;
};

export type Plan = {
	task: string;
	taskContext: string;
	taskPlan: {
		content: string;
		detail: string;
	}[];
	dependencies: Map<number, number[]>;
}

export type PlanType = {
	id: string,
	params: Plan
}




export type OnText = (p: { newText: string; fullText: string; newReasoning: string; fullReasoning: string }) => void;
export type OnToolCall = (p: { fullText: string; plan: PlanType }) => void;
export type OnFinalMessage = (p: { fullText: string; }) => void; // id is tool_use_id
export type OnError = (p: { message: string; fullError: Error | null }) => void;
export type AbortRef = { current: (() => void) | null };

export type AgentEvent = {
	onText: OnText;
	onToolCall: OnToolCall;
	onError: OnError;
	onFinalMessage: OnFinalMessage;
}

export type MessageOpts = AgentEvent & PlanAgentMessage

export type BlockedAgentMessageParams = 'onText' | 'onToolCall' | 'onError' | 'onFinalMessage' | 'abortRef';
export type EventAgentMessageOnTextParams = Parameters<OnText>[0] & { requestId: string };
export type EventAgentMessageOnErrorParams = Parameters<OnError>[0] & { requestId: string };
export type EventAgentMessageOnFinalMessageParams = Parameters<OnFinalMessage>[0] & { requestId: string };
export type EventAgentMessageOnToolCallParams = Parameters<OnToolCall>[0] & { requestId: string };


export type AgentSendMessageOpts = {
	agentMessage: PlanAgentMessage;
	onText: OnText;
	onToolCall: OnToolCall;
	onError: OnError;
	onFinalMessage: OnFinalMessage;
}
