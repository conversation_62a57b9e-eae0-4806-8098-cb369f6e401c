import { createDecorator } from '../../../../platform/instantiation/common/instantiation.js';
import { InstantiationType, registerSingleton } from '../../../../platform/instantiation/common/extensions.js';
import { Disposable, IDisposable } from '../../../../base/common/lifecycle.js';
import { IWorkspaceContextService } from '../../../../platform/workspace/common/workspace.js';
import { EnsureIndexParams, searchParams, ICodebaseService, ISearchResult, QueryContextData, DeleteIndexParams, EventCodebaseOnProgressParams, ClangdConfigData, LLmConfigData, CreateRepoConfig, PauseRemoteIndexParams, ResumeRemoteIndexParams, UpdateIndexParams, CodeContextItem } from './codebaseTypes.js';
import { ICodeseekSettingsService } from './codeseekSettingsService.js';
import { CodebaseModes, FeatureNames, SettingsOfCodebase } from './codeseekSettingsTypes.js';
import { getWorkspaceUri } from './helpers/path.js';
import { defaultExcludeFolders, ICodeseekFileService } from './codeseekFileService.js';
import { URI } from '../../../../base/common/uri.js';
import { ICodeseekLogger } from './codeseekLogService.js';
import { IChannel } from '../../../../base/parts/ipc/common/ipc.js';
import { Event } from '../../../../base/common/event.js';
import { IMainProcessService } from '../../../../platform/ipc/common/mainProcessService.js';
import { IConfigurationService } from '../../../../platform/configuration/common/configuration.js';
import { IFileService, FileChangeType } from '../../../../platform/files/common/files.js';
import { autorun, derived } from '../../../../base/common/observable.js';
import { ISCMViewService } from '../../scm/common/scm.js';
import * as path from '../../../../base/common/path.js';
import { CODESEEK_VIEW_CONTAINER_ID } from '../browser/actionIDs.js';

export interface ICodebaseRemoteService extends ICodebaseService {
	readonly _serviceBrand: undefined;

	initialize(): void;
}

export const ICodebaseRemoteService = createDecorator<ICodebaseRemoteService>('codeseekRemoteService');
export class CodebaseRemoteService extends Disposable implements ICodebaseRemoteService {
	readonly _serviceBrand: undefined;
	private workspaceUri: URI | null = null;
	private readonly channel: IChannel;
	private fileWatcher: IDisposable | undefined;
	private pendingFileChanges: Map<string, { uri: URI, status: 'add' | 'delete' | 'update' }> = new Map();
	private updateInterval: any;
	private readonly periodicUpdateTime = 5 * 60 * 1000; // 5分钟
	private isRunningCodebaseProcess = false;
	private isFirstInit = true;

	private excludeDirs: string[] = [
		...defaultExcludeFolders,
		'node_modules',
		'.git',
		'dist',
		'build',
		'target',
		'out',
		'.idea',
		'.vscode',
		'**/node_modules/**',
		'**/dist/**',
		'**/build/**',
		'**/target/**',
		'**/out/**',
		'.git/**',
		'**/.idea/**',
		'**/.vscode/**',
	];

	private readonly codebaseHooks = {
		onProgress: {} as { [repoUriStr: string]: ((params: EventCodebaseOnProgressParams) => void) },
	};

	private readonly _activeRepositoryHistoryItemRefName = derived(reader => {
		const repository = this.scmViewService.activeRepository.read(reader);
		const historyProvider = repository?.provider.historyProvider.read(reader);
		const historyItemRef = historyProvider?.historyItemRef.read(reader);

		return historyItemRef?.name;
	});

	constructor(
		@IMainProcessService private readonly _mainProcessService: IMainProcessService,
		@IWorkspaceContextService private _workspaceContextService: IWorkspaceContextService,
		@ICodeseekSettingsService private readonly _codeseekSettingsService: ICodeseekSettingsService,
		@ICodeseekFileService private readonly _codeseekFileService: ICodeseekFileService,
		@ICodeseekLogger private readonly logger: ICodeseekLogger,
		@IConfigurationService private readonly _configurationService: IConfigurationService,
		@IFileService private readonly _fileService: IFileService,
		@ISCMViewService private readonly scmViewService: ISCMViewService,
	) {
		super();
		this.channel = this._mainProcessService.getChannel('codeseek-channel-codebase');
		this.workspaceUri = getWorkspaceUri(this._workspaceContextService).workspaceUri;
		this.startCodebaseProcess();
		this.setupFileWatcher();

		this._register({
			dispose: () => {
				this.stopCodebaseProcess()
				this.disposeFileWatcher();
				this.stopPeriodicUpdate();
			}
		});

		this._register(autorun(reader => {
			const repository = this.scmViewService.activeRepository.read(reader);
			const historyItemRefName = this._activeRepositoryHistoryItemRefName.read(reader);
			this.logger.info(`active repository: ${repository?.provider.name}, branch: ${historyItemRefName}`);
			const status = this._codeseekSettingsService.state.settingsOfCodebase[this.workspaceUri!.fsPath]?.[CodebaseModes.Remote]?.status;
			if (this.isRunningCodebaseProcess && status === 'completed') {
				if (repository && historyItemRefName) {
					this.logger.info(`the repository checkout branch, to update codebase`);
					this.initialize();
				}
			}
		}));

		this._register(this._workspaceContextService.onDidChangeWorkspaceFolders(() => {
			this.startCodebaseProcess();
			this.setupFileWatcher();
		}));

		this._register(this._codeseekSettingsService.onDidDeleteCodebase(async e => {
			this.logger.info(`delete codebase of workspace: ${this.workspaceUri?.fsPath}`);
			this.deleteIndex({
				repoUri: this.workspaceUri!,
			});
		}));
		this._register(this._codeseekSettingsService.onDidReSyncCodebase(async e => {
			this.logger.info(`reSync codebase of workspace: ${this.workspaceUri?.fsPath}`);
			await this.deleteIndex({
				repoUri: this.workspaceUri!,
			}, false);
			this.initialize();
		}));

		this._register(this._codeseekSettingsService.onDidPauseCodebase(async e => {
			this.logger.info(`pause codebase of workspace: ${this.workspaceUri?.fsPath}`);
			await this.pauseRemoteIndex({
				repoUri: this.workspaceUri!,
			});
		}));

		this._register(this._codeseekSettingsService.onDidResumeCodebase(async e => {
			this.logger.info(`resume codebase of workspace: ${this.workspaceUri?.fsPath}`);
			await this.resumeRemoteIndex({
				repoUri: this.workspaceUri!,
			});
		}));

		this._register((this.channel.listen('onProgress_remote') satisfies Event<EventCodebaseOnProgressParams>)(e => {
			this.codebaseHooks.onProgress[e.repoUri.fsPath]?.(e);
		}));

		this._register(this._codeseekSettingsService.onDidUpdateCodebase(() => {
			this.startPeriodicUpdate();
		}));

		this._register(this._codeseekSettingsService.onDidChangeCodebaseSwitchStatus(() => {
			if (!this.workspaceUri) {
				return;
			}
			const isRemoteCodebaseEnabled = this._codeseekSettingsService.state.globalSettings.enableRemoteCodeBase;
			const repoSettings = this._codeseekSettingsService.state.settingsOfCodebase[this.workspaceUri.fsPath];
			const remoteSettings = repoSettings?.[CodebaseModes.Remote];
			if (isRemoteCodebaseEnabled) {
				if (this.isRunningCodebaseProcess) {
					this.initialize();
				}
			} else {
				if (remoteSettings && remoteSettings.process && remoteSettings.process > 0 && remoteSettings.process < 1) {
					this.pauseRemoteIndex({
						repoUri: this.workspaceUri
					});
				}
			}
		}));
	}

	private setupFileWatcher(): void {
		this.disposeFileWatcher();

		if (!this.workspaceUri) {
			this.logger.info('workspaceUri is not set, skip setting up file watcher');
			return;
		}

		this.fileWatcher = this._fileService.watch(this.workspaceUri, {
			recursive: true,
			excludes: this.excludeDirs,
		});

		this._register(this._fileService.onDidFilesChange(async e => {
			if (!this.workspaceUri || !this._codeseekSettingsService.state.globalSettings.enableRemoteCodeBase) {
				return;
			}

			if (e.affects(this.workspaceUri, FileChangeType.ADDED, FileChangeType.DELETED, FileChangeType.UPDATED)) {
				if (e.gotAdded()) {
					for (const resource of e.rawAdded) {
						this.pendingFileChanges.set(resource.fsPath, { uri: resource, status: 'add' });
					}
				}

				if (e.gotDeleted()) {
					for (const resource of e.rawDeleted) {
						this.pendingFileChanges.set(resource.fsPath, { uri: resource, status: 'delete' });
					}
				}

				if (e.gotUpdated()) {
					for (const resource of e.rawUpdated) {
						this.pendingFileChanges.set(resource.fsPath, { uri: resource, status: 'update' });
					}
				}
			}
		}));
	}

	private startPeriodicUpdate(): void {
		if (this.updateInterval) {
			return;
		}
		this.logger.info(`start periodic update for codebase of workspace: ${this.workspaceUri?.fsPath}`);
		this.updateInterval = setInterval(() => {
			if (this.pendingFileChanges.size > 0) {
				this.processFileChanges();
			}
		}, this.periodicUpdateTime);
	}

	private stopPeriodicUpdate(): void {
		if (this.updateInterval) {
			clearInterval(this.updateInterval);
			this.updateInterval = undefined;
		}
	}

	private async processFileChanges(): Promise<void> {
		if (this.pendingFileChanges.size === 0) {
			return;
		}

		const changeEntries = Array.from(this.pendingFileChanges.entries());
		const batchSize = 10; // 每批处理的文件数量
		const failedChanges: Map<string, { uri: URI, status: 'add' | 'delete' | 'update' }> = new Map();

		for (let i = 0; i < changeEntries.length; i += batchSize) {
			const batch = changeEntries.slice(i, i + batchSize);
			const results = await Promise.allSettled(batch.map(async ([filePath, change]) => {
				try {
					const isUpdateSuccess = await this.updateIndex({
						fileUri: change.uri,
						status: change.status
					});

					return { filePath, isUpdateSuccess };
				} catch (error) {
					return { filePath, isUpdateSuccess: false };
				}
			}));

			for (const result of results) {
				if (result.status === 'fulfilled') {
					const { filePath, isUpdateSuccess } = result.value;
					if (isUpdateSuccess) {
						this.pendingFileChanges.delete(filePath);
					} else {
						failedChanges.set(filePath, this.pendingFileChanges.get(filePath)!);
					}
				}
			}
		}

		if (failedChanges.size > 0) {
			this.logger.info(`${failedChanges.size} 个文件变更处理失败，将在下次更新中重试`);
			for (const [filePath, change] of failedChanges.entries()) {
				this.pendingFileChanges.set(filePath, change);
			}
		}
	}

	private disposeFileWatcher(): void {
		if (this.fileWatcher) {
			this.fileWatcher.dispose();
			this.fileWatcher = undefined;
		}
	}

	public async startCodebaseProcess() {
		this.isRunningCodebaseProcess = await this.channel.call('startRemoteCodebase');
		if (this.isRunningCodebaseProcess) {
			this.initialize();
		}
	}

	public async stopCodebaseProcess() {
		this.channel.call('stopRemoteCodebase');
		this.isRunningCodebaseProcess = false;
	}

	public initialize() {
		const currentSettings = this._codeseekSettingsService.state.settingsOfCodebase[this.workspaceUri!.fsPath]?.[CodebaseModes.Remote];
		if (!this.isFirstInit) {
			if (currentSettings?.status === 'indexing') {
				this.logger.info('already initializing, skip duplicate call');
				return;
			}
		}
		this.isFirstInit = false;
		this._codeseekSettingsService.setSettingsOfCodebase(this.workspaceUri!, {
			[CodebaseModes.Remote]: {
				...currentSettings,
				status: 'indexing',
			}
		} as SettingsOfCodebase);

		if (!this._codeseekSettingsService.state.globalSettings.enableRemoteCodeBase) {
			this.logger.info('remote codebase is not enabled, skip initialize');
			return;
		}
		if (!this.workspaceUri) {
			this.logger.info('workspaceUri is not set, skip initialize');
			return;
		}
		if (this.workspaceUri.scheme !== 'file') {
			this.logger.info('workspaceUri is not a file, skip initialize');
			return;
		}
		this.pendingFileChanges.clear();
		this.logger.info(`the codebase of repo: ${this.workspaceUri.fsPath} start initialize`);
		this.ensureIndex({
			repoUri: this.workspaceUri,
			options: {
				retryIfLastAttemptFailed: true,
				ignoreExisting: false
			},
			onProgress: (p) => {
				this._codeseekSettingsService.setSettingsOfCodebase(this.workspaceUri!, {
					[CodebaseModes.Remote]: {
						process: p.progress,
						syncedFileNumber: p.SyncedFileNumber,
						status: p.progress === 1 ? 'completed' : 'indexing',
					}
				} as SettingsOfCodebase);
			},
			useProviderFor: FeatureNames.CtrlL,
		});
	}

	public async search(params: searchParams): Promise<ISearchResult[]> {
		if (!this._codeseekSettingsService.state.globalSettings.enableRemoteCodeBase) {
			this.logger.info('remote codebase is not enabled, skip search');
			return [];
		}
		const currentSettings = this._codeseekSettingsService.state.settingsOfCodebase[this.workspaceUri!.fsPath]?.[CodebaseModes.Remote];
		if (currentSettings?.status !== 'completed') {
			this.logger.info('codebase is not completed, skip search');
			return [];
		}
		try {
			const queryResults: CodeContextItem[] | undefined = await this.getResults(params);
			if (!queryResults || queryResults.length === 0) {
				return [];
			}
			const searchResults: ISearchResult[] = queryResults.map((item) => {
				const fullFilePath = path.join(this.workspaceUri!.fsPath, item.relativePath);
				const uri = URI.file(fullFilePath);
				return {
					uri,
					content: item.content,
					range: item.range
				}
			});

			if (this._codeseekSettingsService.state.globalSettings.enableCodeBaseConvertFile) {
				const alreadyConvertedFile: string[] = [];
				for (const searchResult of searchResults) {
					if (alreadyConvertedFile.includes(searchResult.uri.fsPath)) {
						continue
					}
					const content = await this._codeseekFileService.readFile(searchResult.uri) ?? '';
					searchResult.content = content;
					alreadyConvertedFile.push(searchResult.uri.fsPath);
				}
			}
			return searchResults;
		} catch (error) {
			this.logger.error(`search failed: ${error instanceof Error ? error.message : String(error)}`);
			return [];
		}
	}

	private async ensureIndex(params: EnsureIndexParams): Promise<void> {
		const { useProviderFor: featureName } = params;
		this.codebaseHooks.onProgress[params.repoUri.fsPath] = params.onProgress;
		try {
			const modelSelection = this._codeseekSettingsService.getModelSelectionForContainer(featureName!, CODESEEK_VIEW_CONTAINER_ID);
			if (!modelSelection) {
				return;
			}

			const { providerName, modelName } = modelSelection;
			const clangd: ClangdConfigData | undefined = this.getClangdConfig();
			const provider = this._codeseekSettingsService.state.settingsOfProvider[providerName];
			const baseUrl = (!provider.baseURL || provider.baseURL === '') ? provider.endpoint : provider.baseURL;
			const projectStructure = await this._codeseekFileService.getProjectStructure(Infinity);
			const llm: LLmConfigData = {
				provider: providerName,
				model: modelName,
				baseUrl: baseUrl ?? '',
				apiKey: provider.apiKey,
			};

			const params_: EnsureIndexParams & CreateRepoConfig = {
				...params,
				clangd,
				llm,
				projectStructure
			};

			await this.channel.call('ensureRemoteIndex', params_);
		} catch (error) {
			this.logger.error(`索引创建失败: ${error instanceof Error ? error.message : String(error)}`);
		}
	}

	private async updateIndex(params: UpdateIndexParams): Promise<boolean> {
		try {
			this.logger.info(`Updating index for file: ${params.fileUri.fsPath}`);
			const isUpdateSuccess: boolean = await this.channel.call('updateRemoteIndex', params);
			this.logger.info(`Index updated successfully for file: ${params.fileUri.fsPath}`);
			return isUpdateSuccess;
		} catch (error) {
			this.logger.error(`索引更新异常: ${error instanceof Error ? error.message : String(error)}`);
			return false;
		}
	}

	private async deleteIndex(params: DeleteIndexParams, isResetProcess: boolean = true): Promise<boolean> {
		this.stopPeriodicUpdate();
		this.pendingFileChanges.clear();
		const currentSettings = this._codeseekSettingsService.state.settingsOfCodebase[this.workspaceUri!.fsPath]?.[CodebaseModes.Remote];
		if (currentSettings?.status === 'idle') {
			return true;
		}
		const isDeleteSuccess: boolean = await this.channel.call('deleteRemoteIndex', params);
		if (isDeleteSuccess) {
			this._codeseekSettingsService.setSettingsOfCodebase(this.workspaceUri!, {
				[CodebaseModes.Remote]: {
					process: isResetProcess ? undefined : 0,
					syncedFileNumber: 0,
					status: 'idle',
				}
			} as SettingsOfCodebase);
		}
		return isDeleteSuccess;
	}

	private async getResults(params: searchParams): Promise<CodeContextItem[] | undefined> {
		const queryResults: QueryContextData | undefined = await this.channel.call('getRemoteResults', params);
		this.logger.info(`getResults, queryResults: ${JSON.stringify(queryResults, null, 2)}`);
		if (queryResults) {
			const validItems: CodeContextItem[] = [];
			for (const item of queryResults.codeContextItems) {
				const fullFilePath = path.join(this.workspaceUri!.fsPath, item.relativePath);
				const fileUri = URI.file(fullFilePath);
				try {
					const exists = await this._fileService.exists(fileUri);
					if (!exists) {
						continue;
					}
					if (!item.content) {
						continue;
					}

					if (this.pendingFileChanges.has(fullFilePath)) {
						const change = this.pendingFileChanges.get(fullFilePath)!;
						if (change.status === 'update') {
							item.content = await this._codeseekFileService.readFile(fileUri, {
								startLineNumber: item.range.startLineNumber,
								endLineNumber: item.range.endLineNumber,
							});
						}
					}
					validItems.push(item);
				} catch (error) {
					this.logger.error(`处理查询结果时出错: ${error instanceof Error ? error.message : String(error)}`);
				}
			}
			return validItems;
		}
		return undefined;
	}

	private async pauseRemoteIndex(params: PauseRemoteIndexParams): Promise<boolean> {
		const currentSettings = this._codeseekSettingsService.state.settingsOfCodebase[this.workspaceUri!.fsPath]?.[CodebaseModes.Remote];
		if (currentSettings?.status === 'paused') {
			return true;
		}
		try {
			const isPauseSuccess: boolean = await this.channel.call('pauseRemoteIndex', params);
			this._codeseekSettingsService.setSettingsOfCodebase(this.workspaceUri!, {
				[CodebaseModes.Remote]: {
					...currentSettings,
					status: 'paused',
				}
			} as SettingsOfCodebase);
			return isPauseSuccess;
		} catch (error) {
			this.logger.error(`索引暂停失败: ${error instanceof Error ? error.message : String(error)}`);
			return false;
		}
	}

	private async resumeRemoteIndex(params: ResumeRemoteIndexParams): Promise<boolean> {
		const currentSettings = this._codeseekSettingsService.state.settingsOfCodebase[this.workspaceUri!.fsPath]?.[CodebaseModes.Remote];
		if (currentSettings?.status === 'indexing') {
			return true;
		}
		try {
			const isResumeSuccess: boolean = await this.channel.call('resumeRemoteIndex', params);
			this._codeseekSettingsService.setSettingsOfCodebase(this.workspaceUri!, {
				[CodebaseModes.Remote]: {
					...currentSettings,
					status: 'indexing',
				}
			} as SettingsOfCodebase);
			return isResumeSuccess;
		} catch (error) {
			this.logger.error(`索引恢复失败: ${error instanceof Error ? error.message : String(error)}`);
			return false;
		}
	}

	private getClangdConfig(): ClangdConfigData | undefined {
		const clangdCPath = this._configurationService.getValue<string>('clangd.path');
		const clangdArguments: string[] = this._configurationService.getValue<string[]>('clangd.arguments');
		if (!clangdCPath || !clangdArguments) return undefined;
		const compileCommandsDir = clangdArguments.find(arg => arg.startsWith('--compile-commands-dir'));
		if (!compileCommandsDir) return undefined;
		const parts = compileCommandsDir.split('=');
		const compileCommandsDirPath = parts.length > 1 ? parts[1] : '';
		if (!compileCommandsDirPath) return undefined;
		return {
			path: clangdCPath,
			compileCommandsDir: compileCommandsDirPath,
		}
	}
}

registerSingleton(ICodebaseRemoteService, CodebaseRemoteService, InstantiationType.Delayed);
