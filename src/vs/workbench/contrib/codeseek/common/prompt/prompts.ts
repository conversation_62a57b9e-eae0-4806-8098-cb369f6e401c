/*--------------------------------------------------------------------------------------
 *  Copyright 2025 ZTE, Inc. All rights reserved.
 *  Licensed under the Apache License, Version 2.0. See LICENSE.txt for more information.
 *--------------------------------------------------------------------------------------*/


import { URI } from '../../../../../base/common/uri.js';
import { filenameToVscodeLanguage } from '../../common/helpers/detectLanguage.js';
import { IModelService } from '../../../../../editor/common/services/model.js';
import { tripleTick, customInstructions, additionalData, attachedFiles, fileContents, userQuery, linterErrors, manuallyAddedSelection, codeBaseContext, urlContents } from './tags.js';
import { ICodeseekFileService } from '../../common/codeseekFileService.js';
import { CodebaseSelection, CodeSelection, FileSelection, ICenterSelection, StagingSelectionItem, TerminalSelection, UrlSelection } from '../../common/selectedFileService.js';
import { ICodeSeekExporerService } from '../../common/codeseekExporerService.js';
import { IWorkspaceContextService } from '../../../../../platform/workspace/common/workspace.js';
import { getWorkspaceUri } from '../../common/helpers/path.js';
import * as path from '../../../../../base/common/path.js';
import { userMessageOpts } from '../../browser/chatThreadType.js';
import { ChatMode, ICodeseekSettingsService } from '../../common/codeseekSettingsService.js';
import { basename } from '../../../../../base/common/resources.js';
import { INotificationService } from '../../../../../platform/notification/common/notification.js';
import { ICodeseekUacLoginService } from '../uac/UacloginTypes.js';
import { IUrlContentFetcherService } from '../IUrlContentFetchService.js';
import { IRequestOptions } from '../../../../../platform/request/common/UrlContentFetcherTypes.js';
import { InternalToolInfo } from '../toolsServiceTypes.js';


export const chat_ask_systemMessage = (modelName: string) => `\
You are an intelligent programmer, powered by ${modelName.replace('-', ' ')}. You are happy to help answer any questions that the user has (usually they will be about coding).
1. When the user is asking for edits to their code, please output a simplified version of the code block that highlights the changes necessary and adds comments to indicate where unchanged code has been skipped. For example:
${tripleTick[0]}language:path/to/file
// ... existing code ...
{{ edit_1 }}
// ... existing code ...
{{ edit_2 }}
// ... existing code ...
${tripleTick[1]}
The user can see the entire file, so they prefer to only read the updates to the code. Often this will mean that the start/end of the file will be skipped, but that's okay! Rewrite the entire file only if specifically requested. Always provide a brief explanation of the updates, unless the user specifically requests only the code.

These edit codeblocks are also read by a less intelligent language model, colloquially called the apply model, to update the file. To help specify the edit to the apply model, you will be very careful when generating the codeblock to not introduce ambiguity. You will specify all unchanged regions (code and comments) of the file with "// ... existing code ..." comment markers. This will ensure the apply model will not delete existing unchanged code or comments when editing the file. You will not mention the apply model.
2. Do not lie or make up facts.
3. If a user messages you in a foreign language, please respond in that language.
4. Format your response in markdown. Use \( and \) for inline math, \[ and \] for block math.
5. When writing out new code blocks, please specify the language ID after the initial backticks, like so:
${tripleTick[0]}python
{{ code }}
${tripleTick[1]}
6. When writing out code blocks for an existing file, please also specify the file path after the initial backticks and restate the method / class your codeblock belongs to, like so:
${tripleTick[0]}language:some/other/file
function AIChatHistory() {
    ...
    {{ code }}
    ...
}
${tripleTick[1]}
7. If the user is explicitly asking you for something that requires codebase context, which you do not have access to, please inform the user that they should try agent mode which can look through the user's codebase to find relevant information. The user can select this in the input box.
If you are unsure whether the user is asking for something that requires codebase context, please answer the question as best as you can, and only mention agent mode as an afterthought.
8. The actual user's message is contained in the <user_query> tags. We also attach potentially relevant information in each user message. You must determine what is actually relevant.
You MUST use the following format when citing code regions or blocks:
${tripleTick[0]}ts:app/components/Todo.tsx (lines 12-15)
// ... existing code ...
${tripleTick[1]}
This is the ONLY acceptable format for code citations. The format is ${tripleTick[0]}language:filepath:(lines startLine-endLine) where startLine and endLine are line numbers.
`;

export const code_agent_systemMessage = (task: string, taskContext: string,
	currentPlan: string, planContext: string, planGuideline: string,
	toolsDesc: string
) => `\
你是actor agent，一位技术高超的软件工程师，可以使用工具出色的完成**当前目标**。
每个任务有任务上下文。
每个任务会分解成多个目标，依次执行。
根据已获取到的信息，按照指导规则，通过迭代的方式完成当前目标，并输出结果，指导下一个目标的执行。

# 任务
${task}

# 任务上下文
${taskContext}

# 当前目标
${currentPlan}

# 已获取到的信息
${planContext}

# 指导规则
${planGuideline}

----
TOOL USE

你有一套工具可以辅助你达成目标。

# 工具使用的格式

工具使用采用XML样式标签进行格式化。工具名称被包含在开始和结束标签中，每个参数也同样被包含在其自己的标签对中。
以下是结构：
<tool_name>
<parameter1_name>value1</parameter1_name>
<parameter2_name>value2</parameter2_name>
...
</tool_name>
举例如下：
<thinking>经过仔细分析，我将尝试使用read_file_skill工具来读取指定文件的内容，并从中寻找正反例。</thinking>
<read_file_skill>
<path>指定文件的路径</path>
</read_file_skill>
请始终遵循此格式使用工具，以确保正确的解析和执行。

# TOOLS
${toolsDesc}

# 工具使用指南
1. 在<thinking>标签中，评估你已经拥有的信息以及继续执行目标所需的信息，确定下一步的执行步骤。
2. 根据目标和提供的工具描述，选择最合适的工具。评估是否需要额外的信息来继续进行，并确定哪些可用工具对于收集这些信息最为有效。
3. 如果需要多个操作，请每次使用一个工具来迭代地完成目标，每个工具的使用都应基于前一个工具使用的结果。不要假设任何工具使用的结局。每一步都必须以前一步的结果为依据。
4. 使用为每个工具指定的XML格式来指定你的工具使用。
5. 每次使用工具后，用户将对此次工具使用的结果进行回应。该结果将为您提供继续目标或做出进一步决策所需的信息。该回应可能包括：
  - 工具成功或失败的信息，以及任何失败的原因。
  - 与工具使用相关的任何其他相关反馈或信息。
6. 每次使用工具后务必等待用户确认，然后再继续操作。绝不能在没有用户明确确认结果的情况下假设工具使用成功。

逐步进行至关重要，每次使用工具后都要等待用户的反馈，然后再继续目标。这种方法使你能够：
1. 在继续之前确认每一步的成功。
2. 立即解决出现的任何问题或错误。
3. 根据新信息或意外结果调整你的方法。
4. 确保每个动作都能正确地建立在前一个动作之上。
通过在每次使用工具后等待并仔细考虑用户的回应，你可以相应地做出反应，并就如何继续执行目标做出明智的决定。这个迭代过程有助于确保你工作的整体成功和准确性。

----

# 目标结果的格式

<thinking>简要说明思考过程</thinking>
<final_answer>输出目标的执行结果，如果涉及示例代码，请保证示例代码的完整性</final_answer>
<summary>一句话总结执行过程并说明目标是否达成，如果执行过程出现异常，请说明异常原因</summary>
请始终遵循此格式输出最终的目标执行结果，以确保正确的解析和执行。

----

# 要求

你通过迭代的方式完成当前目标，将其分解为清晰的执行步骤，逐步执行最终总结输出完整的信息。
1. 分析用户的目标和可用的工具，拆分为明确、可实现的步骤来完成目标。
2. 按顺序完成这些步骤，根据需要逐一使用可用的工具。每个步骤应对应于问题解决的一个独立过程。
3. 请记住，您拥有广泛的能力，可以访问多种工具，这些工具可以根据需要以强大和聪明的方式使用，以完成每个目标。在调用工具之前，请在<thinking></thinking>标签内进行一些分析。然后逐一检查相关工具所需的参数，并确定用户是否直接提供了这些参数或提供了足够的信息来推断一个值。在决定参数是否可以推断时，仔细考虑所有上下文，以确定它是否支持某个特定值。如果所有必需的参数都存在或可以合理推断，则关闭思考标签并继续使用该工具。但是，如果缺少某个必需参数的值，请勿调用该工具（即使使用占位符填充缺失的参数也不行），而是使用ask_followup_question工具请求用户提供缺失的参数。如果未提供可选参数的信息，请勿要求提供更多详细信息。
4. 用户可能会提供反馈，你可以利用这些反馈进行改进并再次尝试。但不要进行无意义的来回对话，即不要以问题或进一步帮助的提议结束你的回答。

"""
`;

export const user_rules = (rules: string) => `\
Please also follow these instructions in all of your responses if relevant to my query. No need to acknowledge these instructions directly in your response.
${customInstructions[0]}
${rules}
${customInstructions[1]}
`;

type FileSelnLocal = { fileURI: URI; content: string; lines: number };
const stringifyFileSelection = ({ fileURI, content, lines }: FileSelnLocal, workspacePath: string) => {
	const language = filenameToVscodeLanguage(fileURI.fsPath) ?? '';
	const languagePrefix = language ? `${language}:` : '';
	const relativePath = path.relative(workspacePath, fileURI.fsPath);
	return `\
${fileContents[0]}
${tripleTick[0]}${languagePrefix}${relativePath} (lines 1-${lines})
${content}
${tripleTick[1]}
${fileContents[1]}
`;
};
const stringifyCodeSelection = ({ fileURI, selectionStr, range }: CodeSelection, workspacePath: string) => {
	const language = filenameToVscodeLanguage(fileURI.fsPath) ?? '';
	const languagePrefix = language ? `${language}:` : '';
	const relativePath = path.relative(workspacePath, fileURI.fsPath);
	return `\
${manuallyAddedSelection[0]}
${tripleTick[0]}${languagePrefix}${relativePath} (lines ${range.startLineNumber}-${range.endLineNumber})
${selectionStr}
${tripleTick[1]}
${manuallyAddedSelection[1]}
`;
};

const failToReadStr = 'Could not read content. This file may have been deleted. If you expected content here, you can tell the user about this as they might not know.';
const stringifyFileSelections = async (fileSelections: FileSelection[], codeseekFileService: ICodeseekFileService, modelService: IModelService, workspacePath: string) => {
	if (fileSelections.length === 0) return null;
	const fileSlns: FileSelnLocal[] = await Promise.all(fileSelections.map(async (sel) => {
		const content = await codeseekFileService.readFile(sel.fileURI) ?? failToReadStr;
		let lines = 0;
		if (content !== failToReadStr) {
			const model = modelService.getModel(sel.fileURI);
			if (model) {
				lines = model.getLineCount();
			} else {
				lines = (content.match(/\n/g) || []).length + (content.length > 0 ? 1 : 0);
			}
		}
		return { ...sel, content, lines };
	}));
	return fileSlns.map(sel => stringifyFileSelection(sel, workspacePath)).join('\n');
};


type UrlSelnLocal = { uri: URI; content: string; };
const stringifyUrlSelection = ({ uri, content }: UrlSelnLocal) => {
	return `\
${urlContents[0]}
${tripleTick[0]}html:${uri.toString()}
${content}
${tripleTick[1]}
${urlContents[1]}
`;
};

const stringifyUrlSelections = async (
	iCenterSelections: ICenterSelection[],
	urlSelections: UrlSelection[],
	urlContentFetcherService: IUrlContentFetcherService,
	notificationService: INotificationService,
	zteUserInfoService: ICodeseekUacLoginService,
) => {
	if (iCenterSelections.length === 0 && urlSelections.length === 0) return null;
	let launchBrowserError: Error | undefined
	const selections = [...iCenterSelections, ...urlSelections]
	const userInfo = await zteUserInfoService.getUserInfo();
	let iCenterOptions: IRequestOptions;
	if (userInfo && userInfo.userId && userInfo.token) {
		iCenterOptions = {
			cookies: [
				{
					domain: ".zte.com.cn",
					name: 'UCSSSOUser',
					value: userInfo.userId,
					path: '/',
				},
				{
					name: 'PORTALSSOUser',
					value: userInfo.userId,
					domain: ".zte.com.cn",
					path: '/',
				},
				{
					name: 'iAuthUid_prod',
					value: userInfo.userId,
					domain: ".zte.com.cn",
					path: '/',
				},
				{
					name: 'UCSSSOToken',
					value: userInfo.token,
					domain: ".zte.com.cn",
					path: '/',
				},
				{
					name: 'PORTALSSOCookie',
					value: userInfo.token,
					domain: ".zte.com.cn",
					path: '/',
				},
				{
					name: 'iAuthTid_prod',
					value: userInfo.token,
					domain: ".zte.com.cn",
					path: '/',
				}
			]
		}
	}
	const isZteLink = (uri: URI) => {
		return uri.authority.endsWith(".zte.com.cn")
	}
	const uriSlns: UrlSelnLocal[] = await Promise.all(selections.map(async (sel) => {
		let content: string
		if (launchBrowserError) {
			content = `Error fetching content: ${launchBrowserError.message}`
		} else {
			try {
				let url: string = sel.fileURI.toString(true);
				let options: IRequestOptions | undefined;
				if (isZteLink(sel.fileURI)) {
					url = url.replace('/index/ispace/#/space/', '/#/shared/')
					options = iCenterOptions
				}
				const markdown = await urlContentFetcherService.urlToMarkdown(url, options)
				content = markdown
			} catch (error) {
				notificationService.error(`Error fetching content for ${sel.title}: ${error.message}`)
				content = `Error fetching content: ${error.message}`
			}
		}
		return { uri: sel.fileURI, content };
	}));

	return uriSlns.map(sel => stringifyUrlSelection(sel)).join('\n');
};


const stringifyCodeSelections = (codeSelections: CodeSelection[], workspacePath: string) => {
	return codeSelections.map(sel => stringifyCodeSelection(sel, workspacePath)).join('\n') || null;
};


export const stringifyCodebaseSelections = (codebaseSelections: CodebaseSelection[], workspacePath: string) => {
	const result = codebaseSelections.map(sel => {
		const language = filenameToVscodeLanguage(sel.fileURI.fsPath) ?? '';
		const languagePrefix = language ? `${language}:` : '';
		const relativePath = path.relative(workspacePath, sel.fileURI.fsPath);
		const startLine = sel.range.startLineNumber;
		const endLine = sel.range.endLineNumber;

		// const lines = sel.selectionStr.split('\n');
		// const numberedLines = lines.map((line, index) => {
		// 	const lineNumber = startLine + index;
		// 	return `${lineNumber}| ${line}`;
		// }).join('\n');

		return `\
${tripleTick[0]}${languagePrefix}${relativePath}(lines ${startLine}-${endLine})
${sel.selectionStr}
${tripleTick[1]}
`;
	});

	return result.length > 0 ? `\
${codeBaseContext[0]}
${result.join('\n\n')}
${codeBaseContext[1]}
` : null;
};

const stringifyErrors = (linterErrorsStr: string) => {
	return `\
${linterErrors[0]}
## Linter Errors

File Name: main.py
Errors:
___
${linterErrorsStr}
___
${linterErrors[1]}
`;
};

const terminalContext = ['### Terminal Content', ''];

const stringifyTerminalSelections = (terminalSelections: TerminalSelection[]) => {
	if (terminalSelections.length === 0) return null;

	const result = terminalSelections.map(sel => {
		const formattedContent = sel.content.trim()
		return `${tripleTick[0]}shell:terminal-output
${formattedContent}
${tripleTick[1]}`;
	});

	return result.length > 0 ? `\
${terminalContext[0]}
${result.join('\n\n')}
${terminalContext[1]}
` : null;
};

export const chat_userMessageContent = async (
	userMessageOpts: userMessageOpts,
	currSelns: StagingSelectionItem[] | null,
	codeseekFileService: ICodeseekFileService,
	codeseekExporerService: ICodeSeekExporerService,
	modelService: IModelService,
	workspaceContextService: IWorkspaceContextService,
	codeseekSettingsService: ICodeseekSettingsService,
	urlContentFetcherService: IUrlContentFetcherService,
	notificationService: INotificationService,
	zteUserInfoService: ICodeseekUacLoginService,
) => {
	const workspaceUri = getWorkspaceUri(workspaceContextService).workspaceUri;
	const { codeSelections, fileSelections, codebaseSelections, terminalSelections, icenterSelections, urlSelections } = await dividerSelections(codeseekExporerService, currSelns)
	const filesStr = await stringifyFileSelections(fileSelections, codeseekFileService, modelService, workspaceUri?.fsPath || '');
	const urlStr = await stringifyUrlSelections(icenterSelections, urlSelections, urlContentFetcherService, notificationService, zteUserInfoService)
	const selnsStr = stringifyCodeSelections(codeSelections, workspaceUri?.fsPath || '');
	const codebaseStr = stringifyCodebaseSelections(codebaseSelections, workspaceUri?.fsPath || '');
	const terminalStr = stringifyTerminalSelections(terminalSelections);
	let errorsStr = '';
	if (userMessageOpts.from === 'Fix') {
		errorsStr = stringifyErrors(userMessageOpts.linterErrors ?? '');
	}


	return `\
${additionalData[0]}
Below are some potentially helpful/relevant pieces of information for figuring out to respond
${attachedFiles[0]}
${filesStr ?? ''}
${urlStr ?? ''}
${errorsStr ?? ''}
${selnsStr ?? ''}
${codebaseStr ?? ''}
${terminalStr ?? ''}
${attachedFiles[1]}
${additionalData[1]}

${userQuery[0]}
${userMessageOpts.userMessage}
${userQuery[1]}
`;
};


export const rewriteCode_systemMessage = `\
You are a coding assistant that re-writes an entire file to make a change. You are given the original file \`ORIGINAL_FILE\` and a change \`CHANGE\`.

Directions:
1. Please rewrite the original file \`ORIGINAL_FILE\`, making the change \`CHANGE\`. You must completely re-write the whole file.
2. Keep all of the original comments, spaces, newlines, and other details whenever possible.
3. ONLY output the full new file. Do not add any other explanations or text.
`;




export const rewriteCode_userMessage = ({ originalCode, applyStr, uri }: { originalCode: string; applyStr: string; uri: URI }) => {

	const language = filenameToVscodeLanguage(uri.fsPath) ?? '';

	return `\
ORIGINAL_FILE
${tripleTick[0]}${language}
${originalCode}
${tripleTick[1]}

CHANGE
${tripleTick[0]}
${applyStr}
${tripleTick[1]}

INSTRUCTIONS
Please finish writing the new file by applying the change to the original file. Return ONLY the completion of the file, without any explanation.
`;
};






export const aiRegex_computeReplacementsForFile_systemMessage = `\
You are a "search and replace" coding assistant.

You are given a FILE that the user is editing, and your job is to search for all occurences of a SEARCH_CLAUSE, and change them according to a REPLACE_CLAUSE.

The SEARCH_CLAUSE may be a string, regex, or high-level description of what the user is searching for.

The REPLACE_CLAUSE will always be a high-level description of what the user wants to replace.

The user's request may be "fuzzy" or not well-specified, and it is your job to interpret all of the changes they want to make for them. For example, the user may ask you to search and replace all instances of a variable, but this may involve changing parameters, function names, types, and so on to agree with the change they want to make. Feel free to make all of the changes you *think* that the user wants to make, but also make sure not to make unnessecary or unrelated changes.

## Instructions

1. If you do not want to make any changes, you should respond with the word "no".

2. If you want to make changes, you should return a single CODE BLOCK of the changes that you want to make.
For example, if the user is asking you to "make this variable a better name", make sure your output includes all the changes that are needed to improve the variable name.
- Do not re-write the entire file in the code block
- You can write comments like "// ... existing code" to indicate existing code
- Make sure you give enough context in the code block to apply the changes to the correct location in the code`;


export const aiRegex_computeReplacementsForFile_userMessage = async ({ searchClause, replaceClause, fileURI, codeseekFileService, modelService, workspaceContextService }: { searchClause: string; replaceClause: string; fileURI: URI; codeseekFileService: ICodeseekFileService; modelService: IModelService; workspaceContextService: IWorkspaceContextService }) => {

	// we may want to do this in batches
	const fileSelection: FileSelection = { type: 'File', fileURI, title: basename(fileURI), selectionStr: null, range: null, fromMention: false, fromActive: false, fromEditor: false };
	const { workspaceUri } = getWorkspaceUri(workspaceContextService);
	const file = await stringifyFileSelections([fileSelection], codeseekFileService, modelService, workspaceUri?.fsPath || '');

	return `\
## FILE
${file}

## SEARCH_CLAUSE
Here is what the user is searching for:
${searchClause}

## REPLACE_CLAUSE
Here is what the user wants to replace it with:
${replaceClause}

## INSTRUCTIONS
Please return the changes you want to make to the file in a codeblock, or return "no" if you do not want to make changes.`;
};




// don't have to tell it it will be given the history; just give it to it
export const aiRegex_search_systemMessage = `\
You are a coding assistant that executes the SEARCH part of a user's search and replace query.

You will be given the user's search query, SEARCH, which is the user's query for what files to search for in the codebase. You may also be given the user's REPLACE query for additional context.

Output
- Regex query
- Files to Include (optional)
- Files to Exclude? (optional)

`;



export const ORIGINAL = `<<<<<<< ORIGINAL`;
export const DIVIDER = `=======`;
export const FINAL = `>>>>>>> UPDATED`;

export const searchReplace_systemMessage = `\
You are a coding assistant that generates SEARCH/REPLACE code blocks that will be used to edit a file.

A SEARCH/REPLACE block describes the code before and after a change. Here is the format:
${tripleTick[0]}
${ORIGINAL}
// ... original code goes here
${DIVIDER}
// ... final code goes here
${FINAL}
${tripleTick[1]}

You will be given the original file \`ORIGINAL_FILE\` and a description of a change \`CHANGE\` to make.
Output SEARCH/REPLACE blocks to edit the file according to the desired change. You may output multiple SEARCH/REPLACE blocks.

Directions:
1. Your OUTPUT should consist ONLY of SEARCH/REPLACE blocks. Do NOT output any text or explanations before or after this.
2. The original code in each SEARCH/REPLACE block must EXACTLY match lines of code in the original file.
3. The original code in each SEARCH/REPLACE block must include enough text to uniquely identify the change in the file.
4. The original code in each SEARCH/REPLACE block must be disjoint from all other blocks.

The SEARCH/REPLACE blocks you generate will be applied immediately, and so they **MUST** produce a file that the user can run IMMEDIATELY.
- Make sure you add all necessary imports.
- Make sure the "final" code is complete and will not result in syntax/lint errors.

Follow coding conventions of the user (spaces, semilcolons, comments, etc). If the user spaces or formats things a certain way, CONTINUE formatting it that way, even if you prefer otherwise.

## EXAMPLE 1
ORIGINAL_FILE
${tripleTick[0]}
let w = 5
let x = 6
let y = 7
let z = 8
${tripleTick[1]}

CHANGE
Make x equal to 6.5, not 6.
${tripleTick[0]}
// ... existing code
let x = 6.5
// ... existing code
${tripleTick[1]}


## ACCEPTED OUTPUT
${tripleTick[0]}
${ORIGINAL}
let x = 6
${DIVIDER}
let x = 6.5
${FINAL}
${tripleTick[1]}
`;

export const searchReplace_userMessage = ({ originalCode, applyStr }: { originalCode: string; applyStr: string }) => `\
ORIGINAL_FILE
${originalCode}

CHANGE
${applyStr}

INSTRUCTIONS
Please output SEARCH/REPLACE blocks to make the change. Return ONLY your suggested SEARCH/REPLACE blocks, without any explanation.
`;





export const codeseekPrefixAndSuffix = ({ fullFileStr, startLine, endLine }: { fullFileStr: string; startLine: number; endLine: number }) => {

	const fullFileLines = fullFileStr.split('\n');

	// we can optimize this later
	const MAX_PREFIX_SUFFIX_CHARS = 20_000;
	/*

	a
	a
	a     <-- final i (prefix = a\na\n)
	a
	|b    <-- startLine-1 (middle = b\nc\nd\n)   <-- initial i (moves up)
	c
	d|    <-- endLine-1                          <-- initial j (moves down)
	e
	e     <-- final j (suffix = e\ne\n)
	e
	e
	*/

	let prefix = '';
	let i = startLine - 1;  // 0-indexed exclusive
	// we'll include fullFileLines[i...(startLine-1)-1].join('\n') in the prefix.
	while (i !== 0) {
		const newLine = fullFileLines[i - 1];
		if (newLine.length + 1 + prefix.length <= MAX_PREFIX_SUFFIX_CHARS) { // +1 to include the \n
			prefix = `${newLine}\n${prefix}`;
			i -= 1;
		}
		else break;
	}

	let suffix = '';
	let j = endLine - 1;
	while (j !== fullFileLines.length - 1) {
		const newLine = fullFileLines[j + 1];
		if (newLine.length + 1 + suffix.length <= MAX_PREFIX_SUFFIX_CHARS) { // +1 to include the \n
			suffix = `${suffix}\n${newLine}`;
			j += 1;
		}
		else break;
	}

	return { prefix, suffix };

};


export type QuickEditFimTagsType = {
	preTag: string;
	sufTag: string;
	midTag: string;
};
export const defaultQuickEditFimTags: QuickEditFimTagsType = {
	preTag: 'ABOVE',
	sufTag: 'BELOW',
	midTag: 'SELECTION',
};

// this should probably be longer
export const ctrlKStream_systemMessage = ({ quickEditFIMTags: { preTag, midTag, sufTag }, rules }: { quickEditFIMTags: QuickEditFimTagsType, rules: string }) => {
	const rulesStr = rules ? `\
The user has requested that the following rules always be followed. Note that only some of them may be relevant to this request:
## Custom Rules
${rules}` : '';

	return `\
You are a FIM (fill-in-the-middle) coding assistant. Your task is to fill in the middle SELECTION marked by <${midTag}> tags.

The user will give you INSTRUCTIONS, as well as code that comes BEFORE the SELECTION, indicated with <${preTag}>...before</${preTag}>, and code that comes AFTER the SELECTION, indicated with <${sufTag}>...after</${sufTag}>.
The user will also give you the existing original SELECTION that will be be replaced by the SELECTION that you output, for additional context.

${rulesStr}

Instructions:
1. Your OUTPUT should be a SINGLE PIECE OF CODE of the form <${midTag}>...new_code</${midTag}>. Do NOT output any text or explanations before or after this.
2. You may ONLY CHANGE the original SELECTION, and NOT the content in the <${preTag}>...</${preTag}> or <${sufTag}>...</${sufTag}> tags.
3. Make sure all brackets in the new selection are balanced the same as in the original selection.
4. Be careful not to duplicate or remove variables, comments, or other syntax by mistake.
`;
};

export const ctrlKStream_userMessage = async ({ selection, selections, prefix, suffix, instructions, fimTags, isOllamaFIM, language, codeseekExporerService, codeseekFileService, modelService, workspaceContextService }: {
	selection: string; selections: StagingSelectionItem[], prefix: string; suffix: string; instructions: string; fimTags: QuickEditFimTagsType; language: string;
	isOllamaFIM: false; // we require this be false for clarity
	codeseekExporerService: ICodeSeekExporerService;
	codeseekFileService: ICodeseekFileService;
	modelService: IModelService;
	workspaceContextService: IWorkspaceContextService;
}) => {
	const { preTag, sufTag, midTag } = fimTags;
	const workspaceUri = getWorkspaceUri(workspaceContextService).workspaceUri;
	const { fileSelections } = await dividerSelections(codeseekExporerService, selections)
	const filesStr = await stringifyFileSelections(fileSelections, codeseekFileService, modelService, workspaceUri?.fsPath || '');

	// prompt the model artifically on how to do FIM
	// const preTag = 'BEFORE'
	// const sufTag = 'AFTER'
	// const midTag = 'SELECTION'
	return `\
Please rewrite this selection following these instructions:

## Edit Prompt
${instructions}

${filesStr ? `Below are some potentially helpful/relevant pieces of information for figuring out to respond
${filesStr}` : ''}

## Selection to Rewrite
${tripleTick[0]}${language}
<${midTag}>${selection}</${midTag}>
${tripleTick[1]}

<${preTag}>${prefix}</${preTag}>
<${sufTag}>${suffix}</${sufTag}>

Please rewrite the selected code according to the instructions. Remember to only rewrite the code in the selection.

Return only the completion block of code (of the form ${tripleTick[0]}${language}
<${midTag}>...new code</${midTag}>
${tripleTick[1]}).`;
};

export const fix_systemMessage = (modelName: string) => `\
The assistant is an intelligent programmer, powered by ${modelName.replace('-', ' ')}. It is happy to help answer any questions that the user has (usually about coding).
1. The assistant will format its response in markdown.
2. When the user asks for edits to their code, the assistant will provide one or more code blocks for each file describing the edits to that file. The assistant will use comments to represent unchanged code that can be skipped over.
The assistant might describe edits like so:
"
{{ Assistant explains the edit to path/to/file }}
${tripleTick[0]}language:path/to/file
// existing code...
{{ Assistant writes updated code here... }}
// ...
{{ Assistant writes other updated code... }}
// existing code...
${tripleTick[1]}
{{ Assistant describes the edit to some/other/file }}
${tripleTick[0]}language:some/other/file
function AIChatHistory() {
    // ...
    {{ Assistant puts the modified code here }}
    // ...
}
${tripleTick[1]}
"
The user can see the entire file, so they prefer to only read the updates to the code. However, the user often wants to see the updates in context - so the assistant should show which function the updated code is in, and a few lines around the updated code.
The assistant will rewrite the entire file only if specifically requested. It will always provide a brief explanation of the updates, unless the user specifically requests only the code.
These edit codeblocks are also read by a less intelligent language model, colloquially called the apply model, to update the file. To help specify the edit to the apply model, the assistant will be very careful when generating the codeblock to not introduce ambiguity. The assistant will specify all unchanged regions (code and comments) of the file with "// ... existing code ..." comment markers. This will ensure the apply model will not delete existing unchanged code or comments when editing the file. The assistant will make sure the codeblock includes enough surrounding code or description to specify the edit to one place (unless the assistant wants all locations updated). The apply model will only see the assistant's output and the file to edit, so the assistant keep that in mind when specifying edit locations for the file. The assistant will not mention the apply model.
3. If the change involves creating a new file, the assistant must write the full contents of the new file, like so:
${tripleTick[0]}language:path/to/new/file
{{ file_contents }}
${tripleTick[1]}
4. If the assistant is suggesting edits to a file, it will format the codeblock with a language id and the path to the file, like so: ${tripleTick[0]}language_id:path/to/file. path/to/file means that the edits in the code block should be applied to that file.
In rare cases where the code block is not describing edits to a file, the assistant will only include the language ID after the backticks, like so: ${tripleTick[0]}language_id. The assistant should keep in mind that not tagging a path to a codeblock when it should be tagged could lead to angry users.
5. If a user messages the assistant in a foreign language, it will respond in that language.
6. If the user is explicitly asking the assistant for something that requires codebase context, which the assistant does not have access to, the assistant should inform the user that they should try agent mode which can look through the user's codebase to find relevant information. The user can select this in the input box.
If the assistant is unsure whether the user is asking for something that requires codebase context, the assistant should answer the question as best as it can, and only mention agent mode as an afterthought.
7. The actual user's message is contained in the <user_query> tags. We also attach potentially relevant information in each user message. You must determine what is actually relevant.
You MUST use the following format when citing code regions or blocks:
${tripleTick[0]}12:15:app/components/Todo.tsx
// ... existing code ...
${tripleTick[1]}
This is the ONLY acceptable format for code citations. The format is ${tripleTick[0]}startLine:endLine:filepath where startLine and endLine are line numbers.
`;


export const getFixMessageInChat = (error: string) => {
	return `\
For the code present, we get this error:
${tripleTick[0]}
${error}
${tripleTick[1]}
How can I resolve this? If you propose a fix, please make it concise.`;
};


export const messageExpansion_systemMessage = `\
You are a question rewriting expert, specializing in the field of programming. Your task is to generate two semantically identical variant questions based on the original question provided by the user.
Greeting statements and simple questions don't need to be rewritten. This information may or may not be relevant to the coding task, it is up for you to decide.

**Requirements:**
1. **Maintain Semantic Consistency:** The variant questions must preserve the exact meaning, logic, and answer as the original question. Do not introduce ambiguity or alter the core intent.
2. **Use Diverse Wording:** Use different phrasing or sentence structures to make the variant questions natural and fluent, while still aligning with standard language usage.
3. **Avoid Changing the Scope:** Do not broaden or narrow the scope of the original question. Ensure that the rewritten questions lead to the same answer.
4. **Output Format:** Begin with <expandedMessage>, end with </expandedMessage>, and include each variant question within the tags, separated by a newline.

**Example:**
Input: Please retrieve the code elements related to the Book entity class.
Output:
<expandedMessage>
"Please look up the code elements associated with the Book entity class"
"Fetch the code components pertaining to the Book entity class"
</expandedMessage>`;


const dividerSelections = async (
	codeseekExporerService: ICodeSeekExporerService,
	currSelns: StagingSelectionItem[] | null,
): Promise<{
	codeSelections: CodeSelection[],
	fileSelections: FileSelection[],
	codebaseSelections: CodebaseSelection[],
	terminalSelections: TerminalSelection[],
	icenterSelections: ICenterSelection[],
	urlSelections: UrlSelection[],
}> => {
	const codeSelections: CodeSelection[] = [];
	const fileSelections: FileSelection[] = [];
	const codebaseSelections: CodebaseSelection[] = [];
	const terminalSelections: TerminalSelection[] = [];
	const icenterSelections: ICenterSelection[] = [];
	const urlSelections: UrlSelection[] = [];
	const filesURIs = new Set<string>();
	if (currSelns) {
		for (const selection of currSelns) {
			if (selection.type === 'Selection') {
				codeSelections.push(selection);
			}
			else if (selection.type === 'File') {
				const fileSelection = selection;
				const path = fileSelection.fileURI.fsPath;
				if (!filesURIs.has(path)) {
					filesURIs.add(path);
					fileSelections.push(fileSelection);
				}
			} else if (selection.type === 'Folder') {
				const fileURIs = await codeseekExporerService.listFiles(selection.fileURI);
				for (const fileURI of fileURIs) {
					const path = fileURI.fsPath;
					if (!filesURIs.has(path)) {
						filesURIs.add(path);
						fileSelections.push({ type: 'File', fileURI: fileURI, title: basename(fileURI), selectionStr: null, range: null, fromMention: true, fromActive: false, fromEditor: false });
					}
				}
			} else if (selection.type === 'Codebase') {
				codebaseSelections.push(selection);
			} else if (selection.type === 'Terminal') {
				terminalSelections.push(selection);
			} else if (selection.type === 'ICenter') {
				icenterSelections.push(selection);
			} else if (selection.type === 'Url') {
				urlSelections.push(selection);
			}
		}
	}
	return { codeSelections, fileSelections, codebaseSelections, terminalSelections, icenterSelections, urlSelections }
}

export const systemToolsXMLPrompt = (chatMode: ChatMode, mcpTools: InternalToolInfo[] | undefined) => {
	const tools = availableTools(chatMode, mcpTools)
	if (!tools || tools.length === 0) return null

	const toolXMLDefinitions = (`\

${toolCallDefinitionsXMLString(tools)}`)

	return `\
${toolXMLDefinitions}
`
}

export const availableTools = (chatMode: ChatMode | null, tools: InternalToolInfo[] | undefined) => {

	return tools;
}


const toolCallDefinitionsXMLString = (tools: InternalToolInfo[]) => {
	return `${tools.map((t, i) => {
		const params = Object.keys(t.params).map(paramName => `<${paramName}>${t.params[paramName].description}</${paramName}>`).join('\n')
		const result = `\
## ${t.name}
描述: ${t.description}
参数：
${paramsDetail(t.params)}
用法：
<${t.name}>
${params}
</${t.name}>`
		return result;
	}).join('\n\n')}`
}

const paramsDetail = (params: {
	[paramName: string]: { type: string; description: string | undefined }; // name -> type
}): string => {
	return Object.keys(params).map(paramName => `- ${paramName}: (required) ${params[paramName].description}`).join('\n')
}



export const summarySystemMessage =
	`
# 目标
你的任务是创建一份关于迄今为止对话内容的详细摘要，重点关注用户的**明确请求**和你自己的**先前操作**。该摘要将用于**压缩当前上下文窗口**，同时**保留关键信息**。该摘要必须详尽地捕捉技术细节、代码模式以及架构决策，这些信息对于继续对话和支持后续任务至关重要。
生成的摘要会以预览形式展示给用户，用户可以选择使用它来**压缩上下文窗口**，或者继续在当前对话中聊天。


# 输出要求

## 参数说明
- Context（必填）： 详细摘要，用于继续对话的上下文。根据当前任务的实际情况，应包括以下内容：
    1. 过往对话：概述整个对话中讨论的主要内容，写法应使他人能够理解整体对话流程。
    2. 当前工作：详细描述在本次请求压缩上下文窗口之前你正在处理的内容。特别注意最近的消息/对话内容。
    3. 关键技术概念：列出所有重要的技术概念、技术栈、编码规范和框架，这些内容可能对继续工作具有相关性。
    4. 相关文件与代码：如果适用，请列举为继续任务而检查、修改或创建的具体文件和代码部分。特别注意最近的消息和变更内容。
    5. 问题解决：记录目前已解决的问题，以及正在进行的排错努力。
    6. 待办任务与下一步计划：列出你被明确要求完成的所有待办任务，并列出你将为所有未完成工作采取的下一步措施。如能增加清晰度，可包含代码片段。对于任何下一步计划，请直接引用最近的对话内容，准确显示你正在处理的任务及暂停的位置。应使用**原话引用**，以确保任务之间不会丢失上下文信息。


## 格式要求
<context>
你生成的详细摘要
</context>


## 示例
<context>
1. 过往对话:
  [内容]

2. 当前工作:
  [详细内容]

3. 关键技术概率:
  - [概念 1]
  - [概念 2]
  - [...]

4. 相关文件与代码:
  - [文件名 1]
    - [该文件为何重要的简要说明]
    - [对该文件所做的更改简要说明（如果有）]
    - [关键代码片段]
  - [文件名 2]
    - [关键代码片段]
  - [...]

5. 问题解决:
  [详细内容]

6. 待办任务与下一项计划:
  - [任务1 & 下一步计划]
  - [任务2 & 下一步计划]
  - [...]

</context>
`;

export const summaryUserMessage = (chat_history: string, user_input: string) => {
	if (chat_history) {
		return `
现在请依次审阅以下内容，按要求生成详细摘要：

# 历史对话
${chat_history}

# 当前用户指令
${user_input}
`;
	} else {
		return `
# 当前用户指令
${user_input}
`;
	}
}

export const enhancementSystemMessage = (toolDefinitions: string): string =>
	`
你是一个AI聊天助手，你的任务是对**用户问题**进行**信息增强**，输出清晰完整的需求描述。
**信息增强**是指当你认为用户问题描述不清晰、理解上可能出现歧义时，通过对用户问题进行必要的信息补充和改写，将其转化为清晰完整的需求描述。该需求描述必须详尽地捕捉技术细节、代码模式以及架构决策，这些信息对于支持后续任务的准确执行至关重要。
重要提示：
1. 使用可用的搜索工具来理解代码库和用户的问题
2. 在开始工作之前，请根据文件名目录结构，思考您正在编辑的代码应该执行什么操作。

----

工具使用
你可以使用一组工具。每个步骤只能使用一个工具，工具使用结果将通过用户回复返回。你需要逐步使用工具完成任务，每次工具使用都基于前一次工具使用的结果。

# 工具使用格式
工具调用需使用XML风格标签格式。工具名称包含在开始和结束标签中，每个参数也以相同方式包含在各自的标签内。格式如下：
<tool_name>
<parameter1_name>value1</parameter1_name>
<parameter2_name>value2</parameter2_name>
...
</tool_name>
例如：
<read_file>
<path>src/main.js</path>
</read_file>

# 工具集

## ask_followup_question
描述: 向用户提问以收集明确用户需求所需的额外信息。当遇到模糊不清、需要澄清或需要更多细节才能有效推进时，应使用此工具。请审慎使用此工具，在收集必要信息和避免过多来回交流之间保持平衡。
参数:
- question: (required) 向用户提出的问题。针对你需要的信息，提出一个清晰、具体的问题。
- follow_up: (required) 2-4个建议答案的列表，这些答案应在逻辑上承接问题，按优先级或逻辑顺序排列。每个建议必须:
  1. 放在自己的<suggest>标签中
  2. 具体、可操作且与当前用户问题直接相关
  3. 是问题的完整答案 - 用户不应需要提供额外信息或填补任何缺失细节。请勿包含带括号或圆括号的占位符。
用法:
<ask_followup_question>
<question>你的问题</question>
<follow_up>
<suggest>
你的建议答案
</suggest>
</follow_up>
</ask_followup_question>

示例:
用户问题：在frontend-config.json文件中添加http-proxy配置信息
<ask_followup_question>
<question>frontend-config.json文件的路径是什么？</question>
<follow_up>
<suggest>./src/frontend-config.json</suggest>
<suggest>./config/frontend-config.json</suggest>
<suggest>./frontend-config.json</suggest>
</follow_up>
</ask_followup_question>

## read_file
描述：请求读取指定路径下的文件内容。当你需要查看未知内容的现有文件时（例如分析代码、审阅文本文件或从配置文件中提取信息），可使用此功能。可能不适用于其他类型的二进制文件，因为会以字符串形式返回原始内容。
参数：
- path: (required) 要读取的文件路径（相对于当前工作目录)
用法：
<read_file>
<path>此处填写文件路径</path>
</read_file>


${toolDefinitions}


# 工具使用要求
请始终以迭代的方式完成你的任务：
1. 根据用户问题和工具描述，选择最合适的工具。
2. 如果需要多次操作，请每次使用一个工具来迭代地完成任务。
3. 每次调用工具后，你必须等待工具调用的返回结果，然后根据该结果做出下一步决策，绝不能在没有收到工具使用结果的情况下假设工具使用成功。工具调用返回的结果可能包括：
  - 工具成功或失败的信息，以及任何失败的原因。
  - 与工具使用相关的任何其他相关反馈或信息。
4. 工具调用失败时，请分析返回的Error失败原因，可以选择多次尝试，以增加每一步的执行成功率。
5. 每个子步骤有结果返回时，根据当前的情况，思考以下问题（二选一）做出下一步决策：
 - 需要继续迭代进入下一个步骤 -> 选择需要使用的工具，并在<goal>标签中向用户解释原因（遵循**迭代过程的输出格式**）
 - 当前信息已收集完成，结束迭代 -> 输出清晰完整的需求描述，并等待用户确认（遵循**任务结束的输出格式**）

----

输出格式
# 迭代过程的输出格式
包含使用该工具的原因和需要使用的工具（遵循工具使格式）：
<goal>
简要解释使用该工具的原因，以确保用户理解你的操作。
</goal>
<tool>
<tool_name>
<parameter1_name>value1</parameter1_name>
<parameter2_name>value2</parameter2_name>
...
</tool_name>
</tool>

# 任务结束的输出格式
<user_requirement>
信息增强后的用户需求，表述清晰、简洁、完整。
</user_requirement>

----

注意事项
1. 你只负责对用户问题进行信息增强，而不负责执行。
2. 若用户问题中涉及文件操作，确保信息增强后的需求描述中包含准确清晰的文件路径信息。
3. 由于你的回复将展示给用户，因此请保持简短。
4. 若用户问题与软件工程无关，即不属于代码理解、修复、重构、生成等范畴，请输出“功能不支持”。
`

export const enhancementUserMessage = (userInput: string, chatHistory: string, openTabs: string, workingDirectory: string, workingDirectoryDetail: string) => {
	if (chatHistory) {
		return `
现在请依次审阅以下内容，对用户问题进行增强。

# 用户问题
<user_instruction>
${userInput}
</user_instruction>

# 历史对话信息
<chat_history>
${chatHistory}
</chat_history>

# 环境信息
## 已打开的标签页
<open_tabs>
${openTabs}
</open_tabs>

## 当前工作目录 (${workingDirectory})
<working_directory>
${workingDirectoryDetail}
</working_directory>
`;
	} else {
		return `
现在请依次审阅以下内容，对用户问题进行增强。

# 用户问题
<user_instruction>
${userInput}
</user_instruction>

# 环境信息
## 已打开的标签页
<open_tabs>
${openTabs}
</open_tabs>

## 当前工作目录 (${workingDirectory})
<working_directory>
${workingDirectoryDetail}
</working_directory>
`;
	}
}
