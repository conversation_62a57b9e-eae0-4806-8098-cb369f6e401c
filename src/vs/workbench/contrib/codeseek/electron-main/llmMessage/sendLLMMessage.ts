/*--------------------------------------------------------------------------------------
 *  Copyright 2025 ZTE, Inc. All rights reserved.
 *  Licensed under the Apache License, Version 2.0. See LICENSE.txt for more information.
 *--------------------------------------------------------------------------------------*/

import { SendLLMMessageParams, OnText, OnFinalMessage, OnError } from '../../common/llmMessageTypes.js';
import { IMetricsService } from '../../common/metricsService.js';
import { displayInfoOfProviderName } from '../../common/codeseekSettingsTypes.js';
import { sendLLMMessageToProviderImplementation } from './llmProxy.js';
import { ICodeseekLogger } from '../../common/codeseekLogService.js';
import { env, IdeTestConfig } from '../utils/common.js';
import { XMLParser } from 'fast-xml-parser';
import { ToolCallType } from '../../common/toolsServiceTypes.js';

/**
 * Sends a language model (LLM) message with support for different message types and providers.
 *
 * @param params Configuration parameters for sending an LLM message, including message type, content, and callbacks
 * @param metricsService Service for capturing metrics about the LLM message interaction
 * @returns A promise that resolves when the message sending process is complete
 *
 * @remarks
 * Supports chat messages, agent messages, and Fill-in-the-Middle (FIM) messages across different providers.
 * Handles message streaming, tool calls, error handling, and provides abort functionality.
 */
export const sendLLMMessage = ({
	requestId,
	messagesType,
	codeseekSettingsState,
	messages: messages_,
	onText: onText_,
	onFinalMessage: onFinalMessage_,
	onError: onError_,
	abortRef: abortRef_,
	logging: { loggingName },
	providerName,
	modelName,
	llmChannel,
}: SendLLMMessageParams,
	metricsService: IMetricsService,
	logger: ICodeseekLogger
) => {
	if (env.AI_IDE_TEST_CONFIG) {
		const config: IdeTestConfig = JSON.parse(env.AI_IDE_TEST_CONFIG);
		if (config.llm) {
			if (config.llm.provider) {
				providerName = config.llm.provider;
			}
			if (config.llm.baseURL) {
				codeseekSettingsState.settingsOfProvider[providerName].baseURL = config.llm.baseURL;
			}
			if (config.llm.modelName) {
				modelName = config.llm.modelName;
			}
			if (config.llm.apiKey) {
				codeseekSettingsState.settingsOfProvider[providerName].apiKey = config.llm.apiKey;
			}
		}
	}

	let _aborter: (() => void) | null = null;
	const _setAborter = (fn: () => void) => { _aborter = fn; };
	let _didAbort = false;

	const onText: OnText = (params) => {
		if (_didAbort) return;
		onText_(params);
	};

	const onFinalMessage: OnFinalMessage = ({ fullText, toolCalls }) => {
		if (_didAbort) return;
		const xmlObj = parseXml(fullText);
		const newFullText = removeToolTags(fullText);
		const tool = extractTool(xmlObj);

		onFinalMessage_({ fullText: newFullText, toolCalls: tool });
	};

	const onError: OnError = ({ message: error, fullError }) => {
		if (_didAbort) return;

		if (error === 'TypeError: fetch failed')
			error = `Failed to fetch from ${displayInfoOfProviderName(providerName).title}. This likely means you specified the wrong endpoint in Flow Settings, or your local model provider like Ollama is powered off.`;
		onError_({ message: error, fullError });
	};

	const onAbort = () => {
		try { _aborter?.(); }
		catch (e) { }
		_didAbort = true;
	};
	abortRef_.current = onAbort;

	try {
		const implementation = sendLLMMessageToProviderImplementation[providerName as keyof typeof sendLLMMessageToProviderImplementation];
		if (!implementation) {
			onError({ message: `Error: Provider "${providerName}" not recognized.`, fullError: null });
			return;
		}
		const { sendChat } = implementation;
		const sendFIM = implementation.sendFIM as unknown as ((params: any) => void) | null;
		if (messagesType === 'chatMessages') {
			sendChat({ messages: Array.isArray(messages_) ? messages_ : [], logger, onText, onFinalMessage, onError, codeseekSettingsState, modelName, _setAborter, providerName });
			return;
		}
		if (messagesType === 'FIMMessage') {
			if (sendFIM) {
				sendFIM({ messages: !Array.isArray(messages_) ? messages_ : { prefix: '', suffix: '' }, onText, onFinalMessage, onError, codeseekSettingsState, modelName, _setAborter, providerName });
				return;
			}
			logger.error(`Error: This provider does not support Autocomplete yet.`);
			onError({ message: `Error: This provider does not support Autocomplete yet.`, fullError: null });
			return;
		}
		logger.error(`Error: Message type "${messagesType}" not recognized.`);
		onError({ message: `Error: Message type "${messagesType}" not recognized.`, fullError: null });
	} catch (error) {
		if (error instanceof Error) { onError({ message: error + '', fullError: error }); }
		else { onError({ message: `Unexpected Error in sendLLMMessage: ${error}`, fullError: error }); }
		// ; (_aborter as any)?.()
		// _didAbort = true
	}
};


export const parseXml = (data: string): Record<string, any> => {
	const parser = new XMLParser();
	return parser.parse(data);
}

export const removeToolTags = (data: string): string => {
	return data
		.replace(/<tool\b[^>]*>[\s\S]*?<\/tool>/gi, '')
		.replace(/<goal\b[^>]*>[\s\S]*?<\/goal>/gi, '');
}

interface ToolType {
	[toolName: string]: { [param: string]: string };
}

export const extractTool = (xmlObj: Record<string, any>): ToolCallType[] => {
	if (xmlObj.tool) {
		const tool = xmlObj.tool as ToolType;
		const toolName = Object.keys(tool)[0];
		const params = tool[toolName];

		// Map params to the correct ToolCallParamsType shape based on toolName
		let mappedParams: any = params;
		switch (toolName) {
			case 'file_read':
			case 'file_write':
			case 'file_delete':
			case 'file_create':
				mappedParams = { path: params.path };
				break;
			case 'file_edit':
				mappedParams = {
					path: params.path,
					content: params.content,
					start: Number(params.start),
					end: Number(params.end)
				};
				break;
			case 'search':
			case 'search_code':
				mappedParams = { query: params.query };
				break;
			case 'terminal_run':
				mappedParams = {
					content: params.content,
					command: params.command
				};
				break;
			// Add more cases as needed for other tool types
			default:
				mappedParams = params;
		}

		return [{
			name: toolName as ToolCallType['name'],
			params: mappedParams,
			id: toolName,
		}]
	} else {
		return []
	}
}
