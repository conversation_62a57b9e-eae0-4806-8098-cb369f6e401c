/*--------------------------------------------------------------------------------------
 *  Copyright 2025 ZTE, Inc. All rights reserved.
 *  Licensed under the Apache License, Version 2.0. See LICENSE.txt for more information.
 *--------------------------------------------------------------------------------------*/

import { SendLLMMessageParams, OnText, OnFinalMessage, OnError } from '../../common/llmMessageTypes.js';
import { IMetricsService } from '../../common/metricsService.js';
import { displayInfoOfProviderName } from '../../common/codeseekSettingsTypes.js';
import { sendLLMMessageToProviderImplementation } from './llmProxy.js';
import { ICodeseekLogger } from '../../common/codeseekLogService.js';
import { env, IdeTestConfig } from '../utils/common.js';
import { XMLParser } from 'fast-xml-parser';
import { ToolCallType, ToolNameEnum } from '../../common/toolsServiceTypes.js';

/**
 * Sends a language model (LLM) message with support for different message types and providers.
 *
 * @param params Configuration parameters for sending an LLM message, including message type, content, and callbacks
 * @param metricsService Service for capturing metrics about the LLM message interaction
 * @returns A promise that resolves when the message sending process is complete
 *
 * @remarks
 * Supports chat messages, agent messages, and Fill-in-the-Middle (FIM) messages across different providers.
 * Handles message streaming, tool calls, error handling, and provides abort functionality.
 */
export const sendLLMMessage = ({
	requestId,
	messagesType,
	codeseekSettingsState,
	messages: messages_,
	onText: onText_,
	onFinalMessage: onFinalMessage_,
	onError: onError_,
	abortRef: abortRef_,
	logging: { loggingName },
	providerName,
	modelName,
	llmChannel,
}: SendLLMMessageParams,
	metricsService: IMetricsService,
	logger: ICodeseekLogger
) => {
	if (env.AI_IDE_TEST_CONFIG) {
		const config: IdeTestConfig = JSON.parse(env.AI_IDE_TEST_CONFIG);
		if (config.llm) {
			if (config.llm.provider) {
				providerName = config.llm.provider;
			}
			if (config.llm.baseURL) {
				codeseekSettingsState.settingsOfProvider[providerName].baseURL = config.llm.baseURL;
			}
			if (config.llm.modelName) {
				modelName = config.llm.modelName;
			}
			if (config.llm.apiKey) {
				codeseekSettingsState.settingsOfProvider[providerName].apiKey = config.llm.apiKey;
			}
		}
	}

	let _aborter: (() => void) | null = null;
	const _setAborter = (fn: () => void) => { _aborter = fn; };
	let _didAbort = false;

	const onText: OnText = (params) => {
		if (_didAbort) return;
		onText_(params);
	};

	const onFinalMessage: OnFinalMessage = ({ fullText, toolCalls }) => {
		if (_didAbort) return;
		const newFullText = removeToolTags(fullText);
		const tool = extractTool(fullText);

		onFinalMessage_({ fullText: newFullText, toolCalls: tool });
	};

	const onError: OnError = ({ message: error, fullError }) => {
		if (_didAbort) return;

		if (error === 'TypeError: fetch failed')
			error = `Failed to fetch from ${displayInfoOfProviderName(providerName).title}. This likely means you specified the wrong endpoint in Flow Settings, or your local model provider like Ollama is powered off.`;
		onError_({ message: error, fullError });
	};

	const onAbort = () => {
		try { _aborter?.(); }
		catch (e) { }
		_didAbort = true;
	};
	abortRef_.current = onAbort;

	try {
		const implementation = sendLLMMessageToProviderImplementation[providerName as keyof typeof sendLLMMessageToProviderImplementation];
		if (!implementation) {
			onError({ message: `Error: Provider "${providerName}" not recognized.`, fullError: null });
			return;
		}
		const { sendChat } = implementation;
		const sendFIM = implementation.sendFIM as unknown as ((params: any) => void) | null;
		if (messagesType === 'chatMessages') {
			sendChat({ messages: Array.isArray(messages_) ? messages_ : [], logger, onText, onFinalMessage, onError, codeseekSettingsState, modelName, _setAborter, providerName });
			return;
		}
		if (messagesType === 'FIMMessage') {
			if (sendFIM) {
				sendFIM({ messages: !Array.isArray(messages_) ? messages_ : { prefix: '', suffix: '' }, onText, onFinalMessage, onError, codeseekSettingsState, modelName, _setAborter, providerName });
				return;
			}
			logger.error(`Error: This provider does not support Autocomplete yet.`);
			onError({ message: `Error: This provider does not support Autocomplete yet.`, fullError: null });
			return;
		}
		logger.error(`Error: Message type "${messagesType}" not recognized.`);
		onError({ message: `Error: Message type "${messagesType}" not recognized.`, fullError: null });
	} catch (error) {
		if (error instanceof Error) { onError({ message: error + '', fullError: error }); }
		else { onError({ message: `Unexpected Error in sendLLMMessage: ${error}`, fullError: error }); }
		// ; (_aborter as any)?.()
		// _didAbort = true
	}
};


export const parseXml = (data: string): Record<string, any> => {
	const parser = new XMLParser();
	return parser.parse(data);
}

export const removeToolTags = (data: string): string => {
	return data
		.replace(/<tool\b[^>]*>[\s\S]*?<\/tool>/gi, '')
		.replace(/<goal\b[^>]*>[\s\S]*?<\/goal>/gi, '');
}

export const extractTool = (text: string): ToolCallType[] => {
	const toolCalls: ToolCallType[] = [];

	// 创建所有工具名称的正则表达式模式
	const toolNames = Object.values(ToolNameEnum);
	const toolNamesPattern = toolNames.join('|');

	// 匹配工具调用的正则表达式：<tool_name>...</tool_name>
	const toolCallRegex = new RegExp(`<(${toolNamesPattern})>([\\s\\S]*?)<\\/\\1>`, 'gi');

	let match;
	while ((match = toolCallRegex.exec(text)) !== null) {
		const toolName = match[1] as ToolNameEnum;
		const toolContent = match[2];

		// 提取参数
		const params = extractParameters(toolContent);

		// 根据工具名称映射参数到正确的结构
		const mappedParams = mapToolParameters(toolName, params);

		toolCalls.push({
			name: toolName,
			params: mappedParams,
			id: `${toolName}_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
		});
	}

	return toolCalls;
};

// 提取XML标签内的参数
const extractParameters = (content: string): Record<string, string> => {
	const params: Record<string, string> = {};

	// 匹配参数标签：<param_name>value</param_name>
	const paramRegex = /<([^>]+)>([\s\S]*?)<\/\1>/g;

	let match;
	while ((match = paramRegex.exec(content)) !== null) {
		const paramName = match[1].trim();
		const paramValue = match[2].trim();
		params[paramName] = paramValue;
	}

	return params;
};

// 根据工具名称映射参数到正确的ToolCallParamsType结构
const mapToolParameters = (toolName: ToolNameEnum, params: Record<string, string>): any => {
	switch (toolName) {
		case ToolNameEnum.READ_FILE:
			return { path: params.uri || params.path || '' };

		case ToolNameEnum.LIST_FILES:
			return { path: params.uri || params.path || '' };

		case ToolNameEnum.PATHNAME_SEARCH:
			return { query: params.query || '' };

		case ToolNameEnum.SEARCH:
			return { query: params.query || '' };

		case ToolNameEnum.CREATE_FILE:
			return { path: params.uri || params.path || '' };

		case ToolNameEnum.UPDATE_FILE:
			return {
				path: params.uri || params.path || '',
				content: params.content || '',
				start: Number(params.start) || 0,
				end: Number(params.end) || 0
			};

		case ToolNameEnum.APPROVE_REQUEST:
			return {
				content: params.content || '',
				command: params.command || ''
			};

		case ToolNameEnum.ASK_FOLLOWUP_QUESTION:
			return { question: params.question || '' };

		case ToolNameEnum.CTAGS_QUERY:
			return { symbol: params.symbol || '' };

		case ToolNameEnum.CLANGD_QUERY:
			return {
				filePath: params.filePath || '',
				line: Number(params.line) || 0,
				character: Number(params.character) || 0
			};

		case ToolNameEnum.SHOW_SUMMARY:
			return {
				summary: params.summary || '',
				detail: params.detail || ''
			};

		case ToolNameEnum.CODEBASE_SEARCH:
			return { query: params.query || '' };

		default:
			// 对于未知工具，返回原始参数
			return params;
	}
};
