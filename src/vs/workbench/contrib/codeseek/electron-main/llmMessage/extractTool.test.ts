/*--------------------------------------------------------------------------------------
 *  Copyright 2025 ZTE, Inc. All rights reserved.
 *  Licensed under the Apache License, Version 2.0. See LICENSE.txt for more information.
 *--------------------------------------------------------------------------------------*/

import { extractTool } from './sendLLMMessage.js';
import { ToolNameEnum } from '../../common/toolsServiceTypes.js';

describe('extractTool', () => {
	test('should extract single tool call with parameters', () => {
		const text = `
Some text before
<read_file>
<uri>src/main.js</uri>
</read_file>
Some text after
		`;

		const result = extractTool(text);
		
		expect(result).toHaveLength(1);
		expect(result[0].name).toBe(ToolNameEnum.READ_FILE);
		expect(result[0].params).toEqual({ path: 'src/main.js' });
		expect(result[0].id).toMatch(/^read_file_\d+_[a-z0-9]+$/);
	});

	test('should extract multiple tool calls', () => {
		const text = `
<read_file>
<uri>file1.js</uri>
</read_file>

<search>
<query>function test</query>
</search>
		`;

		const result = extractTool(text);
		
		expect(result).toHaveLength(2);
		expect(result[0].name).toBe(ToolNameEnum.READ_FILE);
		expect(result[0].params).toEqual({ path: 'file1.js' });
		expect(result[1].name).toBe(ToolNameEnum.SEARCH);
		expect(result[1].params).toEqual({ query: 'function test' });
	});

	test('should handle tool with multiple parameters', () => {
		const text = `
<update_to_file>
<uri>test.js</uri>
<content>console.log('hello');</content>
<start>1</start>
<end>10</end>
</update_to_file>
		`;

		const result = extractTool(text);
		
		expect(result).toHaveLength(1);
		expect(result[0].name).toBe(ToolNameEnum.UPDATE_TO_FILE);
		expect(result[0].params).toEqual({
			path: 'test.js',
			content: "console.log('hello');",
			start: 1,
			end: 10
		});
	});

	test('should handle clangd_query with numeric parameters', () => {
		const text = `
<clangd_query>
<filePath>src/main.cpp</filePath>
<line>42</line>
<character>15</character>
</clangd_query>
		`;

		const result = extractTool(text);
		
		expect(result).toHaveLength(1);
		expect(result[0].name).toBe(ToolNameEnum.CLANGD_QUERY);
		expect(result[0].params).toEqual({
			filePath: 'src/main.cpp',
			line: 42,
			character: 15
		});
	});

	test('should return empty array when no tools found', () => {
		const text = `
Just some regular text without any tool calls.
<not_a_tool>
<param>value</param>
</not_a_tool>
		`;

		const result = extractTool(text);
		
		expect(result).toHaveLength(0);
	});

	test('should handle nested XML content in parameters', () => {
		const text = `
<create_file>
<uri>test.xml</uri>
<content><root><child>value</child></root></content>
</create_file>
		`;

		const result = extractTool(text);
		
		expect(result).toHaveLength(1);
		expect(result[0].name).toBe(ToolNameEnum.CREATE_FILE);
		expect(result[0].params).toEqual({ path: 'test.xml' });
	});

	test('should handle whitespace in parameters', () => {
		const text = `
<search>
<query>  function with spaces  </query>
</search>
		`;

		const result = extractTool(text);
		
		expect(result).toHaveLength(1);
		expect(result[0].name).toBe(ToolNameEnum.SEARCH);
		expect(result[0].params).toEqual({ query: 'function with spaces' });
	});

	test('should handle case insensitive tool names', () => {
		const text = `
<READ_FILE>
<uri>test.js</uri>
</READ_FILE>
		`;

		const result = extractTool(text);
		
		expect(result).toHaveLength(1);
		expect(result[0].name).toBe(ToolNameEnum.READ_FILE);
	});

	test('should handle show_summary tool', () => {
		const text = `
<show_summary>
<summary>Test Summary</summary>
<detail>Detailed information here</detail>
</show_summary>
		`;

		const result = extractTool(text);
		
		expect(result).toHaveLength(1);
		expect(result[0].name).toBe(ToolNameEnum.SHOW_SUMMARY);
		expect(result[0].params).toEqual({
			summary: 'Test Summary',
			detail: 'Detailed information here'
		});
	});
});
