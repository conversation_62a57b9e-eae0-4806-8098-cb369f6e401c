/*--------------------------------------------------------------------------------------
 *  Copyright 2025 ZTE, Inc. All rights reserved.
 *  Licensed under the Apache License, Version 2.0. See LICENSE.txt for more information.
 *--------------------------------------------------------------------------------------*/

// registered in app.ts
// code convention is to make a service responsible for this stuff, and not a channel, but having fewer files is simpler...

import { IServerChannel } from '../../../../../base/parts/ipc/common/ipc.js';
import { Emitter, Event } from '../../../../../base/common/event.js';
import {
	EventLLMMessageOnTextParams, EventLLMMessageOnErrorParams, EventLLMMessageOnFinalMessageParams,
	MainSendLLMMessageParams, AbortRef, SendLLMMessageParams, MainLLMMessageAbortParams, ModelListParams,
	EventModelListOnSuccessParams, EventModelListOnErrorParams, OllamaModelResponse, VLLMModelResponse,
	MainModelListParams
} from '../../common/llmMessageTypes.js';
import { sendLLMMessage } from '../llmMessage/sendLLMMessage.js';
import { IMetricsService } from '../../common/metricsService.js';
import { sendLLMMessageToProviderImplementation, setRestfulApiService } from '../llmMessage/llmProxy.js';
import { ICodeseekLogger } from '../../common/codeseekLogService.js';
import { IRestfulApiMainService } from '../restfulApiMainService.js';

export class LLMMessageChannel implements IServerChannel {
	// sendLLMMessage
	private readonly llmMessageEmitters = {
		onText: new Emitter<EventLLMMessageOnTextParams>(),
		onFinalMessage: new Emitter<EventLLMMessageOnFinalMessageParams>(),
		onError: new Emitter<EventLLMMessageOnErrorParams>(),
	};

	// aborters for above
	private readonly abortRefOfRequestId: Record<string, AbortRef> = {};

	// list
	private readonly listEmitters = {
		ollama: {
			success: new Emitter<EventModelListOnSuccessParams<OllamaModelResponse>>(),
			error: new Emitter<EventModelListOnErrorParams<OllamaModelResponse>>(),
		},
		vLLM: {
			success: new Emitter<EventModelListOnSuccessParams<VLLMModelResponse>>(),
			error: new Emitter<EventModelListOnErrorParams<VLLMModelResponse>>(),
		}
	} satisfies {
		[providerName: string]: {
			success: Emitter<EventModelListOnSuccessParams<any>>;
			error: Emitter<EventModelListOnErrorParams<any>>;
		};
	};

	// stupidly, channels can't take in @IService
	constructor(
		private readonly metricsService: IMetricsService,
		private readonly logger: ICodeseekLogger,
		private readonly restfulApiMainService: IRestfulApiMainService,
	) {
		// 初始化时设置 restfulApiService 到 llmProxy 模块
		setRestfulApiService(this.restfulApiMainService);
	}

	// browser uses this to listen for changes
	listen(_: unknown, event: string): Event<any> {
		// text
		if (event === 'onText_sendLLMMessage') return this.llmMessageEmitters.onText.event;
		else if (event === 'onFinalMessage_sendLLMMessage') return this.llmMessageEmitters.onFinalMessage.event;
		else if (event === 'onError_sendLLMMessage') return this.llmMessageEmitters.onError.event;
		// list
		else if (event === 'onSuccess_list_ollama') return this.listEmitters.ollama.success.event;
		else if (event === 'onError_list_ollama') return this.listEmitters.ollama.error.event;
		else if (event === 'onSuccess_list_vLLM') return this.listEmitters.vLLM.success.event;
		else if (event === 'onError_list_vLLM') return this.listEmitters.vLLM.error.event;

		else throw new Error(`Event not found: ${event}`);
	}

	// browser uses this to call (see this.channel.call() in llmMessageService.ts for all usages)
	async call(_: unknown, command: string, params: any): Promise<any> {
		try {
			if (command === 'sendLLMMessage') {
				this._callSendLLMMessage(params);
			}
			else if (command === 'abort') {
				this._callAbort(params);
			}
			else if (command === 'ollamaList') {
				this._callOllamaList(params);
			}
			else if (command === 'vLLMList') {
				this._callVLLMList(params);
			}
			else {
				throw new Error(`Codeseek sendLLM: command "${command}" not recognized.`);
			}
		}
		catch (e) {
			this.logger.error('llmMessageChannel: Call Error:', e);
		}
	}

	// the only place sendLLMMessage is actually called
	private _callSendLLMMessage(params: MainSendLLMMessageParams) {
		const { requestId } = params;

		if (!(requestId in this.abortRefOfRequestId))
			this.abortRefOfRequestId[requestId] = { current: null };

		const mainThreadParams: SendLLMMessageParams = {
			...params,
			onText: (p) => { this.llmMessageEmitters.onText.fire({ requestId, ...p }); },
			onFinalMessage: (p) => { this.llmMessageEmitters.onFinalMessage.fire({ requestId, ...p }); },
			onError: (p) => { this.llmMessageEmitters.onError.fire({ requestId, ...p }); },
			abortRef: this.abortRefOfRequestId[requestId],
			llmChannel: this,
		};
		sendLLMMessage(mainThreadParams, this.metricsService, this.logger);
	}

	_callOllamaList = (params: MainModelListParams<OllamaModelResponse>) => {
		const { requestId } = params;
		const emitters = this.listEmitters.ollama;
		const mainThreadParams: ModelListParams<OllamaModelResponse> = {
			...params,
			onSuccess: (p) => { emitters.success.fire({ requestId, ...p }); },
			onError: (p) => { emitters.error.fire({ requestId, ...p }); },
		};
		sendLLMMessageToProviderImplementation.ollama.list(mainThreadParams);
	};

	_callVLLMList = (params: MainModelListParams<VLLMModelResponse>) => {
		const { requestId } = params;
		const emitters = this.listEmitters.vLLM;
		const mainThreadParams: ModelListParams<VLLMModelResponse> = {
			...params,
			onSuccess: (p) => { emitters.success.fire({ requestId, ...p }); },
			onError: (p) => { emitters.error.fire({ requestId, ...p }); },
		};
		sendLLMMessageToProviderImplementation.vLLM.list(mainThreadParams);
	};

	private _callAbort(params: MainLLMMessageAbortParams) {
		const { requestId } = params;
		if (!(requestId in this.abortRefOfRequestId)) return;
		this.abortRefOfRequestId[requestId].current?.();
		delete this.abortRefOfRequestId[requestId];
	}
}
