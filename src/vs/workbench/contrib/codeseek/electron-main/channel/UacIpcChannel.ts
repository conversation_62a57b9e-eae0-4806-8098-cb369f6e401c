import { CancellationToken } from '../../../../../base/common/cancellation.js';
import { Event } from '../../../../../base/common/event.js';
import { IServerChannel } from '../../../../../base/parts/ipc/common/ipc.js';
import { IRequestService } from '../../../../../platform/request/common/request.js';
import { ZteUserLoginChannelCommand } from '../../common/uac/zteUserTypes.js';
import { problemCheck } from '../uac/check.js';
import { codeServerPwdFree, loginWithPwd, pollUac, queryUserInfo, udsPwdFree } from '../uac/uac.js';


export class UacIpcChannel implements IServerChannel {
	constructor(private requestService: IRequestService) { }
	call(ctx: string, command: string, arg?: any, cancellationToken?: CancellationToken): Promise<any> {
		switch (command) {
			case ZteUserLoginChannelCommand.POOL_UAC:
				return pollUac(this.requestService, arg[0], cancellationToken);
			case ZteUserLoginChannelCommand.QUERY_USER_INFO:
				return queryUserInfo(this.requestService, arg[0], arg[1])
			case ZteUserLoginChannelCommand.UDS_PWQ_FREE:
				return udsPwdFree(this.requestService)
			case ZteUserLoginChannelCommand.LOGIN_WITH_PWD:
				return loginWithPwd(this.requestService, arg[0], arg[1], arg[2])
			case ZteUserLoginChannelCommand.CODE_SERVER_PWD_PREE:
				return codeServerPwdFree(this.requestService, arg[0])
			case ZteUserLoginChannelCommand.PROMBLES_CHECK:
				return problemCheck(arg[0], arg[1])
			default:
				throw new Error(`Unknown command: ${command}`);
		}
	}
	listen<T>(ctx: string, event: string, arg?: any): Event<T> {
		throw new Error('Method not implemented.');
	}

}
