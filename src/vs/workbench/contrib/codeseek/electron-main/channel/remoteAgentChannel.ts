/*--------------------------------------------------------------------------------------
 *  Copyright 2025 ZTE, Inc. All rights reserved.
 *  Licensed under the Apache License, Version 2.0. See LICENSE.txt for more information.
 *--------------------------------------------------------------------------------------*/
import { IServerChannel } from '../../../../../base/parts/ipc/common/ipc.js';
import { Emitter, Event } from '../../../../../base/common/event.js';
import { ICodeseekLogger } from '../../common/codeseekLogService.js';
import { IInstantiationService } from '../../../../../platform/instantiation/common/instantiation.js';
import { RemoteAgentMainService, SocketMessageOpts } from '../remoteAgentMainService.js';
import { CodeseekSettingsState } from '../../common/codeseekSettingsService.js';
import { PlanAgentMessage } from '../../browser/agent/planAgentService.js';
import { ProviderNames } from '../../common/codeseekSettingsTypes.js';
import { AbortRef, AgentEvent, EventAgentMessageOnErrorParams, EventAgentMessageOnFinalMessageParams, EventAgentMessageOnTextParams, EventAgentMessageOnToolCallParams, RemoteAgentMessageType } from '../../common/remoteAgentServiceType.js';

export type ChannelSendMessageParams = {
	agentMessage: PlanAgentMessage;
	codeseekSettingsState: CodeseekSettingsState;
	requestId: string;
	providerName: ProviderNames;
	modelName: string;
}

export class RemoteAgentChannel implements IServerChannel {

	private readonly agentMessageEmitters = {
		onText: new Emitter<EventAgentMessageOnTextParams>(),
		onToolCall: new Emitter<EventAgentMessageOnToolCallParams>(),
		onFinalMessage: new Emitter<EventAgentMessageOnFinalMessageParams>(),
		onError: new Emitter<EventAgentMessageOnErrorParams>(),
	};

	private readonly abortRefOfRequestId: Record<string, AbortRef> = {};
	private readonly serviceInstances: Record<string, RemoteAgentMainService> = {};

	constructor(
		private readonly instantiationService: IInstantiationService,
		private readonly logger: ICodeseekLogger
	) { }

	listen(_: unknown, event: string): Event<any> {
		if (event === 'onText_sendRemoteAgentMessage') return this.agentMessageEmitters.onText.event;
		else if (event === 'onFinalMessage_sendRemoteAgentMessage') return this.agentMessageEmitters.onFinalMessage.event;
		else if (event === 'onError_sendRemoteAgentMessage') return this.agentMessageEmitters.onError.event;
		else if (event === 'onToolCall_sendRemoteAgentMessage') return this.agentMessageEmitters.onToolCall.event;
		else throw new Error(`Event not found: ${event}`);
	}

	async call(_: unknown, command: string, params: any): Promise<any> {
		try {
			if (command === 'sendMessage') {
				this._callSendMessage(params);
			}
			else if (command === 'abort') {
				this._callAbort(params);
			}
			else {
				throw new Error(`Codeseek sendLLM: command "${command}" not recognized.`);
			}
		}
		catch (e) {
			this.logger.error('llmMessageChannel: Call Error:', e);
		}
	}

	private _callSendMessage(params: ChannelSendMessageParams) {
		const { requestId, agentMessage } = params;

		if (!(requestId in this.abortRefOfRequestId))
			this.abortRefOfRequestId[requestId] = { current: null };

		let socketMessageOpts: SocketMessageOpts;
		if (agentMessage.messageType === RemoteAgentMessageType.feedback) {
			socketMessageOpts = {
				...agentMessage,
				requestId,
				abortRef: this.abortRefOfRequestId[requestId],
			}
		} else {
			socketMessageOpts = {
				...agentMessage,
				codeseekSettingsState: params.codeseekSettingsState,
				providerName: params.providerName,
				modelName: params.modelName,
				requestId,
				abortRef: this.abortRefOfRequestId[requestId],
			}
		}

		const args: AgentEvent & { requestId: string } = {
			requestId,
			onText: (p) => { this.agentMessageEmitters.onText.fire({ requestId, ...p }); },
			onToolCall: (p) => { this.agentMessageEmitters.onToolCall.fire({ requestId, ...p }); },
			onFinalMessage: (p) => { this.agentMessageEmitters.onFinalMessage.fire({ requestId, ...p }); },
			onError: (p) => { this.agentMessageEmitters.onError.fire({ requestId, ...p }); },
		}

		if (!(requestId in this.serviceInstances)) {
			this.serviceInstances[requestId] = this.instantiationService.createInstance(RemoteAgentMainService, args);
			this.serviceInstances[requestId].onDispose((requestId) => {
				delete this.serviceInstances[requestId];
			});
		}

		this.serviceInstances[requestId].sendMessage(socketMessageOpts);
	}

	private _callAbort(params: { requestId: string }) {
		const { requestId } = params;
		if (requestId in this.abortRefOfRequestId) {
			this.abortRefOfRequestId[requestId].current?.();
			delete this.abortRefOfRequestId[requestId];
		}
		if (requestId in this.serviceInstances) {
			this.serviceInstances[requestId].disconnect();
			delete this.serviceInstances[requestId];
		}
	}

}
