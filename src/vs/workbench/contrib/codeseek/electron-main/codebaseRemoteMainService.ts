import { createDecorator } from '../../../../platform/instantiation/common/instantiation.js';
import { InstantiationType, registerSingleton } from '../../../../platform/instantiation/common/extensions.js';
import { Disposable } from '../../../../base/common/lifecycle.js';
import { EnsureIndexParams, searchParams, QueryContextData, CodeBaseResponse, CreateRepoData, RepoSnapshotData, QueryContextParams, EventCodebaseOnProgressParams, State, DeleteRepoIndexParams, CreateRepoParams, CreateRepoConfig, PauseRemoteIndexParams, ResumeRemoteIndexParams, UpdateIndexParams } from '../common/codebaseTypes.js';
import * as path from '../../../../base/common/path.js';
import { URI } from '../../../../base/common/uri.js';
import { IDirectoryNode, IFileNode, IProjectStructure } from '../common/codeseekFileService.js';
import * as crypto from 'crypto';
import { IFileService } from '../../../../platform/files/common/files.js';
import { ICodeseekLogger } from '../common/codeseekLogService.js';
import { ChildProcess, spawn } from 'child_process';
import { IProductService } from '../../../../platform/product/common/productService.js';
import { osType } from '../browser/helpers/systemInfo.js';
import { RepoSnapshot } from '../common/codebaseTypes.js';
import os from 'os';
import detectPort from 'detect-port';
import { execSync } from 'node:child_process';
import { AbortError } from '../../../common/pTimeout.js';
import { ILifecycleMainService } from '../../../../platform/lifecycle/electron-main/lifecycleMainService.js';
import * as fs from 'fs';
import { IEnvironmentMainService } from '../../../../platform/environment/electron-main/environmentMainService.js';

enum CodebaseUrl {
	createRepo = '/api/codebase/v1/createRepoIndex',
	queryRepoIndex = '/api/codebase/v1/queryRepoIndex',
	deleteRepoIndex = '/api/codebase/v1/deleteRepoIndex',
	queryRepoSnapshot = '/api/codebase/v1/repoSnapshot',
	createFileIndex = '/api/codebase/v1/createFileIndex',
	updateFileIndex = '/api/codebase/v1/updateFileIndex',
	deleteFileIndex = '/api/codebase/v1/deleteFileIndex',
	noticeRepoIndexCreateComplete = '/api/codebase/v1/repoIndexCreateComplete',
	queryContext = '/api/codebase/v1/queryContext',
	healthCheck = '/api/codebase/v1/heartbeat'
}

export type RemoteEnsureIndexParams = EnsureIndexParams & CreateRepoConfig & {
	onProgress: (p: EventCodebaseOnProgressParams) => void;
}

export interface ICodebaseRemoteMainService {
	readonly _serviceBrand: undefined;

	startCodebaseProcess(): Promise<boolean>;
	stopCodebaseProcess(): Promise<void>;
	isCodebaseRunning(): boolean;
	checkCodebaseHealth(): Promise<boolean>;

	ensureIndex(params: RemoteEnsureIndexParams): Promise<void>;
	getResults(params: searchParams): Promise<QueryContextData | undefined>;
	deleteRemoteIndex(params: DeleteRepoIndexParams): Promise<boolean>;
	pauseRemoteIndex(params: PauseRemoteIndexParams): Promise<boolean>;
	resumeRemoteIndex(params: ResumeRemoteIndexParams): Promise<boolean>;
	updateRemoteIndex(params: UpdateIndexParams): Promise<boolean>;
}

export const ICodebaseRemoteMainService = createDecorator<ICodebaseRemoteMainService>('codebaseRemoteMainService');
export class CodebaseRemoteMainService extends Disposable implements ICodebaseRemoteMainService {
	private baseUrl: string | undefined;
	private port: number | undefined;
	readonly _serviceBrand: undefined;
	private allState: Record<string, State> = {};
	private readonly fileContentCache: Map<string, { content: string, hashcode: string, timestamp: number }> = new Map();
	private codebaseProcess: ChildProcess | null = null;
	private keepAliveInterval: NodeJS.Timeout | null = null;
	private restartAttempts = 0;
	private isIndexPaused = false;

	private readonly CACHE_EXPIRY = 30 * 60 * 1000; // 30分钟缓存过期
	private readonly REQUEST_TIMEOUT = 60 * 1000; // 请求超时时间
	private readonly KEEP_ALIVE_CHECK_INTERVAL = 5 * 60 * 1000; // 5分钟检查一次保活
	private readonly MAX_RESTART_ATTEMPTS = 3; // 最大重启次数
	private readonly SERVICE_READY_TIMEOUT = 20 * 1000; // 增加服务就绪检查超时时间到20秒
	private readonly PORT_RANGE_START = 1323; // 端口范围
	private readonly PORT_RANGE_END = 1333;

	constructor(
		@ICodeseekLogger private readonly logger: ICodeseekLogger,
		@IFileService private readonly fileService: IFileService,
		@IProductService private readonly productService: IProductService,
		@ILifecycleMainService private readonly lifecycleMainService: ILifecycleMainService,
		@IEnvironmentMainService private readonly _envMainService: IEnvironmentMainService,
	) {
		super();
		this.initialize().catch(error => {
			this.logger.error(`初始化失败: ${error.message}`);
			throw error;
		});
		this._register({
			dispose: () => {
				this.stopCodebaseProcess().catch(err => {
					this.logger.error(`Error stopping codebase process during dispose: ${err.message}`);
				});
			}
		});
		this.lifecycleMainService.onWillShutdown(e => this.onWillShutdown());
		if (this._isDevMode()) {
			this.lifecycleMainService.onWillLoadWindow(e => this.onWillShutdown());
		}
	}

	private async initialize(): Promise<void> {
		try {
			this.port = await this.findAvailablePort();
			this.baseUrl = `http://localhost:${this.port}`;
		} catch (error) {
			throw error;
		}
	}

	private _isDevMode(): boolean {
		return !this._envMainService.isBuilt;
	}

	private async onWillShutdown(): Promise<void> {
		this.logger.info('Application is shutting down, terminating codebase process...');

		try {
			if (this.codebaseProcess && this.codebaseProcess.pid) {
				try {
					await this.killProcess(this.codebaseProcess);
					return;
				} catch (err) { }
			}
			const codebasePath = await this.getCodebasePath();
			if (codebasePath) {
				if (osType === 'windows') {
					execSync(`taskkill /F /IM "${path.basename(codebasePath)}" /T`, { stdio: 'ignore' });
				} else {
					execSync(`pkill -f "${codebasePath}"`, { stdio: 'ignore' });
				}
			}
		} catch (err) {
			this.logger.error(`Failed to terminate codebase processes: ${err}`);
		}
	}

	async getResults(params: searchParams): Promise<QueryContextData | undefined> {
		if (!(params.repoUri.fsPath in this.allState)) {
			this.logger.info(`the ${params.repoUri.fsPath} repo is not exist, skip get results`);
			return undefined;
		}

		if (this.allState[params.repoUri.fsPath].status !== 'completed') {
			this.logger.info(`the ${params.repoUri.fsPath} repo is not completed, skip get results`);
			return undefined;
		}

		let isTry = false;
		const repoId = this.allState[params.repoUri.fsPath].repoId;
		try {
			const requestData: QueryContextParams = {
				query: params.userQuery,
				repoId: repoId,
			};
			const queryContextData: QueryContextData = await this.post(CodebaseUrl.queryContext, requestData);
			return queryContextData;
		} catch (error) {
			if (error.name === 'AbortError' && !isTry) {
				isTry = true;
				await this.restartCodebaseProcess();
				return await this.getResults(params);
			}
			this.logger.error(`Failed to get results for ${params.repoUri.fsPath} repo`);
			return undefined;
		}
	}

	private async getRepoId(repoUri: URI): Promise<CreateRepoData | null> {
		const body = {
			repoPath: repoUri.fsPath,
			repoName: repoUri.path.split('/').pop()
		};
		try {
			const response = await this.post(`${CodebaseUrl.queryRepoIndex}`, body, true, false);
			if (response.code === 0 || response.code === 2) {
				return response.data as CreateRepoData;
			}
		} catch (error) {
			throw error;
		}
		return null;
	}

	private async getFileContentAndHash(uri: URI, force = false): Promise<{ content: string, hashcode: string }> {
		const cacheKey = uri.path;
		const now = Date.now();
		const cached = this.fileContentCache.get(cacheKey);

		if (cached && (now - cached.timestamp) < this.CACHE_EXPIRY && !force) {
			return { content: cached.content, hashcode: cached.hashcode };
		}

		try {
			const uri_ = URI.parse(uri.path);
			const content = await this.fileService.readFile(uri_);
			const contentStr = content.value.toString();
			const hashcode = crypto.createHash('md5').update(contentStr).digest('hex');

			this.fileContentCache.set(cacheKey, {
				content: contentStr,
				hashcode,
				timestamp: now
			});

			return { content: contentStr, hashcode };
		} catch (error) {
			this.logger.error(`Failed to read file ${uri.path}:`, error);
			throw error;
		}
	}

	private clearExpiredCache(): void {
		const now = Date.now();
		for (const [key, value] of this.fileContentCache.entries()) {
			if (now - value.timestamp > this.CACHE_EXPIRY) {
				this.fileContentCache.delete(key);
			}
		}
	}

	async ensureIndex(params: RemoteEnsureIndexParams): Promise<void> {
		if (params.repoUri.scheme !== 'file') {
			throw new Error(`Invalid URI scheme: ${params.repoUri.scheme}. Expected 'file'.`);
		}
		this.logger.info(`ensure index for ${params.repoUri.fsPath}`);
		try {
			const fileNodes = await this.collectFiles(params.repoUri, params.projectStructure!);
			if (fileNodes.length === 0) {
				this.logger.info(`the ${params.repoUri.fsPath} repo is empty`);
				return;
			}
			const totalFiles = fileNodes.length;

			const createRepoData: CreateRepoParams = {
				repoPath: params.repoUri.fsPath,
				repoName: params.repoUri.path.split('/').pop()!,
				config: {
					llm: params.llm,
				}
			};
			if (params.clangd) {
				createRepoData.config.clangd = params.clangd;
			}

			const repoData: CreateRepoData = await this.post(CodebaseUrl.createRepo, createRepoData);
			this.allState[params.repoUri.fsPath] = {
				repoId: repoData.repoId,
				repoName: createRepoData.repoName,
				repoPath: createRepoData.repoPath,
				status: 'running',
			};
			const repoSnapshotData: RepoSnapshotData = await this.get(`${CodebaseUrl.queryRepoSnapshot}/${repoData.repoId}`);
			const isNeedCreateIndex = await this.isNeedCreateIndex(params.repoUri, fileNodes, repoSnapshotData);
			if (!isNeedCreateIndex) {
				if (!Object.keys(this.allState).includes(params.repoUri.fsPath)) {
					return
				}
				this.logger.info(`the ${params.repoUri.fsPath} repo already complete`);
				params.onProgress({ repoUri: params.repoUri, progress: 1, SyncedFileNumber: totalFiles });
				this.allState[params.repoUri.fsPath] = {
					repoId: repoData.repoId,
					repoName: createRepoData.repoName,
					repoPath: createRepoData.repoPath,
					status: 'completed',
				};
				return;
			}
			if (this.allState[params.repoUri.fsPath] && this.allState[params.repoUri.fsPath].status === 'paused') {
				this.logger.info(`${params.repoUri.fsPath} repo is already paused, skip ensure index`);
				return;
			}
			this.logger.info(`${params.repoUri.fsPath} repo start create index`);
			this.isIndexPaused = false;
			let indexedFiles = repoSnapshotData.repoSnapshot.length;
			let progress = indexedFiles / totalFiles;

			while (fileNodes.length > 0) {
				if (this.allState[params.repoUri.fsPath].status === 'paused') {
					this.allState[params.repoUri.fsPath].projectStructure = params.projectStructure;
					this.allState[params.repoUri.fsPath].onProgress = params.onProgress;
					this.allState[params.repoUri.fsPath].llm = createRepoData.config.llm;
					if (createRepoData.config.clangd) {
						this.allState[params.repoUri.fsPath].clangd = createRepoData.config.clangd;
					}
					this.isIndexPaused = true;
					return;
				}
				this.clearExpiredCache();
				const fileNode = fileNodes.shift();
				if (fileNode) {
					try {
						const relativePath = path.relative(params.repoUri.path, fileNode.uri.path);
						const { content, hashcode } = await this.getFileContentAndHash(fileNode.uri);
						const indexStatus = this.getIndexStatus(relativePath, hashcode, repoSnapshotData);
						if (indexStatus == 'indexed' || !fileNode.language) {
							continue;
						}
						const fileIndexData = {
							repoId: repoData.repoId,
							relativePath,
							content,
							hashcode,
							language: fileNode.language
						};

						if (indexStatus === 'updating') {
							await this.post(CodebaseUrl.updateFileIndex, fileIndexData, false);
						} else {
							await this.post(CodebaseUrl.createFileIndex, fileIndexData, false);
						}
						indexedFiles++;
					} catch (error) {
						this.logger.error(`Failed to process file ${fileNode.uri.path}:`, error);
					}
				}
				progress = progress >= 0.99 ? 0.99 : indexedFiles / totalFiles;
				params.onProgress({ repoUri: params.repoUri, progress: progress, SyncedFileNumber: indexedFiles });
			}

			for (const repoSnapshot of repoSnapshotData.repoSnapshot) {
				if (!fileNodes.find(fileNode => path.relative(params.repoUri.path, fileNode.uri.path) === repoSnapshot.relativePath)) {
					const data = { repoId: repoData.repoId, relativePath: repoSnapshot.relativePath };
					await this.post(CodebaseUrl.deleteFileIndex, data, false);
				}
			}

			params.onProgress({ repoUri: params.repoUri, progress: 1, SyncedFileNumber: totalFiles });
			this.logger.info(`the ${params.repoUri.fsPath} repo index create complete`);
			await this.post(CodebaseUrl.noticeRepoIndexCreateComplete, { repoId: repoData.repoId });
			this.allState[params.repoUri.fsPath] = {
				repoId: repoData.repoId,
				repoName: createRepoData.repoName,
				repoPath: createRepoData.repoPath,
				status: 'completed',
			};
		} catch (error) {
			this.logger.error(`Failed to ensure index for ${params.repoUri.fsPath} repo`);
			this.allState[params.repoUri.fsPath].status = 'error';
			throw error;
		}
	}

	async deleteRemoteIndex(params: DeleteRepoIndexParams): Promise<boolean> {
		let isTry = false;
		try {
			if (!(params.repoUri.fsPath in this.allState)) {
				this.logger.info(`the ${params.repoUri.fsPath} repo is not exist, skip delete remote index`);
				return true;
			}

			if (this.allState[params.repoUri.fsPath].status === 'running') {
				this.allState[params.repoUri.fsPath].status = 'paused';
			} else {
				this.isIndexPaused = true;
			}

			while (!this.isIndexPaused) {
				await new Promise(resolve => setTimeout(resolve, 200));
			}
			const repoIndexData = await this.getRepoId(params.repoUri);
			if (!repoIndexData) {
				this.logger.error(`repo not found, skip delete remote index`);
				return true;
			}
			const repoId = repoIndexData.repoId;
			await this.post(CodebaseUrl.deleteRepoIndex, { repoId });
			delete this.allState[params.repoUri.fsPath];
			this.logger.info(`the ${params.repoUri.fsPath} repo index delete complete`);
			return true;
		} catch (error) {
			if (error.name === 'AbortError' && !isTry) {
				isTry = true;
				await this.restartCodebaseProcess();
				return await this.deleteRemoteIndex(params);
			}
			this.logger.error(`Failed to delete remote index for ${params.repoUri.fsPath} repo`);
			return false;
		}
	}

	async pauseRemoteIndex(params: PauseRemoteIndexParams): Promise<boolean> {
		try {
			if (!this.allState[params.repoUri.fsPath]) {
				this.logger.warn(`the ${params.repoUri.fsPath} repo is not exist, skip pause remote index`);
				return true;
			}
			this.logger.info(`the ${params.repoUri.fsPath} repo is paused, stop create index`);
			if (this.allState[params.repoUri.fsPath].status === 'running') {
				this.allState[params.repoUri.fsPath].status = 'paused';
			} else {
				this.isIndexPaused = true;
			}
			while (!this.isIndexPaused) {
				await new Promise(resolve => setTimeout(resolve, 200));
			}
			this.logger.info(`the ${params.repoUri.fsPath} repo index pause complete`);
			return true;
		} catch (error) {
			this.logger.error(`Failed to pause remote index for ${params.repoUri.fsPath} repo`);
			return false;
		}
	}

	async resumeRemoteIndex(params: ResumeRemoteIndexParams): Promise<boolean> {
		try {
			if (!(params.repoUri.fsPath in this.allState)) {
				this.logger.error(`Repo not found in state, cannot resume: ${params.repoUri.fsPath}`);
				return false;
			}

			if (this.allState[params.repoUri.fsPath].status !== 'paused') {
				this.logger.info(`Repo is not in paused state, current state: ${this.allState[params.repoUri.fsPath].status}`);
				return true;
			}
			this.allState[params.repoUri.fsPath].status = 'running';
			this.logger.info(`Resumed indexing for repo: ${params.repoUri.fsPath}`);

			const repoState = this.allState[params.repoUri.fsPath];
			const ensureParams: RemoteEnsureIndexParams = {
				repoUri: params.repoUri,
				onProgress: repoState.onProgress!,
				projectStructure: repoState.projectStructure!,
				llm: repoState.llm!,
			};
			if (repoState.clangd) {
				ensureParams.clangd = repoState.clangd;
			}

			this.ensureIndex(ensureParams).catch(error => {
				this.logger.error(`Failed to resume indexing for ${params.repoUri.fsPath}: ${error}`);
			});

			this.logger.info(`Restarting indexing process for repo: ${params.repoUri.fsPath}`);
			return true;
		} catch (error) {
			this.logger.error(`Failed to resume remote index for ${params.repoUri.fsPath} repo: ${error}`);
			return false;
		}
	}

	public async updateRemoteIndex(params: UpdateIndexParams): Promise<boolean> {
		if (this.allState[params.fileUri.fsPath].status !== 'completed') {
			this.logger.info(`the ${params.fileUri.fsPath} repo is not completed, skip update remote index`);
			return false;
		}
		try {
			if (params.status === 'add') {
				await this.post(CodebaseUrl.createFileIndex, params, false);
			} else if (params.status === 'delete') {
				await this.post(CodebaseUrl.deleteFileIndex, params, false);
			} else {
				await this.post(CodebaseUrl.updateFileIndex, params, false);
			}
			return true;
		} catch (error) {
			this.logger.error(`Failed to update remote index for ${params.fileUri.fsPath} repo: ${error}`);
			return false;
		}
	}

	private async collectFiles(repoUri: URI, projectStructure: IProjectStructure): Promise<IFileNode[]> {
		const fileNodes: IFileNode[] = [];
		const MAX_FILES = 100000;
		try {
			if (!projectStructure.root) {
				return fileNodes;
			}

			const stack: Array<IDirectoryNode | IFileNode> = [projectStructure.root];
			const processedPaths = new Set<string>();

			while (stack.length > 0 && fileNodes.length < MAX_FILES) {
				const currentNode = stack.pop();

				if (!currentNode) {
					continue;
				}

				const nodePath = currentNode.uri.path;
				if (processedPaths.has(nodePath)) {
					continue;
				}
				processedPaths.add(nodePath);

				if (currentNode.type === 'file') {
					fileNodes.push(currentNode as IFileNode);
				} else if (currentNode.type === 'directory') {
					const dirNode = currentNode as IDirectoryNode;
					if (dirNode.children && dirNode.children.length > 0) {
						for (let i = dirNode.children.length - 1; i >= 0; i--) {
							stack.push(dirNode.children[i]);
						}
					}
				}
			}

			if (fileNodes.length >= MAX_FILES) {
				this.logger.warn(`File collection limited to ${MAX_FILES} files to prevent memory issues`);
			}
		} catch (error) {
			this.logger.error('the error when collect files:', error);
		}
		return fileNodes;
	}

	private getIndexStatus(relativePath: string, hashcode: string, repoSnapshotData: RepoSnapshotData): 'unindexed' | 'indexed' | 'updating' {
		if (repoSnapshotData.repoSnapshot.find((snapshot: RepoSnapshot) => snapshot.relativePath === relativePath && snapshot.hashcode === hashcode)) {
			return 'indexed';
		} else if (repoSnapshotData.repoSnapshot.find((snapshot: RepoSnapshot) => snapshot.relativePath === relativePath)) {
			return 'updating';
		}
		return 'unindexed';
	}

	private async isNeedCreateIndex(repoUri: URI, fileNodes: IFileNode[], repoSnapshotData: RepoSnapshotData): Promise<boolean> {
		for (const fileNode of fileNodes) {
			const relativePath = path.relative(repoUri.path, fileNode.uri.path);
			const { hashcode } = await this.getFileContentAndHash(fileNode.uri, true);
			if (fileNode.language && this.getIndexStatus(relativePath, hashcode, repoSnapshotData) === 'unindexed') {
				return true;
			}
		}
		for (const repoSnapshot of repoSnapshotData.repoSnapshot) {
			if (fileNodes.find(fileNode => path.relative(repoUri.path, fileNode.uri.path) === repoSnapshot.relativePath)) {
				continue;
			}
			return true;
		}
		return false;
	}

	async checkCodebaseHealth(): Promise<boolean> {
		try {
			this.logger.info('Checking codebase health using heartbeat endpoint');
			const healthCheckData = await this.get(CodebaseUrl.healthCheck, false);

			if (healthCheckData === null) {
				this.logger.error('Health check failed, service is not responding');
				return false;
			}

			this.logger.info('Health check succeeded, service is healthy');
			return true;
		} catch (error) {
			this.logger.error(`Health check failed with error: ${error.message}`);
			return false;
		}
	}

	private async findAvailablePort(): Promise<number> {
		try {
			const availablePort = await detectPort(this.PORT_RANGE_START);
			if (availablePort > this.PORT_RANGE_END) {
				throw new Error(`No available ports found in range ${this.PORT_RANGE_START}-${this.PORT_RANGE_END}`);
			}
			return availablePort;
		} catch (error) {
			this.logger.error(`Error finding available port: ${error}`);
			throw error;
		}
	}

	private getDefaultShell(): string {
		if (osType === 'windows') {
			return 'cmd.exe';
		}
		if (fs.existsSync('/bin/bash')) {
			return 'bash';
		}
		return 'sh';
	}

	private isDebugMode(): boolean {
		return this.productService.dataFolderName === '.flow-dev';
	}

	async startCodebaseProcess(): Promise<boolean> {
		if (!this.port || !this.baseUrl) {
			throw new Error('Codebase process is not initialized');
		}

		const isHealthy = await this.checkCodebaseHealth();
		if (!isHealthy) {
			this.logger.warn('Codebase process is running but health check failed, restarting service');
			await this.stopCodebaseProcess();
		} else {
			return true;
		}
		try {
			const codebasePath = await this.getCodebasePath();
			this.logger.info(`codebasePath: ${codebasePath}`);

			if (!codebasePath) {
				throw new Error('Codebase executable not found');
			}

			this.logger.info(`Starting codebase process on port ${this.port}`);
			const command = this.getDefaultShell();
			const args = osType === 'windows' ?
				['/C', `start /b ${codebasePath} -p ${this.port}`] :
				['-c', `exec ${codebasePath} -p ${this.port}`];

			this.codebaseProcess = spawn(command, args, {
				stdio: ['ignore', this.isDebugMode() ? 'pipe' : 'ignore', 'pipe'],
				windowsHide: true,
				shell: false,
				detached: false
			});
			this.logger.info(`Started codebase process with PID: ${this.codebaseProcess.pid}`);

			this.codebaseProcess.stdout?.on('data', (data) => {
			});

			this.codebaseProcess.stderr?.on('data', (data) => {
				this.logger.error(`Codebase stderr: ${data.toString()}`);
			});

			this.codebaseProcess.on('exit', (code, signal) => {
				this.logger.info(`Codebase process exited with code ${code}, signal: ${signal}`);
				this.codebaseProcess = null;
			});

			this.codebaseProcess.on('error', (err) => {
				this.logger.error(`Codebase process error: ${err.message}`);
				this.codebaseProcess = null;
			});

			let retries = 0;
			const maxRetries = 10;
			const retryInterval = this.SERVICE_READY_TIMEOUT / maxRetries;

			while (retries < maxRetries) {
				this.logger.info(`Waiting for codebase service to be ready, attempt ${retries + 1}/${maxRetries}`);
				await new Promise(resolve => setTimeout(resolve, retryInterval));
				const isHealthy = await this.checkCodebaseHealth();
				if (isHealthy) {
					this.logger.info('Codebase process started successfully and service is ready');
					this.startKeepAlive();
					return true;
				}
				retries++;
			}

			this.logger.error('Codebase service failed to become ready within timeout');
			if (this.codebaseProcess) {
				await this.killProcess(this.codebaseProcess);
				this.codebaseProcess = null;
			}
			return false;
		} catch (error) {
			this.logger.error(`Failed to start codebase process: ${error.message}`);
			throw error;
		}
	}

	private async killProcess(process: ChildProcess | null): Promise<void> {
		return new Promise<void>((resolve) => {
			if (!process || !process.pid) {
				resolve();
				return;
			}
			if (osType === 'windows') {
				spawn('taskkill', ['/F', '/T', '/PID', process.pid.toString()]);
				resolve();
			} else {
				process.kill('SIGKILL');
				resolve();
			}
		});
	}

	private startKeepAlive(): void {
		this.stopKeepAlive();

		this.logger.info('Starting keep-alive mechanism for codebase process');
		this.keepAliveInterval = setInterval(async () => {
			this.logger.info('Performing keep-alive health check');
			try {
				const isHealthy = await this.checkCodebaseHealth();
				if (!isHealthy && this.codebaseProcess) {
					this.logger.warn('Keep-alive health check failed, restarting codebase service');
					await this.stopCodebaseProcess();
					await this.startCodebaseProcess();
				}
			} catch (error) {
				this.logger.error(`Keep-alive check error: ${error.message}`);
			}
		}, this.KEEP_ALIVE_CHECK_INTERVAL);
	}

	private stopKeepAlive(): void {
		if (this.keepAliveInterval) {
			this.logger.info('Stopping keep-alive mechanism');
			clearInterval(this.keepAliveInterval);
			this.keepAliveInterval = null;
		}
	}

	async stopCodebaseProcess(): Promise<void> {
		this.stopKeepAlive();

		if (!this.codebaseProcess) {
			this.logger.info('No codebase process to stop');
			return;
		}

		return new Promise<void>((resolve, reject) => {
			try {
				this.logger.info('Stopping codebase process...');
				if (!this.codebaseProcess) {
					this.logger.info('No codebase process to stop');
					return
				}

				const timeout = setTimeout(() => {
					if (this.codebaseProcess) {
						this.logger.warn('Killing codebase process forcefully');
						this.codebaseProcess.kill('SIGKILL');
					}
				}, 5000);

				this.codebaseProcess.on('exit', () => {
					clearTimeout(timeout);
					this.codebaseProcess = null;
					resolve();
				});

				this.killProcess(this.codebaseProcess);
			} catch (error) {
				this.logger.error(`Error stopping codebase process: ${error.message}`);
				if (this.codebaseProcess) {
					this.killProcess(this.codebaseProcess).catch(() => {/* ignore */ });
					this.codebaseProcess = null;
				}
				reject(error);
			}
		});
	}

	isCodebaseRunning(): boolean {
		return this.codebaseProcess !== null;
	}

	private async getCodebasePath(): Promise<string | null> {
		const dataFolderName = this.productService.dataFolderName;
		let codebasefile;
		if (osType === 'windows') {
			codebasefile = path.join(process.execPath, '..', 'tools', 'codebase', 'codebase.exe');
		} else {
			codebasefile = path.join(os.homedir(), dataFolderName, 'codebase', 'bin', 'codebase');
		}

		if (await this.fileService.exists(URI.file(codebasefile))) {
			return codebasefile;
		}

		this.logger.error(`codebasefile not found: ${codebasefile}`);
		return null;
	}

	private async post(url: string, data: any, isLog: boolean = true, isThrow: boolean = true, timeout: number = this.REQUEST_TIMEOUT): Promise<any> {
		let retries = 0;
		const maxRetries = 3;
		const retryDelay = 1000;

		while (retries < maxRetries) {
			try {
				const url_ = `${this.baseUrl}${url}`;
				if (isLog) {
					this.logger.info(`request url: ${url_}, data: ${JSON.stringify(data)}, attempt: ${retries + 1}/${maxRetries}`);
				}

				const controller = new AbortController();
				const timeoutId = setTimeout(() => controller.abort(), timeout);

				const response = await fetch(url_, {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json'
					},
					body: JSON.stringify(data),
					signal: controller.signal
				});

				clearTimeout(timeoutId);

				if (!response.ok) {
					this.logger.error(`HTTP error! status: ${response.status}`);
					throw new Error(`HTTP error! status: ${response.status}`);
				}

				const responseJson = await response.json() satisfies CodeBaseResponse;
				if (isLog) {
					this.logger.info(`response: ${JSON.stringify(responseJson)}`);
				}
				if (isThrow) {
					if (responseJson.code === 0 || responseJson.code === 2) {
						return responseJson.data;
					}
					throw new Error(responseJson.message);
				}
				return responseJson;
			} catch (error) {
				if (error.name === 'AbortError') {
					this.logger.warn(`请求超时, url: ${url}, 重试次数: ${retries + 1}/${maxRetries}`);
					if (retries === maxRetries - 1) {
						throw new AbortError(`请求超时, url: ${url}, 已重试${maxRetries}次`);
					}
					await new Promise(resolve => setTimeout(resolve, retryDelay));
					retries++;
					continue;
				}
				this.logger.error(error);
				throw error;
			}
		}
	}

	private async get(url: string, isLog: boolean = true, timeout: number = this.REQUEST_TIMEOUT): Promise<any> {
		let retries = 0;
		const maxRetries = 3;
		const retryDelay = 1000;

		while (retries < maxRetries) {
			try {
				const url_ = `${this.baseUrl}${url}`;
				if (isLog) {
					this.logger.info(`request url: ${url_}, attempt: ${retries + 1}/${maxRetries}`);
				}

				const controller = new AbortController();
				const timeoutId = setTimeout(() => controller.abort(), timeout);

				const response = await fetch(url_, {
					method: 'GET',
					headers: {
						'Content-Type': 'application/json'
					},
					signal: controller.signal
				});

				clearTimeout(timeoutId);

				if (!response.ok) {
					this.logger.error(`HTTP error! status: ${response.status}`);
					throw new Error(`HTTP error! status: ${response.status}`);
				}

				const responseJson = await response.json() satisfies CodeBaseResponse;
				if (isLog) {
					this.logger.info(`response: ${JSON.stringify(responseJson)}`);
				}
				if (responseJson.code === 0 || responseJson.code === 2) {
					return responseJson.data;
				}
				throw new Error(responseJson.message);
			} catch (error) {
				if (error.name === 'AbortError') {
					this.logger.warn(`请求超时, url: ${url}, 重试次数: ${retries + 1}/${maxRetries}`);
					if (retries === maxRetries - 1) {
						throw new AbortError(`请求超时, url: ${url}, 已重试${maxRetries}次`);
					}
					await new Promise(resolve => setTimeout(resolve, retryDelay));
					retries++;
					continue;
				}
				if (isLog) {
					this.logger.error(error);
				}
				throw error;
			}
		}
	}

	private async restartCodebaseProcess(): Promise<boolean> {
		if (this.restartAttempts >= this.MAX_RESTART_ATTEMPTS) {
			this.logger.error(`Exceeded maximum restart attempts (${this.MAX_RESTART_ATTEMPTS}), giving up`);
			this.stopKeepAlive();
			return false;
		}

		this.restartAttempts++;
		this.logger.info(`Attempting to restart codebase process (attempt ${this.restartAttempts}/${this.MAX_RESTART_ATTEMPTS})`);

		await new Promise(resolve => setTimeout(resolve, 1000));
		try {
			await this.stopCodebaseProcess();
			const success = await this.startCodebaseProcess();
			if (success) {
				this.logger.info(`Successfully restarted codebase process on port ${this.port}`);
			} else {
				this.logger.error('Failed to restart codebase process');
			}
			return success;
		} catch (error) {
			this.logger.error(`Error restarting codebase process: ${error.message}`);
			return false;
		}
	}
}

registerSingleton(ICodebaseRemoteMainService, CodebaseRemoteMainService, InstantiationType.Eager);

