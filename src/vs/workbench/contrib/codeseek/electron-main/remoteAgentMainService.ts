import { Disposable } from '../../../../base/common/lifecycle.js';
import { AbortRef, AgentEvent, OnError, OnFinalMessage, OnText, OnToolCall } from '../common/remoteAgentServiceType.js';
import { ICodeseekLogger } from '../common/codeseekLogService.js';
import { CodeseekSettingsState } from '../common/codeseekSettingsService.js';
import { ProviderNames } from '../common/codeseekSettingsTypes.js';
import { env, IdeTestConfig } from './utils/common.js';
import { io, Socket } from 'socket.io-client';
import os from "os";
import osName from "os-name";
import { getShell } from './utils/shell.js';
import { Emitter, Event } from '../../../../base/common/event.js';
import { AgentParamsFromIde, EnvironmentDetails, SystemInformation } from '../browser/pluginTaskService.js';
import { Feedback, PlanAgentTopic, RemoteAgentMessageType } from '../common/remoteAgentServiceType.js';

export enum SocketEvent {
	connect = 'connect',
	oh_action = 'oh_action',
	oh_event = 'oh_event',
}

export const endToken = '<end/>' as const;

export type ModelProvider = {
	name: string;
	model: string;
	api_key: string;
	base_url: string;
};

export type IdeData = AgentParamsFromIde & {
	provider: {
		name: string;
		base_url: string;
		model: string;
		api_key: string;
	};
};

export type SendTaskMessage = {
	task_id: string;
	content: string;
	data: {
		task: Record<string, any>;
		ide: IdeData;
	};
};

export type SendFeedbackMessage = {
	code: string;
	msg: string;
	task_id: string;
	topic: any;
	content: string;
	result: {
		task_result: boolean;
		task_summary: string;
	};
};

export type ReceivedMessage = {
	task_id: string;
	topic: any;
	content: string;
	tool?: {
		name: string;
		params: {
			task: string;
			task_context: string;
			task_plan: {
				content: string;
				detail: string;
			}[];
			dependencies: Map<number, number[]>;
		};
	};
};

export type PlanMessageOpts = {
	messageType: RemoteAgentMessageType.createPlan;
	messages: string;
	codeseekSettingsState: CodeseekSettingsState;
	providerName: ProviderNames;
	modelName: string;

	requestId: string;
	abortRef: AbortRef;
	agentParamsFromPlugin?: { [key: string]: any };
	agentParamsFromIde?: AgentParamsFromIde;
};

export type FeedbackMessageOpts = {
	messageType: RemoteAgentMessageType.feedback;
	feedback: Feedback;

	requestId: string;
	abortRef: AbortRef;
}

export type SocketMessageOpts = PlanMessageOpts | FeedbackMessageOpts;

export interface IRemoteAgentMainService {
	readonly _serviceBrand: undefined;

	sendMessage(message: SocketMessageOpts): Promise<void>;
}


export class RemoteAgentMainService extends Disposable implements IRemoteAgentMainService {
	_serviceBrand: undefined;
	private uri: string = 'https://10.57.148.56:28001';
	private socket: Socket | null = null;
	private fullText: string = '';

	private eventQueue: Array<ReceivedMessage> = [];
	private isProcessing = false;

	private onText: OnText;
	private onToolCall: OnToolCall;
	private onFinalMessage: OnFinalMessage;
	private onError: OnError;
	private _didAbort = false;

	private _onDispose = new Emitter<string>();
	readonly onDispose: Event<string> = this._onDispose.event;

	constructor(
		private args: AgentEvent & { requestId: string },
		@ICodeseekLogger private readonly logger: ICodeseekLogger,
	) {
		super();
		if (env.AI_IDE_TEST_CONFIG) {
			const config: IdeTestConfig = JSON.parse(env.AI_IDE_TEST_CONFIG);
			if (config.agentSocketUri) {
				this.uri = config.agentSocketUri;
			}
		}
		this.onText = (params) => {
			if (this._didAbort) return;
			this.args.onText(params);
		};

		this.onToolCall = (params) => {
			if (this._didAbort) return;
			this.args.onToolCall(params);
		};

		this.onFinalMessage = (params) => {
			if (this._didAbort) return;
			this.args.onFinalMessage(params);
		};

		this.onError = ({ message: error, fullError }) => {
			if (this._didAbort) return;
			this.args.onError({ message: error, fullError });
		};

		this.connect();
	}

	private connect() {
		try {
			if (!this.uri) {
				this.logger.error('Agent socket uri is not set');
				this.args.onError({ message: 'Agent socket uri is not set', fullError: null });
				return;
			}
			this.logger.info(`Connecting to agent socket, uri: ${this.uri}`);
			this.socket = io(this.uri, {
				transports: ["websocket"],
				query: {
					task_id: this.args.requestId,
				},
				path: '/api/code-agent/v1/socket.io',
				reconnection: true,
				reconnectionAttempts: 3,
				reconnectionDelay: 1000,
				reconnectionDelayMax: 5000,
				timeout: 2000,
				rejectUnauthorized: false
			});
		} catch (error) {
			this.logger.error('Error connecting to agent socket', error);
			this.args.onError({ message: error.message, fullError: error });
			throw error;
		}
	}

	async sendMessage(messageOpts: SocketMessageOpts): Promise<void> {
		if (!this.socket) return;
		let _aborter: (() => void) | null = null;
		const _setAborter = (fn: () => void) => { _aborter = fn; };

		const onAbort = () => {
			try { _aborter?.(); }
			catch (e) { }
			this._didAbort = true;
		};
		messageOpts.abortRef.current = onAbort;

		// 设置中止函数
		_setAborter(() => {
			if (this.socket) {
				this.disconnect();
			}
		});

		let message: SendTaskMessage | SendFeedbackMessage;
		if (messageOpts.messageType === 'feedback') {
			message = this.provideFeedbackMessage(messageOpts);
		} else {
			const modelProvider: ModelProvider = {
				name: messageOpts.providerName,
				base_url: messageOpts.codeseekSettingsState.settingsOfProvider[messageOpts.providerName].baseURL,
				model: messageOpts.modelName,
				api_key: messageOpts.codeseekSettingsState.settingsOfProvider[messageOpts.providerName].apiKey ?? ''
			};
			message = this.provideReportMessage(messageOpts.messages, messageOpts.agentParamsFromIde!, modelProvider, messageOpts.agentParamsFromPlugin);
		}

		this.socket.on(SocketEvent.connect, () => {
			this.logger.info(`Connected to server, socket id: ${this.socket?.id}, task id: ${this.args.requestId}`);
			this.logger.info(`Sending report message: ${JSON.stringify(message)}`);
			this.socket?.emit(SocketEvent.oh_action, message);
			this.socket?.on(SocketEvent.oh_event, (data: any) => {
				try {
					const data_ = data satisfies ReceivedMessage;
					this.logger.info(`Received event: ${JSON.stringify(data_)}`);
					if (data_.task_id === this.args.requestId) {
						this.eventQueue.push(data_);

						if (!this.isProcessing) {
							this.processNextEvent();
						}
					}
				} catch (error) {
					this.logger.error(`Error processing event: ${JSON.stringify(error)}`);
					this.onError?.({ message: error.message, fullError: error });
				}
			});
		});

	}

	private provideReportMessage(
		messages: string, agentParamsFromIde: AgentParamsFromIde,
		modelProvider: ModelProvider, agentParamsFromPlugin: any
	): SendTaskMessage {
		if (!agentParamsFromIde.system_information.default_shell) {
			agentParamsFromIde.system_information.default_shell = getShell();
		}
		const system_information: SystemInformation = {
			"operating_system": osName(),
			"home_directory": os.homedir(),
			...agentParamsFromIde?.system_information,
		};

		const environment_details: EnvironmentDetails = {
			...agentParamsFromIde?.environment_details,
		};

		const ideData: IdeData = {
			...agentParamsFromIde!,
			provider: modelProvider,
			system_information: system_information,
			environment_details: environment_details,
		};

		return {
			task_id: this.args.requestId,
			content: messages,
			data: {
				task: agentParamsFromPlugin ?? {},
				ide: ideData
			},
		};
	}

	private processNextEvent() {
		if (this.isProcessing || this.eventQueue.length === 0) return;

		this.isProcessing = true;
		const data: ReceivedMessage = this.eventQueue.shift()!;

		if (data.content === endToken) {
			this.onFinalMessage?.({ fullText: this.fullText });
			if (this.socket && this.socket.connected) {
				this.socket.disconnect();
			}
			this.isProcessing = false;
			this.eventQueue.length = 0;
			return;
		}

		if (data.content && data.content !== endToken) {
			this.handleText(data.content);
			if (!data.tool || !data.tool.name) {
				this.isProcessing = false;
				this.processNextEvent();
			}
		}
		if (data.tool && data.tool.name) {
			this.handleToolCall(data);
		}
		this.isProcessing = false;
		this.processNextEvent();
	}

	private handleText(text: string) {
		if (this.fullText) {
			this.fullText += '\n\n';
		}

		let processedLength = 0;
		const streamInterval = 10;
		const chunkSize = 3;

		while (processedLength < text.length) {
			const endIndex = Math.min(processedLength + chunkSize, text.length);
			const newChunk = text.substring(processedLength, endIndex);
			processedLength = endIndex;
			this.fullText += newChunk;

			this.onText?.({
				newText: newChunk,
				fullText: this.fullText,
				newReasoning: '',
				fullReasoning: '',
			});
			setTimeout(() => { }, streamInterval);
		}
	}

	private handleToolCall(data: ReceivedMessage) {
		if (!data.tool || !data.tool.name) return;
		this.onToolCall({
			fullText: this.fullText,
			plan: {
				params: {
					task: data.tool.params.task,
					taskContext: data.tool.params.task_context,
					taskPlan: data.tool.params.task_plan,
					dependencies: data.tool.params.dependencies,
				},
				id: data.task_id
			}
		});
		this.fullText = '';
	}

	public disconnect() {
		if (this.socket && this.socket.connected) {
			this.logger.info(`Disconnecting from server, socket id: ${this.socket?.id}, task id: ${this.args.requestId}`);
			this.socket.disconnect();
			this.socket = null;
			this.eventQueue = [];
			this.isProcessing = false;
			this.fullText = '';
		}
		this._onDispose.fire(this.args.requestId);
	}

	private provideFeedbackMessage(messageOpts: FeedbackMessageOpts): SendFeedbackMessage {
		return {
			code: messageOpts.feedback.taskResult ? "0" : "1",
			msg: '',
			task_id: messageOpts.feedback.taskId,
			topic: PlanAgentTopic,
			content: '',
			result: {
				task_result: messageOpts.feedback.taskResult,
				task_summary: messageOpts.feedback.taskSummary,
			},
		};
	}
}
