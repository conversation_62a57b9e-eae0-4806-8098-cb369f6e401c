import { Disposable } from '../../../../base/common/lifecycle.js';
import { createDecorator } from '../../../../platform/instantiation/common/instantiation.js';
import { ICodeseekLogger } from '../common/codeseekLogService.js';

export interface IRestfulApiMainService {
	readonly _serviceBrand: undefined;
	getAllModels(): Promise<any[]>;
}

export const IRestfulApiMainService = createDecorator<IRestfulApiMainService>('restfulApiMainService');

export class RestfulApiMainService extends Disposable implements IRestfulApiMainService {
	_serviceBrand: undefined;

	constructor(
		@ICodeseekLogger private readonly _logger: ICodeseekLogger,
	) {
		super();
	}

	async getAllModels(): Promise<any[]> {
		try {
			process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';
			const headers = {
				"accept": "application/json",
				"Content-Type": "application/json"
			};
			this._logger.info(`[RestfulApiMainService] headers: ${JSON.stringify(headers)}`);
			const response = await fetch('https://wxran.zte.com.cn/ide-model-proxy/admin/configs?status=active&page=1&limit=1000', {
				method: 'GET',
				headers: headers,
			})
			const data = await response.json();
			this._logger.info(`[RestfulApiMainService] response: ${JSON.stringify(data.data)}`);
			return data.data;
		} catch (error) {
			this._logger.error(`[RestfulApiMainService] Error capturing metrics event: ${error}`);
			return [];
		}
	}
}
