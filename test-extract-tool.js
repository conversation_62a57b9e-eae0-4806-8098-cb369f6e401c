// 简单的测试脚本来验证 extractTool 函数
const { ToolNameEnum } = {
    ToolNameEnum: {
        READ_FILE: 'read_file',
        LIST_FILES: 'list_files',
        PATHNAME_SEARCH: 'pathname_search',
        SEARCH: 'search',
        CREATE_FILE: 'create_file',
        UPDATE_TO_FILE: 'update_to_file',
        APPROVE_REQUEST: 'approve_request',
        ASK_FOLLOWUP_QUESTION: 'ask_followup_question',
        CTAGS_QUERY: 'ctags_query',
        CLANGD_QUERY: 'clangd_query',
        SHOW_SUMMARY: 'show_summary',
        CODEBASE_SEARCH: 'codebase_search',
    }
};

// 复制 extractTool 函数的实现
const extractTool = (text) => {
    const toolCalls = [];
    
    // 创建所有工具名称的正则表达式模式
    const toolNames = Object.values(ToolNameEnum);
    const toolNamesPattern = toolNames.join('|');
    
    // 匹配工具调用的正则表达式：<tool_name>...</tool_name>
    const toolCallRegex = new RegExp(`<(${toolNamesPattern})>([\\s\\S]*?)<\\/\\1>`, 'gi');
    
    let match;
    while ((match = toolCallRegex.exec(text)) !== null) {
        const toolName = match[1];
        const toolContent = match[2];
        
        // 提取参数
        const params = extractParameters(toolContent);
        
        // 根据工具名称映射参数到正确的结构
        const mappedParams = mapToolParameters(toolName, params);
        
        toolCalls.push({
            name: toolName,
            params: mappedParams,
            id: `${toolName}_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        });
    }
    
    return toolCalls;
};

// 提取XML标签内的参数
const extractParameters = (content) => {
    const params = {};
    
    // 匹配参数标签：<param_name>value</param_name>
    const paramRegex = /<([^>]+)>([\s\S]*?)<\/\1>/g;
    
    let match;
    while ((match = paramRegex.exec(content)) !== null) {
        const paramName = match[1].trim();
        const paramValue = match[2].trim();
        params[paramName] = paramValue;
    }
    
    return params;
};

// 根据工具名称映射参数到正确的ToolCallParamsType结构
const mapToolParameters = (toolName, params) => {
    switch (toolName) {
        case ToolNameEnum.READ_FILE:
            return { path: params.uri || params.path || '' };
            
        case ToolNameEnum.LIST_FILES:
            return { path: params.uri || params.path || '' };
            
        case ToolNameEnum.PATHNAME_SEARCH:
            return { query: params.query || '' };
            
        case ToolNameEnum.SEARCH:
            return { query: params.query || '' };
            
        case ToolNameEnum.CREATE_FILE:
            return { path: params.uri || params.path || '' };
            
        case ToolNameEnum.UPDATE_TO_FILE:
            return {
                path: params.uri || params.path || '',
                content: params.content || '',
                start: Number(params.start) || 0,
                end: Number(params.end) || 0
            };
            
        case ToolNameEnum.APPROVE_REQUEST:
            return {
                content: params.content || '',
                command: params.command || ''
            };
            
        case ToolNameEnum.ASK_FOLLOWUP_QUESTION:
            return { question: params.question || '' };
            
        case ToolNameEnum.CTAGS_QUERY:
            return { symbol: params.symbol || '' };
            
        case ToolNameEnum.CLANGD_QUERY:
            return {
                filePath: params.filePath || '',
                line: Number(params.line) || 0,
                character: Number(params.character) || 0
            };
            
        case ToolNameEnum.SHOW_SUMMARY:
            return {
                summary: params.summary || '',
                detail: params.detail || ''
            };
            
        case ToolNameEnum.CODEBASE_SEARCH:
            return { query: params.query || '' };
            
        default:
            // 对于未知工具，返回原始参数
            return params;
    }
};

// 测试用例
console.log('=== 测试 extractTool 函数 ===\n');

// 测试1: 单个工具调用
const test1 = `
Some text before
<read_file>
<uri>src/main.js</uri>
</read_file>
Some text after
`;

console.log('测试1 - 单个工具调用:');
const result1 = extractTool(test1);
console.log('结果:', JSON.stringify(result1, null, 2));
console.log('预期: 1个工具调用，名称为read_file，参数为{path: "src/main.js"}\n');

// 测试2: 多个工具调用
const test2 = `
<read_file>
<uri>file1.js</uri>
</read_file>

<search>
<query>function test</query>
</search>
`;

console.log('测试2 - 多个工具调用:');
const result2 = extractTool(test2);
console.log('结果:', JSON.stringify(result2, null, 2));
console.log('预期: 2个工具调用\n');

// 测试3: 复杂参数
const test3 = `
<update_to_file>
<uri>test.js</uri>
<content>console.log('hello');</content>
<start>1</start>
<end>10</end>
</update_to_file>
`;

console.log('测试3 - 复杂参数:');
const result3 = extractTool(test3);
console.log('结果:', JSON.stringify(result3, null, 2));
console.log('预期: 1个工具调用，包含多个参数\n');

// 测试4: 无工具调用
const test4 = `
Just some regular text without any tool calls.
<not_a_tool>
<param>value</param>
</not_a_tool>
`;

console.log('测试4 - 无工具调用:');
const result4 = extractTool(test4);
console.log('结果:', JSON.stringify(result4, null, 2));
console.log('预期: 空数组\n');

// 测试5: 大小写不敏感
const test5 = `
<READ_FILE>
<uri>test.js</uri>
</READ_FILE>
`;

console.log('测试5 - 大小写不敏感:');
const result5 = extractTool(test5);
console.log('结果:', JSON.stringify(result5, null, 2));
console.log('预期: 1个工具调用\n');

console.log('=== 测试完成 ===');
